<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/logo.svg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>书链通ERP</title>
  <script>
    // 脚本必须使用 ES5 语法，确保在最古老的浏览器上也能运行
    (function () {
      // 检测 Array.prototype.at 是否存在。
      // 这个特性在 Safari 15.4 中被引入，是您配置中最关键的“分界线”。
      var isBrowserSupported = typeof Array.prototype.at === 'function';

      if (isBrowserSupported) {
        // 如果特性检测不通过，立即重定向到提示页面
        window.location.replace('/unsupported-browser.html');
      }
    })();
  </script>
</head>

<body>
  <div id="root"></div>
  <script type="module" src="/src/main.tsx"></script>
</body>

</html>