diff --git a/es/List.js b/es/List.js
index 2926056a10b1c9e87f47c290417dd1d4c1eb2076..67329808b32f73584ea709f027ad37c858810908 100644
--- a/es/List.js
+++ b/es/List.js
@@ -52,6 +52,7 @@ export function RawList(props, ref) {
     innerProps = props.innerProps,
     extraRender = props.extraRender,
     styles = props.styles,
+    scrollbarSize=props.scrollbarSize,
     _props$showScrollBar = props.showScrollBar,
     showScrollBar = _props$showScrollBar === void 0 ? 'optional' : _props$showScrollBar,
     restProps = _objectWithoutProperties(props, _excluded);
@@ -493,7 +494,12 @@ export function RawList(props, ref) {
       position: 'relative'
     }),
     className: mergedClassName
-  }, containerProps, restProps), /*#__PURE__*/React.createElement(ResizeObserver, {
+  }, containerProps, restProps),
+  /*#__PURE__*/React.createElement("div", {
+    style: _extends({}, componentStyle),
+    className: "".concat(prefixCls, "-scroll-container")
+  },
+  /*#__PURE__*/React.createElement(ResizeObserver, {
     onResize: onHolderResize
   }, /*#__PURE__*/React.createElement(Component, {
     className: "".concat(prefixCls, "-holder"),
@@ -512,7 +518,7 @@ export function RawList(props, ref) {
     innerProps: innerProps,
     rtl: isRTL,
     extra: extraContent
-  }, listChildren))), inVirtual && scrollHeight > height && /*#__PURE__*/React.createElement(ScrollBar, {
+  }, listChildren))), /*#__PURE__*/React.createElement("div", {className: "".concat(prefixCls, "-extra-height")})), inVirtual && scrollHeight > height && /*#__PURE__*/React.createElement(ScrollBar, {
     ref: verticalScrollBarRef,
     prefixCls: prefixCls,
     scrollOffset: offsetTop,
@@ -537,6 +543,7 @@ export function RawList(props, ref) {
     onStopMove: onScrollbarStopMove,
     spinSize: horizontalScrollBarSpinSize,
     containerSize: size.width,
+    scrollbarSize:scrollbarSize,
     horizontal: true,
     style: styles === null || styles === void 0 ? void 0 : styles.horizontalScrollBar,
     thumbStyle: styles === null || styles === void 0 ? void 0 : styles.horizontalScrollBarThumb,
diff --git a/es/ScrollBar.js b/es/ScrollBar.js
index 5cc0e5faf2266030bf11258aff19c7d50e6d808e..8b5ed328d42971efc9b8c8adbf6b0948350bc81b 100644
--- a/es/ScrollBar.js
+++ b/es/ScrollBar.js
@@ -18,6 +18,7 @@ var ScrollBar = /*#__PURE__*/React.forwardRef(function (props, ref) {
     containerSize = props.containerSize,
     style = props.style,
     propsThumbStyle = props.thumbStyle,
+    scrollbarSize = props.scrollbarSize,
     showScrollBar = props.showScrollBar;
   var _React$useState = React.useState(false),
     _React$useState2 = _slicedToArray(_React$useState, 2),
@@ -207,7 +208,7 @@ var ScrollBar = /*#__PURE__*/React.forwardRef(function (props, ref) {
     Object.assign(containerStyle, {
       height: 8,
       left: 0,
-      right: 0,
+      right: typeof scrollbarSize === 'number' ? scrollbarSize : 0,
       bottom: 0
     });
     Object.assign(thumbStyle, _defineProperty({
diff --git a/es/hooks/useChildren.js b/es/hooks/useChildren.js
index 1d405e44e16e2673d7de0f0c18fd38465cc61500..a1290325ca19e61ef6c5b376350b280fa68d85c7 100644
--- a/es/hooks/useChildren.js
+++ b/es/hooks/useChildren.js
@@ -2,12 +2,17 @@ import * as React from 'react';
 import { Item } from "../Item";
 export default function useChildren(list, startIndex, endIndex, scrollWidth, offsetX, setNodeRef, renderFunc, _ref) {
   var getKey = _ref.getKey;
+
+  var style = React.useMemo(function () {
+    return {
+      width: scrollWidth
+    };
+  }, [scrollWidth]);
+
   return list.slice(startIndex, endIndex + 1).map(function (item, index) {
     var eleIndex = startIndex + index;
     var node = renderFunc(item, eleIndex, {
-      style: {
-        width: scrollWidth
-      },
+      style: style,
       offsetX: offsetX
     });
     var key = getKey(item);
