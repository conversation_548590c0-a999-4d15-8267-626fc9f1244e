import { useCallback, useState, type MutableRefObject } from 'react';
import {
  Flex,
  Form,
  Input,
  Modal,
  msgSuccess,
  Radio,
  Typography,
} from '@slt/base-ui';
import { useMutation } from '@slt/net-query';
import type { CommonRecord } from '@slt/base-utils';
import type { BizTableRef } from '@slt/biz-ui';
import type { YTRequest } from '@slt/net';
import { useStatementDetailContextContext } from '../../utils/PageContext';

export enum OrderRangeTypeEnum {
  '已选择' = 1,
  '根据查询条件' = 2,
}

type EditSystemRemarkFormValues = {
  /** 操作类型 */
  operateType: OrderRangeTypeEnum;
  /** 备注方式 */
  appendFlag: boolean;
  /** 备注内容 */
  remark?: string;
};

type EditBillRemarkProps<T extends CommonRecord, P extends CommonRecord> = {
  title: string;
  /** 获取额外查询条件 */
  getExtraParams?: (operateType: OrderRangeTypeEnum) => CommonRecord;
  tableRef: MutableRefObject<BizTableRef<T> | null>;
  cb?: () => void;
  request: (data: P) => YTRequest<boolean>;
  icon?: React.ReactNode;
  triggerTextClassName?: string;
};

/**批量修改备注 */
export const EditRemark = <T extends CommonRecord, P extends CommonRecord>(
  props: EditBillRemarkProps<T, P>,
) => {
  const {
    icon,
    triggerTextClassName,
    title,
    getExtraParams,
    tableRef,
    request,
    cb,
  } = props;
  const [openModal, setOpenModal] = useState(false);
  const [selectedNum, setSelectedNum] = useState(0);

  const { handleCheckRemove } = useStatementDetailContextContext();
  const { mutate: batchModifyRemarkAsync, isMutating } = useMutation(request);

  const [form] = Form.useForm<EditSystemRemarkFormValues>();
  const appendFlag = Form.useWatch('appendFlag', form);

  const handleBeforeTrigger = async () => {
    const isDelete = await handleCheckRemove();
    if (isDelete) {
      return false;
    }

    const { selectedRowKeys = [] } = tableRef?.current?.getSelectedRows() || {};
    if (!selectedRowKeys.length) {
      form.setFieldValue('operateType', OrderRangeTypeEnum.根据查询条件);
    }

    setSelectedNum(selectedRowKeys.length);
    return true;
  };

  const handleCancel = () => {
    setOpenModal(false);
    form.resetFields();
    setSelectedNum(0);
  };

  const handleSubmit = async (params: P) => {
    const res = await batchModifyRemarkAsync(params);
    if (res.success) {
      msgSuccess('修改成功');
      handleCancel();
      cb?.();
    }
  };

  const handleOk = () => {
    void form.validateFields().then((values) => {
      const { operateType, appendFlag, remark } = values;

      const params: CommonRecord = {
        ...values,
        ...getExtraParams?.(operateType),
      };

      if (!appendFlag && !remark) {
        Modal.confirm({
          title: '提示',
          content:
            '您选择覆盖备注并且未输入备注内容，将会清空订单已有单据备注，是否确认清空？',
          onOk: () => {
            void handleSubmit(params as P);
            tableRef.current?.reload();
          },
        });
      } else {
        void handleSubmit(params as P);
        tableRef.current?.reload();
      }
    });
  };

  return (
    <Modal
      type="edit"
      size="s"
      open={openModal}
      title={title}
      confirmLoading={isMutating}
      destroyOnClose
      onCancel={handleCancel}
      onOk={handleOk}
      trigger={
        <Flex>
          {icon}
          <Typography.Link
            onClick={() => setOpenModal(true)}
            className={triggerTextClassName}
          >
            {title}
          </Typography.Link>
        </Flex>
      }
      onBeforeTrigger={handleBeforeTrigger}
    >
      <Form
        enableUnsavedWarning={false}
        name="editSystemRemark"
        form={form}
        initialValues={{
          operateType: OrderRangeTypeEnum.已选择,
          appendFlag: true,
        }}
      >
        <Form.Item
          label="订单范围"
          name="operateType"
          rules={[{ required: true, message: '请选择订单范围' }]}
          className="mb-sm"
        >
          <Radio.Group>
            <Radio value={OrderRangeTypeEnum.已选择} disabled={!selectedNum}>
              选中的订单（
              <span className="text-error">{selectedNum}</span>）
            </Radio>
            <Radio value={OrderRangeTypeEnum.根据查询条件}>
              符合当前查询条件的订单
            </Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item
          label="备注内容"
          name="appendFlag"
          rules={[{ required: true, message: '请选择' }]}
          style={{ marginBottom: 4 }}
        >
          <Radio.Group>
            <Radio value>追加</Radio>
            <Radio value={false}>覆盖</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item
          label=""
          name="remark"
          rules={[{ required: !!appendFlag, message: '请输入备注内容' }]}
        >
          <Input.TextArea maxLength={300} placeholder="请输入备注内容" />
        </Form.Item>
      </Form>
    </Modal>
  );
};
