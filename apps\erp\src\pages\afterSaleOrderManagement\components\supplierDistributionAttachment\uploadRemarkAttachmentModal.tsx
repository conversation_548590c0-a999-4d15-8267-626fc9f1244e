/***
 * @name 上传供分、附件备注弹窗
 */

import {
  AttachmentUpload,
  Flex,
  Form,
  Input,
  Modal,
  Typography,
  InfoCircleOutlined,
  Tooltip,
  msgSuccess,
} from '@slt/base-ui';
import { useMutation } from '@slt/net-query';
import {
  omsAfterSaleRemarkAndAttachmentDetail,
  omsUpdateAfterSaleRemarkAndAttachment,
  type AfterSaleAttachmentDetailGroupVO,
  AfterSaleViewModeEnum,
  type UpdateAfterSaleRemarkAndAttachmentParam,
} from '@slt/services';
import { useCallback, useEffect, useState } from 'react';
import type {
  RemarkAndAttachmentProps,
  AfterSaleAttachmentDetail,
  UploadRemarkAndAttachmentFormProps,
} from './type';
import { UploadStatus, type UploadFile } from '@slt/upload-core';
import { transformStringToFileObj } from '@slt/utils';
export const UploadRemarkAttachmentModal = (
  props: RemarkAndAttachmentProps & {
    open: boolean;
    handleClose: (reload?: boolean) => void;
  },
) => {
  const {
    handleClose,
    open,
    viewMode,
    onOk,
    value,
    afterSaleOrderId,
    hasAuth,
  } = props;
  const [form] = Form.useForm<UploadRemarkAndAttachmentFormProps>();
  const [info, setInfo] = useState<AfterSaleAttachmentDetail | null>(null);
  const { mutate: getDetail, isMutating: isGetDetailLoading } = useMutation(
    omsAfterSaleRemarkAndAttachmentDetail,
  );
  const {
    mutate: updateAfterSaleRemarkAndAttachment,
    isMutating: updateLoading,
  } = useMutation(omsUpdateAfterSaleRemarkAndAttachment);

  const dealResponseData = useCallback(
    (data?: AfterSaleAttachmentDetailGroupVO) => {
      const afterSaleAttachmentList =
        data?.afterSaleAttachment?.attachmentUrlList;
      const dealData = {
        afterSaleAttachment: {
          ...data?.afterSaleAttachment,
          attachmentUrlList: transformStringToFileObj(
            afterSaleAttachmentList?.length
              ? afterSaleAttachmentList?.join(',')
              : '',
          ),
        },
        supplierDistributionAttachment:
          data?.supplierDistributionAttachment?.map((el) => {
            const supplierDistributionAttachmentList = el?.attachmentUrlList;
            return {
              ...el,
              attachmentUrlList: supplierDistributionAttachmentList?.length
                ? transformStringToFileObj(
                    supplierDistributionAttachmentList.join(','),
                  )
                : undefined,
            };
          }),
      };
      setInfo(dealData as unknown as AfterSaleAttachmentDetail);
      form?.setFieldsValue({
        remark: dealData?.afterSaleAttachment?.remark || '',
        attachmentList: afterSaleAttachmentList?.length
          ? transformStringToFileObj(afterSaleAttachmentList?.join(','))
          : undefined,
      });
    },
    [form],
  );

  const getDetailModalDetail = useCallback(async () => {
    const res = await getDetail({
      afterSaleOrderIdList: afterSaleOrderId ? [afterSaleOrderId] : undefined,
      attachmentRemarkType: viewMode,
    });
    if (res.success) {
      dealResponseData(res.data);
    }
  }, [afterSaleOrderId, getDetail, dealResponseData, viewMode]);

  const submitRemarkAndAttachment = useCallback(() => {
    void form?.validateFields().then(async (res) => {
      let params: UpdateAfterSaleRemarkAndAttachmentParam = {
        afterSaleOrderIdList: afterSaleOrderId ? [afterSaleOrderId] : undefined,
        attachmentRemarkType: viewMode,
      };
      if (viewMode === AfterSaleViewModeEnum.我的视角) {
        params = {
          ...params,
          attachmentList: res?.attachmentList
            ?.map((el) => el.url || '')
            .filter(Boolean),
          remark: res?.remark,
        };
      }
      if (viewMode === AfterSaleViewModeEnum.供分视角) {
        params = {
          ...params,
          supplierDistributionRemark: res?.remark,
          supplierDistributionAttachmentList: res?.attachmentList
            ?.map((el) => el.url || '')
            .filter(Boolean),
        };
      }
      if (afterSaleOrderId && hasAuth) {
        const response = await updateAfterSaleRemarkAndAttachment(params);
        if (response.success) {
          msgSuccess('操作成功');
          handleClose(true);
        }
      } else {
        onOk?.(params);
        handleClose();
      }
    });
  }, [
    afterSaleOrderId,
    onOk,
    handleClose,
    updateAfterSaleRemarkAndAttachment,
    viewMode,
    form,
    hasAuth,
  ]);

  useEffect(() => {
    if (afterSaleOrderId) {
      void getDetailModalDetail();
    }
    if (!afterSaleOrderId && value) {
      dealResponseData(value);
    }
  }, [value]);

  return (
    <Modal
      title={
        viewMode === AfterSaleViewModeEnum.供分视角 ? (
          <Flex align="center">
            供分销附件/备注
            <Typography.Text className="text-description">
              （信息实时同步给所有供分销商）
            </Typography.Text>
            <Tooltip title="针对分销商-中盘商-供应商情况，供应商侧能看见分销商的备注信息，但将以中盘商的名义显示；分销商侧看供应商的备注信息也以中盘商的明细显示。">
              <InfoCircleOutlined className="ml-xxs size-sm" />
            </Tooltip>
          </Flex>
        ) : (
          '售后附件备注'
        )
      }
      type="edit"
      size="m"
      destroyOnClose
      open={open}
      okText="保存"
      okButtonProps={{
        style: {
          display: !hasAuth ? 'none' : '',
        },
      }}
      confirmLoading={updateLoading || isGetDetailLoading}
      onOk={submitRemarkAndAttachment}
      onCancel={() => handleClose()}
    >
      <Flex vertical>
        <Form
          enableUnsavedWarning={false}
          form={form}
          autoComplete="off"
          labelCol={{ span: 4 }}
          loading={updateLoading || isGetDetailLoading}
          initialValues={{}}
        >
          {info?.supplierDistributionAttachment?.length ? (
            <Form.Item label>
              <Flex vertical>
                {info?.supplierDistributionAttachment?.map((el) => {
                  return (
                    <>
                      <Flex justify="space-between">
                        <Typography.Text
                          className="font-bold weight-[800]"
                          ellipsis
                        >
                          {el?.tenantName || '-'}
                        </Typography.Text>
                        <Typography.Text>
                          {el?.attachmentUpdateTime || '-'}
                        </Typography.Text>
                      </Flex>
                      <Typography.Text>{el?.remark}</Typography.Text>
                      <AttachmentUpload
                        value={el?.attachmentUrlList as unknown as UploadFile[]}
                        isView
                      />
                    </>
                  );
                })}
              </Flex>
            </Form.Item>
          ) : null}
          <Form.Item label="我的备注" name="remark">
            <Input.TextArea
              rows={4}
              maxLength={200}
              disabled={!hasAuth}
              placeholder="请填写备注，最大200字符"
            />
          </Form.Item>
          <Form.Item
            label="附件上传"
            name="attachmentList"
            className="pl-xs"
            rules={[
              {
                validator: (_, value: UploadFile[]) => {
                  if (!value) {
                    return Promise.resolve();
                  }

                  const hasNoSuccess = value.some(
                    (file) => file.status !== UploadStatus.Done,
                  );
                  return hasNoSuccess
                    ? Promise.reject(new Error('存在附件没有上传成功'))
                    : Promise.resolve();
                },
              },
            ]}
          >
            <AttachmentUpload
              isRetainOriginalFileName
              isView={!hasAuth}
              maxCount={10}
            />
          </Form.Item>
        </Form>
      </Flex>
    </Modal>
  );
};
