import { useMemo } from 'react';
import { InputSelect } from '../InputSelect';

export const PublisherNameInputSelect = (props: {
  options: Array<{ label: string; value: string }>;
  value?: string;
  onChange?: (value: string) => void;
}) => {
  const { options, value: propsValue, onChange } = props;

  const value = useMemo(() => {
    return (
      options.find((option) => option.value === propsValue) ?? {
        label: propsValue,
      }
    );
  }, [options, propsValue]);

  const handleChange = (values: { value: string }) => {
    onChange?.(values.value);
  };
  return (
    <InputSelect
      placeholder="请选择"
      isSpecial={false}
      isExceed={false}
      maxLength={100}
      options={options}
      value={value}
      onChange={handleChange}
      generateNewValue={(text) => ({
        value: text,
      })}
    />
  );
};
