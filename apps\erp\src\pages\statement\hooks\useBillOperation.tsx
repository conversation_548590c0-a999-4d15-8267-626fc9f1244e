import {
  ExclamationCircleFilled,
  Flex,
  InfoCircleFilled,
  Modal,
  msgInfo,
  msgSuccess,
  notification,
  Typography,
} from '@slt/base-ui';
import {
  StatementCollectionStatusType,
  StatementInfoTypeEnum,
  StatementTypeEnum,
  type StatementInfoType,
  type StatementType,
} from '../types';
import { routerMap } from '@/router/routerMap';
import { useOpenTab } from '@/hooks/useOpenTab';
import {
  useRef,
  useState,
  type Dispatch,
  type RefObject,
  type SetStateAction,
} from 'react';
import type { BizTableRef } from '@slt/biz-ui';
import {
  BillBusinessType,
  defaultEmptySelectMessage,
  StatementBillStatusType,
  zeroNumber,
} from '@slt/utils';
import {
  DELETE_STATEMENT_MODAL_SECONDARY_TEXT,
  DELETE_STATEMENT_MODAL_TEXT,
  MARK_FINISH_STATEMENT_MODAL_SECONDARY_TEXT,
  MARK_FINISH_STATEMENT_MODAL_TEXT,
  NOT_BILLS_MESSAGE,
  VERIFY_STATEMENT_MODAL_SECONDARY_TEXT,
  VERIFY_STATEMENT_MODAL_TEXT,
} from '../utils/constants';
import { useMutation } from '@slt/net-query';
import {
  statementResetDelete,
  statementFinish,
  statementSettlement,
  anonStatementSettlement,
  type StatementListVO,
  statementQueryReceivableInfo,
  transactionBillCreateOrderByStatements,
  stockReceiveAndPaymentBillQueryTaskProgressUrl,
  type TransactionBillCreateOrderByStatementsRequest,
} from '@slt/services';
import { useStatementDetailContextContext } from '../utils/PageContext';
import {
  OperationStatusNotification,
  type GoTransactionDataInfo,
  type GoTransactionFieldType,
} from '@/components/biz';
import type { SaveFailedReasonModalRef } from '@/components/biz/receipt/saveFailedReasonModal/types';
import { useActions, useTabs } from '@/store/system';
import { GlobalPollingTaskEnum } from '@/store/type';

const { confirm } = Modal;

type UseBillOperationOptions = {
  type: StatementType;
  /** 对账单信息类型：1、详情，2、分享 */
  infoType?: StatementInfoType;
  tableRef?: RefObject<BizTableRef<StatementListVO>>;
};

export const useBillOperation = (options: UseBillOperationOptions) => {
  const { type, infoType, tableRef } = options;
  const typeText = type === StatementTypeEnum.客户 ? '收' : '付';
  const tabs = useTabs();
  const { closeTab, startPolling } = useActions();
  const [notificationApi, notificationContextHolder] =
    notification.useNotification();

  const saveFailedReasonModalRef = useRef<SaveFailedReasonModalRef>(null);
  /** 去收款弹窗需要的展示信息 */
  const [dataInfo, setDataInfo] = useState<GoTransactionDataInfo>({
    count: 0,
    waitAmount: 0,
    amount: 0,
    transactionIds: [],
    filteredCount: 0,
  });

  const { mutate: mutateStatementSettlement } = useMutation(
    infoType === StatementInfoTypeEnum.分享
      ? anonStatementSettlement
      : statementSettlement,
  );
  const { mutate: mutateStatementFinish } = useMutation(statementFinish);
  const { mutate: mutateStatementQueryReceivableInfo } = useMutation(
    statementQueryReceivableInfo,
  );
  const { mutate: mutateTransactionBillCreateOrderByStatements } = useMutation(
    transactionBillCreateOrderByStatements,
  );
  const { mutate: mutateStatementResetDelete } =
    useMutation(statementResetDelete);
  const { reload, billInfoData, handleCheckRemove } =
    useStatementDetailContextContext();

  const { openTab } = useOpenTab();

  const getSelectedRows = () => {
    const selectedRows = tableRef?.current?.getSelectedRows().selectedRows;
    if (!selectedRows || selectedRows.length === 0) {
      return;
    }
    return selectedRows;
  };

  const tableReload = () => {
    tableRef?.current?.reload();
    void reload?.();
  };

  const handleToReconciliationByType = () => {
    if (type === StatementTypeEnum.客户) {
      openTab(routerMap.clientReconciliation.route);
    } else {
      openTab(routerMap.providerReconciliation.route);
    }
  };

  /** 核实账单 */
  const handleVerifyStatement = async (
    isDetail = false,
    cb?: () => void,
    ids?: string[],
  ) => {
    if (handleCheckRemove) {
      const isDelete = await handleCheckRemove();
      if (isDelete) {
        return;
      }
    }
    let billIds: string[];
    if (ids) {
      billIds = ids;
    } else {
      const selectedRows = getSelectedRows();
      if (!selectedRows || selectedRows.length === 0) {
        msgInfo(defaultEmptySelectMessage);
        return;
      }

      const hasInvalidStatusBill = selectedRows.some(
        (item) => item.statementStatus !== StatementBillStatusType.待核实,
      );
      if (hasInvalidStatusBill) {
        msgInfo('仅支持操作待核实状态的账单，请重新选择');
        return;
      }

      const selectedIds = selectedRows.map((item) => item.id);
      billIds = selectedIds;
    }

    confirm({
      title: '提示',
      icon: <InfoCircleFilled className="!text-blue-6" />,
      content: (
        <Flex vertical>
          <Typography.Text>{VERIFY_STATEMENT_MODAL_TEXT}</Typography.Text>
          {isDetail ? null : (
            <Typography.Text type="secondary" className="text-sm">
              注：{VERIFY_STATEMENT_MODAL_SECONDARY_TEXT}
            </Typography.Text>
          )}
        </Flex>
      ),
      onOk() {
        if (!billIds || billIds.length === 0) {
          msgSuccess('核实账单成功');
          tableReload();
          cb?.();
          return;
        }

        void mutateStatementSettlement({ statementIds: billIds }).then(
          (res) => {
            if (res.success) {
              msgSuccess('核实账单成功');
              tableReload();
              cb?.();
            }
          },
        );
      },
    });
  };

  /** 打开去收/付款弹窗，单个对账单 */
  const handleOpenGoTransactionModalByDetail = async (
    setModalVisible: Dispatch<SetStateAction<boolean>>,
  ) => {
    const isDelete = await handleCheckRemove();
    if (isDelete) {
      return;
    }
    if (!billInfoData?.id) {
      return;
    }

    const res = await mutateStatementQueryReceivableInfo({
      statementIdList: [billInfoData.id],
    });
    if (!res.success) {
      return;
    }

    const {
      statementCount,
      waitReceiveAmount,
      currentReceiveAmount,
      occupyReceiveBillNoList = [],
      needlessReceiveCount = zeroNumber,
    } = res.data || {};
    if (statementCount === 0) {
      const msg = `没有可以${typeText}款的单据，当前单据已全部${typeText}款完成`;
      msgInfo(msg);
      return;
    }

    setDataInfo({
      count: statementCount,
      waitAmount: waitReceiveAmount,
      amount: currentReceiveAmount,
      transactionIds: occupyReceiveBillNoList,
      filteredCount: needlessReceiveCount,
    });
    setModalVisible(true);
  };

  /** 打开去收/付款弹窗，批量 */
  const handleOpenGoTransactionModal = async (
    setModalVisible: Dispatch<SetStateAction<boolean>>,
  ) => {
    const selectedRows = getSelectedRows();
    if (!selectedRows) {
      msgInfo(defaultEmptySelectMessage);
      return;
    }

    // 判断selectedRows中，所有customerId是否相同
    const merchantId = selectedRows[0].merchantId;
    const isSameCustomer = selectedRows.every(
      (item) => item.merchantId === merchantId,
    );

    if (!isSameCustomer) {
      msgInfo(
        `请选择同一个客户去${type === StatementTypeEnum.客户 ? '收' : '付'}款`,
      );
      return;
    }

    const hasInvalidStatusBill = selectedRows.some(
      (item) =>
        item.statementStatus !== StatementBillStatusType.结算中 ||
        (item.receiveStatus as StatementCollectionStatusType) ===
          StatementCollectionStatusType.已收款,
    );
    const methodText = type === StatementTypeEnum.客户 ? '收' : '付';
    if (hasInvalidStatusBill) {
      msgInfo(
        `仅支持对结算中且未${methodText}款/部分${methodText}款的账单做${methodText}款，请重新选择`,
      );
      return;
    }

    const statementIdList = selectedRows.map((item) => item.id);

    const res = await mutateStatementQueryReceivableInfo({ statementIdList });
    if (!res.success) {
      return;
    }

    const {
      statementCount,
      waitReceiveAmount,
      currentReceiveAmount,
      occupyReceiveBillNoList = [],
      needlessReceiveCount = zeroNumber,
    } = res.data || {};
    if (statementCount === 0) {
      msgInfo(NOT_BILLS_MESSAGE);
      return;
    }

    setDataInfo({
      count: statementCount,
      waitAmount: waitReceiveAmount,
      amount: currentReceiveAmount,
      transactionIds: occupyReceiveBillNoList,
      filteredCount: needlessReceiveCount,
    });
    setModalVisible(true);
  };

  /** 去收付款页面 */
  const handleToTransaction = (transactionNo: string) => {
    const route =
      type === StatementTypeEnum.客户
        ? routerMap.receivedBill.route
        : routerMap.payBillList.route;

    openTab(route, {
      tabSearch: {
        billNoList: [transactionNo],
      },
    });
  };

  /** 去收/付款弹窗确认 */
  const handleGoTransactionConfirm = (formValues: GoTransactionFieldType) => {
    let params: TransactionBillCreateOrderByStatementsRequest;
    const businessType =
      type === StatementTypeEnum.客户
        ? BillBusinessType.收款单收款
        : BillBusinessType.付款单付款;
    if (tableRef) {
      const selectedRows =
        tableRef.current?.getSelectedRows().selectedRows || [];
      if (!selectedRows || selectedRows.length === 0) {
        return;
      }

      const statementIdList = selectedRows.map((item) => item.id);
      const customerId = selectedRows[0]?.merchantId;
      if (!customerId) {
        return;
      }

      params = {
        statementIdList,
        businessType,
        customerId,
        ...formValues,
      };
    } else {
      if (!billInfoData?.id || !billInfoData?.merchantId) {
        return;
      }

      const statementIdList = billInfoData?.id ? [billInfoData.id] : [];
      const customerId = billInfoData?.merchantId;
      params = {
        statementIdList,
        businessType,
        customerId,
        ...formValues,
      };

      if (!infoType) {
        params.batchReceive = true;
      }
    }

    void mutateTransactionBillCreateOrderByStatements(params).then((res) => {
      if (!res.success || !res.data) {
        return;
      }

      const {
        customerName = '',
        billNo,
        taskId,
        success,
        failReason,
      } = res.data;

      if (success && billNo && !taskId) {
        const statusText = `${customerName}的对账单${typeText}款完成`;
        notificationApi.open({
          message: '提示',
          description: (
            <OperationStatusNotification
              statusText={statusText}
              primaryBtnText="查看详情"
              handlePrimary={() => handleToTransaction(billNo)}
            />
          ),
          placement: 'bottomRight',
          duration: 0,
        });
        return;
      }

      if (failReason) {
        if (saveFailedReasonModalRef?.current) {
          saveFailedReasonModalRef?.current?.showModal(failReason);
        }
        return;
      }

      if (success && taskId) {
        const route =
          type === StatementTypeEnum.客户
            ? routerMap.receivedBill.route
            : routerMap.payBillList.route;

        startPolling({
          request: {
            url: stockReceiveAndPaymentBillQueryTaskProgressUrl + `/${taskId}`,
            method: 'get',
          },
          taskId,
          taskType: GlobalPollingTaskEnum.TOTRANSACTION,
          interval: 2000,
          successCondition: [
            {
              field: 'data.taskStatus',
              value: 2,
            },
            {
              field: 'data.taskStatus',
              value: 3,
            },
          ],
          msgConfig: {
            openDetailTabs: {
              to: route,
            },
          },
        });
      }
    });
  };

  /** 标记对账完成 */
  const handleMarkFinishStatement = async (
    isDetail = false,
    cb?: () => void,
    ids?: string[],
  ) => {
    if (handleCheckRemove) {
      const isDelete = await handleCheckRemove();
      if (isDelete) {
        return;
      }
    }
    let billIds: string[];
    if (ids) {
      billIds = ids;
    } else {
      const selectedRows = getSelectedRows();
      if (!selectedRows || selectedRows.length === 0) {
        msgInfo(defaultEmptySelectMessage);
        return;
      }

      const hasInvalidStatusBill = selectedRows.some(
        (item) => item.statementStatus !== StatementBillStatusType.结算中,
      );
      if (hasInvalidStatusBill) {
        msgInfo('仅支持操作结算中状态的账单，请重新选择');
        return;
      }

      const selectedIds = selectedRows.map((item) => item.id);
      billIds = selectedIds;
    }

    confirm({
      title: '提示',
      icon: <InfoCircleFilled className="!text-blue-6" />,
      content: (
        <Flex vertical>
          <Typography.Text>{MARK_FINISH_STATEMENT_MODAL_TEXT}</Typography.Text>
          {isDetail ? null : (
            <Typography.Text type="secondary" className="text-sm">
              注：{MARK_FINISH_STATEMENT_MODAL_SECONDARY_TEXT}
            </Typography.Text>
          )}
        </Flex>
      ),
      onOk() {
        if (!billIds || billIds.length === 0) {
          msgSuccess('标记对账成功');
          tableReload();
          cb?.();
          return;
        }

        void mutateStatementFinish({ statementIds: billIds }).then((res) => {
          if (res.success) {
            msgSuccess('标记对账成功');
            tableReload();
            cb?.();
          }
        });
      },
    });
  };

  /** 回收被删除的单据详情页面 */
  const handleCloseDetailByDelete = (deletedIds: string[]) => {
    const detailTabs = tabs.filter(
      (tab) =>
        tab.route === routerMap.clientStatementDetail.route &&
        deletedIds.includes((tab?.search?.statementId as string) || ''),
    );
    if (detailTabs.length > 0) {
      detailTabs.forEach((tab) => {
        void closeTab(tab);
      });
    }
  };

  /** 删除对账单 */
  const handleDeleteStatement = (cb?: () => void) => {
    const selectedRows = getSelectedRows();
    if (!selectedRows || selectedRows.length === 0) {
      msgInfo(defaultEmptySelectMessage);
      return;
    }

    const hasInvalidStatusBill = selectedRows.some(
      (item) => item.statementStatus !== StatementBillStatusType.待核实,
    );
    if (hasInvalidStatusBill) {
      msgInfo('仅支持删除待核实状态的账单，请重新选择');
      return;
    }

    const billIds = selectedRows.map((item) => item.id);

    confirm({
      title: '提示',
      icon: <ExclamationCircleFilled />,
      content: (
        <Flex vertical>
          <Typography.Text>{DELETE_STATEMENT_MODAL_TEXT}</Typography.Text>
          <Typography.Text type="secondary" className="text-sm">
            注：{DELETE_STATEMENT_MODAL_SECONDARY_TEXT}
          </Typography.Text>
        </Flex>
      ),
      onOk() {
        if (!billIds || billIds.length === 0) {
          msgSuccess('账单删除成功');
          tableReload();
          cb?.();
          handleCloseDetailByDelete(billIds);
          return;
        }

        void mutateStatementResetDelete({ statementIds: billIds }).then(
          (res) => {
            if (res.success) {
              msgSuccess('账单删除成功');
              tableReload();
              cb?.();
              handleCloseDetailByDelete(billIds);
            }
          },
        );
      },
    });
  };

  return {
    notificationContextHolder,
    saveFailedReasonModalRef,
    /** 去收款弹窗需要的展示信息 */
    dataInfo,
    /** 打开去收/付款弹窗，单个对账单 */
    handleOpenGoTransactionModalByDetail,
    /** 打开去收/付款弹窗，批量 */
    handleOpenGoTransactionModal,
    /** 去收/付款弹窗确认 */
    handleGoTransactionConfirm,
    getSelectedRows,
    tableReload,
    /** 取客户/供应商对账 */
    handleToReconciliationByType,
    /** 核实对账单 */
    handleVerifyStatement,
    /** 标记对账完成 */
    handleMarkFinishStatement,
    /** 删除对账单 */
    handleDeleteStatement,
  };
};
