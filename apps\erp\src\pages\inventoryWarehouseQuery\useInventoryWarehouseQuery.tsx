import { SelectionModalTabsType } from '@/components/biz';
import { useOpenTab } from '@/hooks/useOpenTab';
import { useTabRefresh } from '@/hooks/useTabRefresh';
import { routerMap } from '@/router/routerMap';
import { useTabSearchParams } from '@/store/system';
import type { CollectionProps, TableColumnType } from '@slt/base-ui';
import {
  Flex,
  Form,
  GoodsImage,
  inputNumberProps,
  SysTooltip,
  Typography,
} from '@slt/base-ui';
import type { CommonRecord } from '@slt/base-utils';
import { type BizTableRef, type QueryFormListItem } from '@slt/biz-ui';
import { useMutation } from '@slt/net-query';
import {
  bossBookPublisherSelectInputAll,
  inventoryInfoTotalData,
  type SpecificationDimensionParams,
  type SpecificationDimensionTableListDTO,
  type SummaryDataType,
} from '@slt/services';

import { useCallback, useMemo, useRef, useState } from 'react';
import { isNil } from 'lodash-es';
import { enumToOptions, type CommonSelectEnumString } from '@slt/utils';
import { NumberFilterType, type SearchType } from './types';

const { Link } = Typography;

export const useInventoryWarehouseQuery = () => {
  const [summaryData, setSummaryData] = useState<SummaryDataType>({});

  const { openTab } = useOpenTab();
  const tableRef =
    useRef<BizTableRef<SpecificationDimensionTableListDTO>>(null);
  const [form] = Form.useForm<CommonRecord>();

  const searchParams = useTabSearchParams();
  const skuId = searchParams.get('skuId');
  const goodsSkuName = searchParams.get('goodsSkuName');
  const searchWarehouseId = searchParams.get('warehouseId');

  const initialSearchValues = useMemo(() => {
    const values: SearchType = {
      goodsSkuIdList: skuId ? [{ value: skuId, label: goodsSkuName }] : [],
      warehouseIdList: [],
    };

    if (searchWarehouseId) {
      values.warehouseIdList = JSON.parse(
        decodeURIComponent(searchWarehouseId),
      ) as CommonSelectEnumString[];
    }
    return values;
  }, [skuId, goodsSkuName, searchWarehouseId]);

  useTabRefresh({
    refreshFunc: () => {
      tableRef.current?.reload();
    },
  });

  const { mutate: getTotal } = useMutation(inventoryInfoTotalData);

  /**
   * @description: 点击分仓库查询==>table表的下钻页面
   * @param {*} useCallback
   * @return {*}
   */
  const openType = useCallback(
    (type: string, params?: CommonRecord) => {
      switch (type) {
        case '实际库存':
          openTab(routerMap.inventoryWarehouseQueryByActual.route, {
            search: {
              ...params,
            },
          });
          break;
        case '可销售库存':
          openTab(routerMap.inventoryWarehouseQueryByMarketable.route, {
            search: {
              ...params,
            },
          });
          break;
        case '可发货库存':
          openTab(routerMap.inventoryWarehouseQueryByCanBeShipped.route, {
            search: {
              ...params,
            },
          });
          break;

        default:
          break;
      }
    },
    [openTab],
  );

  const nameOptions: CollectionProps['nameOptions'] = useMemo(() => {
    return [
      {
        type: 'input',
        formItemProps: {
          label: '快速查询',
          name: 'commonQuery',
        },
        fieldProps: {
          placeholder: `请输入商品名称/规格名称/商品编码/规格编码/ISBN查询`,
          allowClear: true,
        },
      },
      {
        type: 'batchInput',
        formItemProps: {
          label: '商品编码',
          name: 'goodsSpuNoList',
        },
        fieldProps: {
          placeholder: `请输入商品编码`,
          allowClear: true,
          showConfig: false,
        },
      },
      {
        type: 'batchInput',
        formItemProps: {
          label: '规格编码',
          name: 'goodsSkuNoList',
        },
        fieldProps: {
          placeholder: '请输入规格编码',
          allowClear: true,
          showConfig: false,
        },
      },
      {
        type: 'batchInput',
        formItemProps: {
          label: 'ISBN',
          name: 'isbnList',
        },
        fieldProps: {
          placeholder: '请输入ISBN',
          allowClear: true,
          showConfig: false,
        },
      },
    ];
  }, []);

  const searchColumns: QueryFormListItem[] = useMemo(() => {
    return [
      {
        type: 'collection',
        fieldProps: {
          nameOptions: nameOptions,
        },
      },
      {
        key: 'warehouseIdList',
        type: 'warehouseSelect',
        fieldProps: {
          mode: 'multiple',
          allowClear: true,
          fieldNames: {
            label: 'label',
            value: 'value',
          },
        },
        formItemProps: {
          label: '仓库',
          name: 'warehouseIdList',
        },
      },
      {
        type: 'goodsTableSelect',
        key: 'goodsSkuIdList',
        fieldProps: {
          showDisabled: true,
          labelInValue: true,
          fieldNames: { value: 'id' },
          acceptGoodsTabs: [SelectionModalTabsType['普通自有']],
        },
        formItemProps: { label: '商品', name: 'goodsSkuIdList' },
      },
      {
        type: 'select',
        fieldProps: {
          options: [
            { label: '启用', value: true },
            { label: '停用', value: false },
          ],
        },
        formItemProps: {
          label: '商品状态',
          name: 'enable',
        },
      },
      {
        type: 'select',
        fieldProps: {
          options: enumToOptions(NumberFilterType),
        },
        formItemProps: {
          label: '数量过滤',
          name: 'numberFilterType',
        },
      },
      {
        key: 'stock',
        type: 'rangeInputNumber',
        fieldProps: {
          fieldNames: {
            start: 'stockMin',
            end: 'stockMax',
          },
          min: 0,
          ...inputNumberProps.int,
        },
        formItemProps: {
          label: '实际库存',
          name: 'stock',
        },
      },
      {
        key: 'availableStock',
        type: 'rangeInputNumber',
        fieldProps: {
          fieldNames: {
            start: 'canSaleStockMin',
            end: 'canSaleStockMax',
          },
          min: 0,
          ...inputNumberProps.int,
        },
        formItemProps: {
          label: '可销售库存',
          name: 'availableStock',
        },
      },
      {
        key: 'price',
        type: 'rangeInputNumber',
        fieldProps: {
          fieldNames: {
            start: 'priceMin',
            end: 'priceMax',
          },
          min: 0,
          ...inputNumberProps.price,
        },
        formItemProps: {
          label: '定价',
          name: 'price',
        },
      },
      {
        type: 'apiSelect',
        fieldProps: {
          mode: 'multiple',
          fieldNames: { label: 'key', value: 'value' },
          request: bossBookPublisherSelectInputAll,
        },
        formItemProps: {
          label: '出版社',
          name: 'publisherIdList',
        },
      },
    ];
  }, []);

  const tableColumns = useMemo(() => {
    const columns: TableColumnType<SpecificationDimensionTableListDTO>[] = [
      {
        title: '仓库',
        key: 'warehouseName',
      },
      {
        title: '图片',
        key: 'image',
        render: (img?: string) => {
          if (!img) {
            return null;
          }

          return <GoodsImage src={img} size="small" />;
        },
      },
      {
        title: '商品名称',
        key: 'goodsSpuName',
      },
      {
        title: '商品编码',
        key: 'goodsSpuNo',
      },
      {
        title: '规格编码',
        key: 'goodsSkuNo',
      },
      {
        title: '供应商',
        key: 'supplierName',
      },
      {
        title: '规格名称',
        key: 'goodsSkuName',
      },
      {
        width: 'number',
        title: '定价',
        key: 'pricingPrice',
      },
      {
        title: '出版社',
        key: 'publisherName',
      },
      {
        title: 'ISBN',
        key: 'isbn',
      },
      {
        width: 'number',
        title: '实际库存',
        key: 'stock',

        render: (_, record) => {
          return record.stock ? (
            <Link
              authId={{
                authId: '889',
                authType: 'text',
              }}
              onClick={() => {
                const warehouseId = encodeURIComponent(
                  JSON.stringify([
                    { label: record.warehouseName, value: record.warehouseId },
                  ]),
                );
                openType('实际库存', {
                  warehouseId,
                  skuId: record.skuId,
                  goodsSpuName: record.goodsSpuName,
                });
              }}
            >
              {record.stock}
            </Link>
          ) : (
            '-'
          );
        },
      },
      {
        width: 'number',
        title: '成本均价',
        key: 'costAmount',
      },
      {
        width: 'number',
        title: '库存余额',
        key: 'totalAmount',
      },
      {
        title: '可销售库存',
        key: 'canSaleStock',
        titleRender: (title) => {
          return (
            <Flex gap={4}>
              <Typography.Text>{title}</Typography.Text>
              <SysTooltip title="根据设置-系统配置-库存设置中的可销售库存配置的公式进行计算" />
            </Flex>
          );
        },
        render: (_, record) => {
          return !isNil(record.canSaleStock) ? (
            <Link
              authId={{
                authId: '890',
                authType: 'text',
              }}
              onClick={() => {
                const warehouseId = encodeURIComponent(
                  JSON.stringify([
                    { label: record.warehouseName, value: record.warehouseId },
                  ]),
                );
                openType('可销售库存', {
                  skuId: record.skuId,
                  goodsSpuName: record.goodsSpuName,
                  goodsSkuName: record.goodsSkuName,
                  goodsSkuNo: record.goodsSkuNo,
                  warehouseId,
                });
              }}
            >
              {record.canSaleStock}
            </Link>
          ) : (
            '-'
          );
        },
      },
      {
        width: 'number',
        title: '可发货库存',
        key: 'canDeliverStock',
        render: (_, record) => {
          return !isNil(record.canDeliverStock) ? (
            <Link
              authId={{
                authId: '2048',
                authType: 'text',
              }}
              onClick={() => {
                const warehouseId = encodeURIComponent(
                  JSON.stringify([
                    { label: record.warehouseName, value: record.warehouseId },
                  ]),
                );
                openType('可发货库存', {
                  skuId: record.skuId,
                  goodsSpuName: record.goodsSpuName,
                  goodsSkuName: record.goodsSkuName,
                  goodsSkuNo: record.goodsSkuNo,
                  warehouseId,
                });
              }}
            >
              {record.canDeliverStock}
            </Link>
          ) : (
            '-'
          );
        },
      },
      {
        width: 'number',
        title: '昨日销量',
        key: 'yesterdaySales',
        tooltips:
          '按单据日期统计前一天已确认的销售出库单，存在1天延迟（不包含今天新增的销售出库单）',
      },
      {
        width: 'number',
        title: '7天销量',
        key: 'weekSales',
        tooltips:
          '按单据日期统计前7天已确认的销售出库单，存在1天延迟（不包含今天新增的销售出库单）',
      },
      {
        width: 'number',
        title: '30天销量',
        key: 'monthSales',
        tooltips:
          '按单据日期统计前30天已确认的销售出库单，存在1天延迟（不包含今天新增的销售出库单）',
      },
      {
        width: 'remark',
        title: '昨日退货量',
        key: 'yesterdayReturn',
        tooltips:
          '按单据日期统计前一天已确认的销售退货单，存在1天延迟（不包含今天新增的销售退货单）',
      },
      {
        width: 'number',
        title: '7天退货量',
        key: 'weekReturn',
        tooltips:
          '按单据日期统计前7天已确认的销售退货单，存在1天延迟（不包含今天新增的销售退货单）',
      },
      {
        width: 'remark',
        title: '30天退货量',
        key: 'monthReturn',
        tooltips:
          '按单据日期统计前30天已确认的销售退货单，存在1天延迟（不包含今天新增的销售退货单',
      },
    ];

    return columns;
  }, [openType]);

  const getTotalData = useCallback(
    async (params: SpecificationDimensionParams) => {
      const res = await getTotal(params);
      if (res?.success && res?.data) {
        const {
          totalStock: stock,
          totalCanSaleStock: canSaleStock,
          totalPageAmount: totalAmount,
          totalCanDeliveryStock: canDeliverStock,
          yesterdaySales,
          weekSales,
          monthSales,
          yesterdayReturn,
          weekReturn,
          monthReturn,
        } = res.data;

        setSummaryData({
          stock,
          canSaleStock,
          totalAmount,
          canDeliverStock,
          yesterdaySales,
          weekSales,
          monthSales,
          yesterdayReturn,
          weekReturn,
          monthReturn,
        });
      }
    },
    [setSummaryData, getTotal],
  );

  /**发送请求前的参数处理 */
  const beforeSearchData = useCallback(
    (params: CommonRecord) => {
      const { warehouseIdList, goodsSkuIdList } = params;
      const newParams: SpecificationDimensionParams = {
        ...params,
        separateWarehouse: true, // 分仓查询
        dimension: 2, // 规格维度
        goodsSkuNoVagueType: 0, // 精确匹配规格编码
      };

      if (warehouseIdList) {
        const warehouseIdListInfo = warehouseIdList as CommonSelectEnumString[];
        newParams.warehouseIdList = warehouseIdListInfo.map((v) =>
          typeof v === 'object' ? v.value : v,
        );
      }

      if (goodsSkuIdList) {
        const goodsSkuIdListInfo = goodsSkuIdList as CommonSelectEnumString[];
        newParams.goodsSkuIdList = goodsSkuIdListInfo.map((v) => v.value);
      }

      getTotalData(newParams);
      return newParams;
    },
    [getTotalData],
  );

  const toManualRecalculateCostPage = useCallback(() => {
    openTab(routerMap.manualRecalculateCost.route);
  }, [openTab]);

  return {
    form,
    initialSearchValues,
    summaryData,
    tableRef,
    searchColumns,
    tableColumns,
    beforeSearchData,
    toManualRecalculateCostPage,
  };
};
