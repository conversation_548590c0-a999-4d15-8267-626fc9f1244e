import {
  OperationStatusNotification,
  type GoTransactionDataInfo,
  type GoTransactionFieldType,
} from '@/components/biz';
import type { SaveFailedReasonModalRef } from '@/components/biz/receipt/saveFailedReasonModal/types';
import { useOpenTab } from '@/hooks/useOpenTab';
import { routerMap } from '@/router/routerMap';
import { useActions } from '@/store/system';
import { GlobalPollingTaskEnum } from '@/store/type';
import { msgInfo, notification } from '@slt/base-ui';
import type { BizTableRef } from '@slt/biz-ui';
import { useMutation } from '@slt/net-query';
import {
  contrastQueryReceivableInfo,
  stockReceiveAndPaymentBillQueryTaskProgressUrl,
  transactionBillCreateOrderByContrast,
  type ResponseDetailData,
} from '@slt/services';
import {
  BillBusinessType,
  defaultEmptySelectMessage,
  zeroNumber,
} from '@slt/utils';
import {
  useRef,
  useState,
  type Dispatch,
  type RefObject,
  type SetStateAction,
} from 'react';

type UseGoTransactionDataOptions = {
  type: 'client' | 'provider';
  tableRef: RefObject<BizTableRef<ResponseDetailData>>;
};

export const useGoTransactionData = (options: UseGoTransactionDataOptions) => {
  const { type, tableRef } = options;

  const { openTab } = useOpenTab();
  const [notificationApi, notificationContextHolder] =
    notification.useNotification();
  const { startPolling } = useActions();
  const typeText = type === 'client' ? '收' : '付';

  const saveFailedReasonModalRef = useRef<SaveFailedReasonModalRef>(null);
  /** 去收款弹窗需要的展示信息 */
  const [dataInfo, setDataInfo] = useState<GoTransactionDataInfo>({
    count: 0,
    amount: 0,
    transactionIds: [],
    filteredCount: 0,
  });

  const { mutate: mutateContrastQueryReceivable } = useMutation(
    contrastQueryReceivableInfo,
  );
  const { mutate: mutateContrastReceivable } = useMutation(
    transactionBillCreateOrderByContrast,
  );

  /** 打开去收/付款弹窗，批量 */
  const handleOpenGoTransactionModal = async (
    setModalVisible: Dispatch<SetStateAction<boolean>>,
  ) => {
    const selectedRows = tableRef.current?.getSelectedRows().selectedRows;
    if (!selectedRows?.length) {
      msgInfo(defaultEmptySelectMessage);
      return;
    }

    const ids = selectedRows.map((item) => item.id);

    const res = await mutateContrastQueryReceivable({ contrastIdList: ids });

    if (!res.success) {
      return;
    }

    const {
      statementCount,
      waitReceiveAmount,
      currentReceiveAmount,
      occupyReceiveBillNoList = [],
      needlessReceiveCount = zeroNumber,
    } = res.data || {};
    if (statementCount === 0) {
      const msg = `无可${typeText}款的单据，当前单据已${typeText}款完成或已被其他待审核${typeText}款单结算`;
      msgInfo(msg);
      return;
    }

    setDataInfo({
      count: statementCount,
      waitAmount: waitReceiveAmount,
      amount: currentReceiveAmount,
      transactionIds: occupyReceiveBillNoList,
      filteredCount: needlessReceiveCount,
    });
    setModalVisible(true);
  };

  const handleToTransaction = (billNoList: string[]) => {
    const route =
      type === 'client'
        ? routerMap.receivedBill.route
        : routerMap.payBillList.route;

    openTab(route, {
      tabSearch: {
        billNoList,
      },
    });
  };

  /** 去收/付款弹窗确认 */
  const handleGoTransactionConfirm = (formValues: GoTransactionFieldType) => {
    const selectedRows = tableRef.current?.getSelectedRows().selectedRows;

    if (!selectedRows?.length) {
      return;
    }

    const ids = selectedRows?.map((item) => item.id) || [];
    const customerId = selectedRows[0]?.customerId;
    const businessType =
      type === 'client'
        ? BillBusinessType.收款单收款
        : BillBusinessType.付款单付款;

    void mutateContrastReceivable({
      contrastIdList: ids,
      businessType,
      customerId,
      ...formValues,
    }).then((res) => {
      if (!res.success || !res.data) {
        return;
      }

      const {
        customerName = '',
        billNo,
        taskId,
        success,
        failReason,
      } = res.data;

      if (success && billNo && !taskId) {
        const statusText = `${customerName}的对账${typeText}款完成`;
        notificationApi.open({
          message: '提示',
          description: (
            <OperationStatusNotification
              statusText={statusText}
              primaryBtnText="查看详情"
              handlePrimary={() => handleToTransaction([billNo])}
            />
          ),
          placement: 'bottomRight',
          duration: 0,
        });
        return;
      }

      if (failReason) {
        if (saveFailedReasonModalRef?.current) {
          saveFailedReasonModalRef?.current?.showModal(failReason);
        }
        return;
      }

      if (success && taskId) {
        const route =
          type === 'client'
            ? routerMap.receivedBill.route
            : routerMap.payBillList.route;

        startPolling({
          request: {
            url: stockReceiveAndPaymentBillQueryTaskProgressUrl + `/${taskId}`,
            method: 'get',
          },
          taskId,
          taskType: GlobalPollingTaskEnum.TOTRANSACTION,
          interval: 2000,
          successCondition: [
            {
              field: 'data.taskStatus',
              value: 2,
            },
            {
              field: 'data.taskStatus',
              value: 3,
            },
          ],
          msgConfig: {
            openDetailTabs: {
              to: route,
            },
          },
        });
      }
    });
  };

  return {
    notificationContextHolder,
    /** 去收款弹窗需要的展示信息 */
    dataInfo,
    saveFailedReasonModalRef,
    /** 打开去收/付款弹窗 */
    handleOpenGoTransactionModal,
    /** 去收/付款弹窗确认 */
    handleGoTransactionConfirm,
  };
};
