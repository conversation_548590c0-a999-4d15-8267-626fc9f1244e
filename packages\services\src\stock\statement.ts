import type { YTRequest } from '@slt/net';
import type {
  StatementResetRepaymentTimeParams,
  StatementQueryPageParams,
  StatementDetailBillInfoVO,
  GoodsCollectQuery,
  GoodsWithDetailQuery,
  GoodsReceivableCollectVO,
  GoodsReceivableWithDetailVO,
  StatementListVO,
  GoodsReceivableQuery,
  GoodsReceivableVO,
  OtherReceivableQuery,
  OtherReceivableVO,
  StatementResetInvoiceParams,
  GoodsReceivableStatisticsVO,
  StatementSmsVerifyParams,
  DownloadStatusVO,
  BatchModifyRemarkParams,
  StatementQueryDiffVO,
  StatementReceivableVO,
  UpdateStatementTagRequest,
  StatementRestBillRemarkRequest,
  StatementRestOtherBillRemarkRequest,
  StatementRestBillDetailRemarkRequest,
  StatementDetailRestBillRemarkRequest,
  StatementTransactionBillCreateVo,
  TransactionBillCreateOrderByStatementsRequest,
} from './statement.type';
import type {
  CommonTableResultVO,
  CommonTableResultVOWithPageVO,
} from '../common.types';
import type {
  SystemTagCommonQuery,
  SystemTagCommonVO,
} from '../basic/tag.interface';

/** 对账单列表，客户分页查询请求 */
export const statementCustomerQueryPage = (
  data?: StatementQueryPageParams,
): YTRequest<StatementListVO> => {
  return {
    url: '/stock/statement/customer/query/page',
    data,
  };
};
/** 对账单列表，供应商分页查询请求 */
export const statementSupplierQueryPage = (
  data?: StatementQueryPageParams,
): YTRequest<StatementListVO> => {
  return {
    url: '/stock/statement/supplier/query/page',
    data,
  };
};
/** 对账单列表，核实对账单 */
export const statementSettlement = (data: {
  statementIds: string[];
}): YTRequest<boolean> => {
  return {
    url: '/stock/statement/settlement',
    data,
  };
};
/** 对账单列表，核实对账单，免登录 */
export const anonStatementSettlement = (data: {
  statementIds: string[];
}): YTRequest<boolean> => {
  return {
    url: '/stock/anon/statement/settlement',
    data,
  };
};

/** 对账单短信校验 */
export const statementSmsVerify = (
  data: StatementSmsVerifyParams,
): YTRequest<boolean> => {
  return {
    url: '/stock/statement/sms/check',
    data,
  };
};

/** 对账单列表，标记对账完成 */
export const statementFinish = (data: {
  statementIds: string[];
}): YTRequest<boolean> => {
  return {
    url: '/stock/statement/finish',
    data,
  };
};

/** 对账单列表，设置最后还款时间 */
export const statementResetRepaymentTime = (
  data: StatementResetRepaymentTimeParams,
): YTRequest<boolean> => {
  return {
    url: '/stock/statement/reset/repayment/time',
    data,
  };
};
/** 对账单列表，删除账单 */
export const statementResetDelete = (data: {
  statementIds: string[];
}): YTRequest<boolean> => {
  return {
    url: '/stock/statement/delete',
    data,
  };
};

/** 对账单详情，账单备注 */
export const statementResetRemark = (data: {
  id: string;
  remark: string;
}): YTRequest<boolean> => {
  return {
    url: '/stock/statement/reset/remark',
    data,
  };
};

/** 对账单，去收款信息获取 */
export const statementQueryReceivableInfo = (data: {
  statementId?: string;
  statementIdList?: string[];
}): YTRequest<StatementReceivableVO> => {
  return {
    url: '/stock/statement/query/receivable/info',
    data,
  };
};

/** 对账单，去收款 */
export const transactionBillCreateOrderByStatements = (
  data: TransactionBillCreateOrderByStatementsRequest,
): YTRequest<StatementTransactionBillCreateVo> => {
  return {
    url: '/stock/receive/and/payment/bill/create/order/by/statements',
    data,
  };
};

/** 对账单详情，添加单据 */
export const statementAddDetail = (data: {
  /** 对账单id */
  financeStatementId: string;
  /** 添加的对账明细id */
  contrastBillIds: string[];
}): YTRequest<boolean> => {
  return {
    url: '/stock/statement/add/detail',
    data,
  };
};
/** 对账单详情，移除单据 */
export const statementRemoveDetail = (data: {
  /** 对账单id */
  financeStatementId: string;
  /** 移除的对账明细id */
  contrastBillIds: string[];
}): YTRequest<boolean> => {
  return {
    url: '/stock/statement/remove/detail',
    data,
  };
};

/** 下载账单taskId获取 */
export const statementBillDownload = (data: {
  /** 对账单id */
  statementId: string;
  /** 是否包含收货信息 */
  containReceiverInfo: number;
}): YTRequest<string> => {
  return {
    url: '/stock/statement/download',
    data,
  };
};
/** 下载账单taskId获取，免登录 */
export const anonStatementBillDownload = (data: {
  /** 对账单id */
  statementId: string;
  /** 是否包含收货信息 */
  containReceiverInfo: number;
}): YTRequest<string> => {
  return {
    url: '/stock/anon/statement/download',
    data,
  };
};

/** 通过任务Id下载对账单 */
export const statementDownloadByTaskId = (
  taskId: string,
): YTRequest<DownloadStatusVO> => {
  return {
    url: `/stock/statement/download/status/${taskId}`,
    method: 'GET',
  };
};
/** 通过任务Id下载对账单，免登录 */
export const anonStatementDownloadByTaskId = (
  taskId: string,
): YTRequest<DownloadStatusVO> => {
  return {
    url: `/stock/anon/download/status/${taskId}`,
    method: 'GET',
  };
};

/** 对账单详情，上传发票 */
export const statementResetInvoice = (
  data: StatementResetInvoiceParams,
): YTRequest<boolean> => {
  return {
    url: '/stock/statement/reset/invoice',
    data,
  };
};

/** 对账单详情——账单信息 */
export const statementQueryById = (
  id: string,
): YTRequest<StatementDetailBillInfoVO> => {
  return {
    url: `/stock/statement/query/${id}`,
    method: 'GET',
  };
};
/** 对账单详情——账单信息，免登录 */
export const anonStatementQueryById = (
  id: string,
): YTRequest<StatementDetailBillInfoVO> => {
  return {
    url: `/stock/anon/statement/query/${id}`,
    method: 'GET',
  };
};

/** 对账单详情——获取协同创建的对账单单据数的差异信息 */
export const statementQuerySynergyDiff = (data: {
  statementId?: string;
  statementIdList?: string[];
}): YTRequest<StatementQueryDiffVO> => {
  return {
    url: '/stock/statement/query/synergy/diff',
    data,
  };
};
/** 对账单详情——获取协同创建的对账单单据数的差异信息，免登录 */
export const anonStatementQuerySynergyDiff = (data: {
  statementId?: string;
  statementIdList?: string[];
}): YTRequest<StatementQueryDiffVO> => {
  return {
    url: '/stock/anon/statement/query/synergy/diff',
    data,
  };
};

/** 对账单详情，商品应付/应收表头统计 */
export const statementGoodReceivableSum = (data?: {
  /** 对账单id */
  statementId: string;
}): YTRequest<GoodsReceivableStatisticsVO> => {
  return {
    url: '/stock/statement/goods/receivable/sum',
    data,
  };
};
/** 对账单详情，商品应付/应收表头统计，免登录 */
export const anonStatementGoodReceivableSum = (data?: {
  /** 对账单id */
  statementId: string;
}): YTRequest<GoodsReceivableStatisticsVO> => {
  return {
    url: '/stock/anon/statement/goods/receivable/sum',
    data,
  };
};

/** 对账单详情——商品应收/付——商品汇总 */
export const statementGoodsReceivableCollectPage = (
  data?: GoodsCollectQuery,
): YTRequest<CommonTableResultVO<GoodsReceivableCollectVO>> => {
  return {
    url: '/stock/statement/goods/receivable/collect/page',
    data,
  };
};
/** 对账单详情——商品应收/付——商品汇总，免登录 */
export const anonStatementGoodsReceivableCollectPage = (
  data?: GoodsCollectQuery,
): YTRequest<CommonTableResultVO<GoodsReceivableCollectVO>> => {
  return {
    url: '/stock/anon/statement/goods/receivable/collect/page',
    data,
  };
};
/** 对账单详情——商品应收/付——商品销售/采购及退货明细 */
export const statementGoodsReceivableDetailPage = (
  data?: GoodsWithDetailQuery,
): YTRequest<CommonTableResultVO<GoodsReceivableWithDetailVO>> => {
  return {
    url: '/stock/statement/goods/receivable/detail/page',
    data,
  };
};
/** 对账单详情——商品应收/付——商品销售/采购及退货明细，免登录 */
export const anonStatementGoodsReceivableDetailPage = (
  data?: GoodsWithDetailQuery,
): YTRequest<CommonTableResultVO<GoodsReceivableWithDetailVO>> => {
  return {
    url: '/stock/anon/statement/goods/receivable/detail/page',
    data,
  };
};
/** 对账单详情——商品应收/付——单据明细 */
export const statementGoodsReceivablePage = (
  data?: GoodsReceivableQuery,
): YTRequest<CommonTableResultVO<GoodsReceivableVO>> => {
  return {
    url: '/stock/statement/goods/receivable/page',
    data,
  };
};
/** 对账单详情——商品应收/付——单据明细，免登录 */
export const anonStatementGoodsReceivablePage = (
  data?: GoodsReceivableQuery,
): YTRequest<CommonTableResultVO<GoodsReceivableVO>> => {
  return {
    url: '/stock/anon/statement/goods/receivable/page',
    data,
  };
};
/** 对账单详情——其他应收/应付及调账 */
export const statementOtherReceivablePage = (
  data?: OtherReceivableQuery,
): YTRequest<CommonTableResultVOWithPageVO<OtherReceivableVO>> => {
  return {
    url: '/stock/statement/other/receivable/page',
    data,
  };
};
/** 对账单详情——其他应收/应付及调账，免登录 */
export const anonStatementOtherReceivablePage = (
  data?: OtherReceivableQuery,
): YTRequest<CommonTableResultVOWithPageVO<OtherReceivableVO>> => {
  return {
    url: '/stock/anon/statement/other/receivable/page',
    data,
  };
};

/** 长链接转短链接 */
export const longUrlToShortUrl = (data: {
  /** 长链接 */
  longUrl: string;
}): YTRequest<string> => {
  return {
    url: '/stock/statement/convert/short/url/',
    data,
  };
};

/** 修改单据备注 */
export const batchModifyBillRemark = (
  data: BatchModifyRemarkParams,
): YTRequest<boolean> => {
  return {
    url: '/stock/batch/modify/bill/remark',
    data,
  };
};

/** 修改明细备注 */
export const batchModifyDetailRemark = (
  data: BatchModifyRemarkParams,
): YTRequest<boolean> => {
  return {
    url: '/stock/batch/modify/detail/remark',
    data,
  };
};

/** 获取对账标签 */
export const statementQueryTag = (
  data?: SystemTagCommonQuery,
): YTRequest<SystemTagCommonVO> => {
  return {
    url: '/stock/statement/query/tag',
    data,
  };
};

/** 修改对账标签 */
export const statementRestBillTag = (
  data: UpdateStatementTagRequest,
): YTRequest<boolean> => {
  return {
    url: '/stock/statement/reset/bill/tag',
    data,
  };
};

/** 修改单据备注--商品应收-商品销售及退货明细 */
export const statementDetailRestBillRemark = (
  data: StatementDetailRestBillRemarkRequest,
): YTRequest<boolean> => {
  return {
    url: '/stock/statement/detail/reset/bill/remark',
    data,
  };
};

/** 修改单据备注--商品应收-单据明细 */
export const statementRestBillRemark = (
  data: StatementRestBillRemarkRequest,
): YTRequest<boolean> => {
  return {
    url: '/stock/statement/reset/bill/remark',
    data,
  };
};

/** 修改单据备注--其他收入及调账 */
export const statementRestOtherBillRemark = (
  data: StatementRestOtherBillRemarkRequest,
): YTRequest<boolean> => {
  return {
    url: '/stock/statement/reset/other/bill/remark',
    data,
  };
};

/** 修改单据明细备注--商品应收-商品销售及退货明细 */
export const statementRestBillDetailRemark = (
  data: StatementRestBillDetailRemarkRequest,
): YTRequest<boolean> => {
  return {
    url: '/stock/statement/reset/bill/detail/remark',
    data,
  };
};

/** 检测对账单是否被删除 */
export const statementCheckRemove = (id: string): YTRequest<boolean> => {
  return {
    url: `/stock/statement/check/remove/${id}`,
    method: 'get',
  };
};
export const anonStatementCheckRemove = (id: string): YTRequest<boolean> => {
  return {
    url: `/stock/anon/statement/check/remove/${id}`,
    method: 'get',
  };
};
