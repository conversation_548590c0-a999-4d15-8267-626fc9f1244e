/** 屏幕最小宽度 */
export const pageContainerMinWidth = 1280;

/**
 * 错误码白名单
 * @description 错误码为3001、3002时，不进行错误提示
 */
export const errorcodeWhiteList = [3001, 3002];

/** 发货时间枚举 */
export enum DeliveryTime {
  '24h' = 24,
  '48h' = 48,
  '72h' = 72,
  '7天' = 168,
  '15天' = 360,
  '30天' = 720,
}

/**首页弹窗打开先后顺序 */
export enum ModalOpenPriorityEnum {
  /**公告弹窗 */
  NoticeModal = 1,
  /**客户合作审核弹窗 */
  InviteSupplierProcessModal = 2,
  /**店铺授权过期提醒弹窗 */
  ShopAuthExpiredRemindModal = 3,
}
