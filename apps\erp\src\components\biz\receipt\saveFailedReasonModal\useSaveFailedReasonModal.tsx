import { useCallback, useState } from 'react';
import type { SaveFailedReasonModalProps } from './types';
import {
  ErrorResultCheckedType,
  type CheckResultDTO,
  type CheckResultParams,
} from '@slt/services';
import { Form } from '@slt/base-ui';
import type { CommonRecord } from '@slt/base-utils';
import type { NewErrorDataDTO, NewResultGoodsDTO } from '../types';
import { useOpenTab } from '@/hooks/useOpenTab';
import { ReceiptListPageUrl } from '../constants';

const defaultErrorTitle = '单据保存失败，原因如下';

export const useSaveFailedReasonModal = (props: SaveFailedReasonModalProps) => {
  const { onEditReceiptNo, onSaveContinue, onCreateReceiptNo } = props;
  const [visible, setVisible] = useState(false);
  /**能否继续保存 */
  const [canContinueSave, setCanContinueSave] = useState(false);
  /**单据编号重复 */
  const [receiptNoInfo, setReceiptInfo] = useState<CheckResultDTO>();
  /**商品没有出库成本价，需要填写固定成本价 */
  const [costPriceOfDeliveryForm, setCostPriceOfDeliveryForm] =
    useState<NewErrorDataDTO>();
  /**其他错误信息 */
  const [resultInfoList, setResultInfoList] = useState<CheckResultDTO[]>([]);
  /**如果可以继续保存情况下如果type为200，则不展示提示title */
  const [isShowTitle, setIsShowTitle] = useState(true);
  /** 错误提示内容 */
  const [errorTitle, setErrorTitle] = useState(defaultErrorTitle);

  /**表单 */
  const [form] = Form.useForm<CommonRecord>();
  const { openTab } = useOpenTab();

  const handleInit = useCallback(
    (params: CheckResultParams, tips?: string) => {
      const { continues, resultList, limitedResultList, continuesResultList } =
        params;
      // 单据的校验结果list修改了数据结构，其他如库存等没改，所以做一下判断
      let checkList: CheckResultDTO[] = [];
      if (resultList && resultList.length > 0) {
        checkList = resultList;
      } else if (limitedResultList && limitedResultList.length > 0) {
        checkList = limitedResultList;
      } else if (continuesResultList && continuesResultList.length > 0) {
        /*** 如果可以继续保存情况下如果type全是200，则不展示提示title */
        const isAllType200 = continuesResultList.every(
          (item) => item?.type === ErrorResultCheckedType.TypeTwoHundred,
        );
        setIsShowTitle(!isAllType200);
        checkList = continuesResultList;
      }

      /**type=100,表示单据编号重复 */
      const receiptNoErrorInfo = checkList?.find(
        (item) => item.type === ErrorResultCheckedType.TypeOneHundred,
      );
      if (receiptNoErrorInfo) {
        setReceiptInfo(receiptNoErrorInfo);
      } else {
        /**type=31,商品没有出库成本价，需要填写固定成本价-特殊处理 */
        const noCostOfDelivery = checkList?.find(
          (item) => item.type === ErrorResultCheckedType.TypeThirtyOne,
        );
        if (noCostOfDelivery) {
          const { errorMsg, goodsList } = noCostOfDelivery?.errorData || {};
          const newGoodsList = goodsList?.map((item) => {
            return {
              ...item,
              handWriteCostPrice: undefined,
            };
          });

          setCostPriceOfDeliveryForm({
            errorMsg,
            goodsList: newGoodsList ?? [],
          });
          form.setFieldValue('goodsList', newGoodsList);
        }

        /**其余错误信息 */
        const newResultInfoList =
          checkList?.filter(
            (item) =>
              ![
                ErrorResultCheckedType.TypeThirtyOne,
                ErrorResultCheckedType.TypeOneHundred,
              ].includes(item.type),
          ) ?? [];

        setResultInfoList(newResultInfoList);

        setCanContinueSave(continues);
        if (continues) {
          setErrorTitle('以下信息保存异常，请检查后确认是否继续');
        } else if (tips) {
          setErrorTitle(tips);
        }
      }
    },
    [form],
  );

  const showModal = useCallback(
    (params: CheckResultParams, tips?: string) => {
      handleInit(params, tips);
      setVisible(true);
    },
    [handleInit],
  );

  const onCancel = useCallback(() => {
    setVisible(false);
    setCanContinueSave(false);
    setReceiptInfo(undefined);
    setCostPriceOfDeliveryForm(undefined);
    setErrorTitle(defaultErrorTitle);
    setResultInfoList([]);
    form.resetFields();
  }, [form]);

  const onRowDataChange = useCallback(
    (
      row: NewResultGoodsDTO,
      list: NewResultGoodsDTO[],
      // changed: CommonRecord,
    ) => {
      const obj = {
        ...costPriceOfDeliveryForm,
        goodsList: list,
      } as NewErrorDataDTO;
      setCostPriceOfDeliveryForm(obj);
      form.setFieldValue('goodsList', list);
    },
    [costPriceOfDeliveryForm, form],
  );

  /**继续保存 */
  const handleContinue = useCallback(() => {
    if (costPriceOfDeliveryForm) {
      void form.validateFields().then((values) => {
        const { goodsList } = values;
        const newList = (goodsList as NewResultGoodsDTO[])?.map((item) => {
          const { rowNo, handWriteCostPrice } = item;
          return {
            rowNo,
            handWriteCostPrice,
          };
        });
        onSaveContinue?.(newList);
        onCancel();
      });
    } else {
      onSaveContinue?.();
      onCancel();
    }
  }, [costPriceOfDeliveryForm, form, onCancel, onSaveContinue]);

  /**手工修改单据编号 */
  const handleEditReceiptNo = useCallback(() => {
    onCancel();
    onEditReceiptNo?.();
  }, [onCancel, onEditReceiptNo]);

  /**生成新单据编号 */
  const handleCreateReceiptNo = useCallback(async () => {
    onCancel();
    if (onCreateReceiptNo) {
      await onCreateReceiptNo();
      onSaveContinue?.();
    }
  }, [onCancel, onCreateReceiptNo, onSaveContinue]);

  /** 跳转单据列表并带上单据编号 */
  const handleLinkToListPage = useCallback(
    (record: { billNo: string; billType: string }) => {
      const { billNo, billType } = record || {};
      const route =
        ReceiptListPageUrl[billType as keyof typeof ReceiptListPageUrl];
      if (billNo && route) {
        openTab(route, {
          tabSearch: { billNoList: [billNo] },
        });
      }
    },
    [openTab],
  );

  return {
    ...props,
    form,
    visible,
    receiptNoInfo,
    costPriceOfDeliveryForm,
    resultInfoList,
    canContinueSave,
    errorTitle,
    showModal,
    onCancel,
    handleContinue,
    handleEditReceiptNo,
    handleCreateReceiptNo,
    onRowDataChange,
    isShowTitle,
    handleLinkToListPage,
  };
};
