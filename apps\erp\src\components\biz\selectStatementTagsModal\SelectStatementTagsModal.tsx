import { BasicTagType, statementQueryTag } from '@slt/services';
import type { CommonRecord } from '@slt/base-utils';
import type { MutableRefObject } from 'react';
import type { BizTableRef } from '@slt/biz-ui';
import { Flex, msgInfo, Typography, type ModalProps } from '@slt/base-ui';
import { defaultEmptySelectMessage } from '@slt/utils';
import { SelectSystemsTagsModal } from '../selectSystemsTagsModal/SelectSystemsTagsModal';
import type { SelectSystemTagsParams } from '../selectSystemsTagsModal/types';

type SelectStatementTagsModalProps<T extends CommonRecord> = {
  /**
   * 是否展示系统标签
   * @default true
   */
  showSystemTags?: boolean;
  /** 使用场景 */
  scene?: 'select' | 'modify';
  tableRef: MutableRefObject<BizTableRef<T> | null>;
  onRemove?: (
    params: SelectSystemTagsParams,
    close: () => void,
  ) => void | Promise<void>;
  onOk: (
    params: SelectSystemTagsParams,
    close: () => void,
  ) => void | Promise<void>;
  modalProps?: Partial<ModalProps>;
  icon?: React.ReactNode;
  triggerTextClassName?: string;
};

export const SelectStatementTagsModal = <T extends CommonRecord>(
  props: SelectStatementTagsModalProps<T>,
) => {
  const {
    icon,
    scene = 'select',
    tableRef,
    modalProps,
    triggerTextClassName,
  } = props;

  const showModal = async (callback: () => void) => {
    const selectedRows = tableRef.current?.getSelectedRows().selectedRows;
    const { onBeforeTrigger } = modalProps || {};
    if (onBeforeTrigger) {
      const result = await onBeforeTrigger();
      if (!result) {
        return;
      }
    }

    if (selectedRows && selectedRows.length > 0) {
      callback?.();
    } else {
      msgInfo(defaultEmptySelectMessage);
    }
  };
  return (
    <>
      <SelectSystemsTagsModal
        {...props}
        scene={scene}
        tagType={BasicTagType.对账标签}
        title="修改对账标签"
        trigger={(onClick) => {
          return (
            <Flex>
              {icon}
              <Typography.Link
                onClick={() => showModal(onClick)}
                className={triggerTextClassName}
              >
                修改对账标签
              </Typography.Link>
            </Flex>
          );
        }}
        getRequest={statementQueryTag}
        modalProps={modalProps}
      />
    </>
  );
};
