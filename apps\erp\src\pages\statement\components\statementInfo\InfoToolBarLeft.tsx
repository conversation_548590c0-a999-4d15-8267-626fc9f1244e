import {
  ExclamationCircleFilled,
  Flex,
  IconFont,
  LinkButton,
  Modal,
  msgInfo,
  msgSuccess,
  Typography,
} from '@slt/base-ui';
import {
  statementAddDetail,
  statementDetailRestBillRemark,
  statementRemoveDetail,
  statementRestBillDetailRemark,
  statementRestBillRemark,
  statementRestBillTag,
  statementRestOtherBillRemark,
  type ResponseDetailData,
} from '@slt/services';
import { useMutation } from '@slt/net-query';
import { StatementInfoTypeEnum, type InfoToolBarLeftProps } from '../../types';
import type { CommonRecord } from '@slt/base-utils';
import { defaultEmptySelectMessage, StatementBillStatusType } from '@slt/utils';
import { SelectStatementModal } from '@/components/biz/commonBillSelectModal/selectStatementModal/SelectStatementModal';
import { useStatementDetailContextContext } from '../../utils/PageContext';
import { useCallback, useMemo } from 'react';
import { BatchDropdown } from '@slt/biz-ui';
import { EditRemark, OrderRangeTypeEnum } from '../modal/EditRemark';
import {
  SelectStatementTagsModal,
  type SelectSystemTagsParams,
} from '@/components/biz';

const { Link } = Typography;

enum UpdateTagOperateType {
  ADD = 1,
  DELETE = 2,
}

export const InfoToolBarLeft = <T extends CommonRecord, P extends CommonRecord>(
  props: InfoToolBarLeftProps<T, P>,
) => {
  const {
    activeKey,
    queryParams,
    infoType,
    type,
    tableRef,
    billTypes,
    needDelete = true,
    showBatch = true,
    showTag = true,
    showBillRemark = true,
    showDetailRemark = true,
    getHeaderStatistics,
  } = props;
  const { billId, billInfoData, reload, handleCheckRemove } =
    useStatementDetailContextContext();
  const { mutate: mutateStatementRemoveDetail } = useMutation(
    statementRemoveDetail,
  );
  const { mutate: mutateStatementAddDetail } = useMutation(statementAddDetail);
  const { mutate: mutateStatementRestBillTag } =
    useMutation(statementRestBillTag);

  const extraParams = useMemo(
    () => ({
      customerIds: billInfoData?.merchantId
        ? [billInfoData.merchantId]
        : undefined,
    }),
    [billInfoData],
  );

  const showBillOperation = useMemo(() => {
    return (
      infoType === StatementInfoTypeEnum.详情 &&
      billInfoData?.statementStatus === StatementBillStatusType['待核实']
    );
  }, [infoType, billInfoData?.statementStatus]);

  const remarkRequest = useMemo(() => {
    switch (activeKey) {
      case 'goodDetail':
        return statementDetailRestBillRemark;
      case 'billDetail':
        return statementRestBillRemark;
      case 'other':
        return statementRestOtherBillRemark;
      default:
        return;
    }
  }, [activeKey]);

  const handleUpdateTags = useCallback(
    async (
      operateType: UpdateTagOperateType,
      selectTags: SelectSystemTagsParams,
      close: () => void,
    ) => {
      const { tagNames } = selectTags;
      const ids = tableRef.current?.getSelectedRows().selectedRowKeys || [];
      const res = await mutateStatementRestBillTag({
        contrastIdList: ids,
        // 操作类型,1:新增,2:删除
        operateType,
        statementId: billId,
        tagList: tagNames,
      });
      if (res.success) {
        msgSuccess('修改成功');
        tableRef.current?.reload();
        close();
      }
    },
    [mutateStatementRestBillTag, tableRef, billId],
  );

  const getDetailExtraParams = useCallback(
    (operateType: OrderRangeTypeEnum) => {
      const selectedRows =
        tableRef.current?.getSelectedRows().selectedRows ||
        ([] as CommonRecord[]);
      const detailUpdateDTOList = selectedRows
        .filter((item) => item.billType && item.id)
        .map((item) => ({
          detailId: item.detailId as string,
          billType: item.billType as string,
        }));
      return {
        statementId: billId,
        detailUpdateDTOList,
        goodsWithDetailQuery:
          operateType === OrderRangeTypeEnum.根据查询条件
            ? queryParams
            : undefined,
      };
    },
    [tableRef, billId, queryParams],
  );

  const getBillExtraParams = useCallback(
    (operateType: OrderRangeTypeEnum) => {
      // NOTE：在退货明细中，rowKey是detailId，所以这里要单独取id
      const ids =
        tableRef.current
          ?.getSelectedRows()
          .selectedRows.map((item) => item?.id) || [];
      const _params =
        operateType === OrderRangeTypeEnum.根据查询条件
          ? queryParams
          : undefined;
      switch (activeKey) {
        case 'goodDetail':
          return {
            statementId: billId,
            contrastIdList: ids,
            goodsWithDetailQuery: _params,
          };
        case 'billDetail':
          return {
            statementId: billId,
            contrastIdList: ids,
            goodsReceivableQuery: _params,
          };
        case 'other':
          return {
            statementId: billId,
            contrastIdList: ids,
            otherReceivableQuery: _params,
          };
        default:
          return queryParams || {};
      }
    },
    [activeKey, billId, queryParams, tableRef],
  );

  const batchDropdownList = useMemo(() => {
    if (!remarkRequest) {
      return [];
    }

    const menuItems = [
      {
        key: 'tag',
        label: (
          <SelectStatementTagsModal
            icon={
              <IconFont
                type="icon-xiugai_duizhangdan"
                className="mr-xxs text-base text-primary"
              />
            }
            triggerTextClassName="text-base"
            tableRef={tableRef}
            scene="modify"
            showSystemTags={false}
            onOk={(selectTags, close) =>
              handleUpdateTags(UpdateTagOperateType.ADD, selectTags, close)
            }
            onRemove={(selectTags, close) =>
              handleUpdateTags(UpdateTagOperateType.DELETE, selectTags, close)
            }
            modalProps={{
              onBeforeTrigger: async () => {
                const isDelete = await handleCheckRemove();
                return !isDelete;
              },
            }}
          />
        ),
        show: showTag,
      },
      // NOTE:这个只有商品应收——商品销售及退货明细才有
      {
        key: 'detailRemark',
        label: (
          <EditRemark
            icon={
              <IconFont
                type="icon-xiugai_mingxibeizhu"
                className="mr-xxs text-base text-primary"
              />
            }
            triggerTextClassName="text-base"
            request={statementRestBillDetailRemark}
            tableRef={tableRef}
            getExtraParams={getDetailExtraParams}
            title="修改明细备注"
          />
        ),
        show: showDetailRemark,
      },
      {
        key: 'billRemark',
        label: (
          <EditRemark
            icon={
              <IconFont
                type="icon-xiugai_danjubeizhu"
                className="mr-xxs text-base text-primary"
              />
            }
            triggerTextClassName="text-base"
            request={remarkRequest}
            tableRef={tableRef}
            getExtraParams={getBillExtraParams}
            title="修改单据备注"
          />
        ),
        show: showBillRemark,
      },
    ];

    return menuItems.filter((item) => item.show).map((item) => item.label);
  }, [
    showTag,
    showDetailRemark,
    showBillRemark,
    tableRef,
    handleUpdateTags,
    remarkRequest,
    handleCheckRemove,
    getDetailExtraParams,
    getBillExtraParams,
  ]);

  // 删除单据
  const handleDeleteBill = async () => {
    const isDelete = await handleCheckRemove();
    if (isDelete) {
      return;
    }

    const selectedRowKeys =
      tableRef.current?.getSelectedRows().selectedRowKeys || [];
    if (selectedRowKeys.length === 0) {
      msgInfo(defaultEmptySelectMessage);
      return;
    }

    Modal.confirm({
      title: '提示',
      icon: <ExclamationCircleFilled />,
      content: '是否确认将所勾选单据移除对账单',
      onOk() {
        void mutateStatementRemoveDetail({
          financeStatementId: billId,
          contrastBillIds: selectedRowKeys,
        }).then((res) => {
          if (res.success) {
            msgInfo('删除成功');
            tableRef.current?.reload();
            void reload?.();
            getHeaderStatistics?.();
          }
        });
      },
    });
  };

  const handleAddBill = (selectedRows: ResponseDetailData[]) => {
    const selectedRowKeys = selectedRows.map((item) => item.id);

    void mutateStatementAddDetail({
      financeStatementId: billId,
      contrastBillIds: selectedRowKeys,
    }).then((res) => {
      if (res.success) {
        msgSuccess('添加成功');
        tableRef.current?.reload();
        void reload?.();
        getHeaderStatistics?.();
      }
    });
  };

  return (
    <Flex gap={12} align="center">
      {showBillOperation ? (
        <>
          <SelectStatementModal
            billTypes={billTypes}
            type={type}
            afterOk={handleAddBill}
            extraParams={extraParams}
            modalProps={{
              onBeforeTrigger: async () => {
                const isDelete = await handleCheckRemove();
                return !isDelete;
              },
            }}
          />
          {needDelete ? (
            <LinkButton sceneType="delete" onClick={handleDeleteBill}>
              移除单据
            </LinkButton>
          ) : null}
        </>
      ) : null}
      {showBatch && infoType !== StatementInfoTypeEnum.分享
        ? batchDropdownList
        : null}
    </Flex>
  );
};
