import { useActions } from '@/store/system';
import { GlobalPollingTaskEnum } from '@/store/type';
import {
  Flex,
  Form,
  Input,
  Modal,
  msgSuccess,
  useAuthCheck,
} from '@slt/base-ui';
import type { CommonRecord } from '@slt/base-utils';
import { useMutation } from '@slt/net-query';
import {
  financeAdvanceFinanceBillDelReview,
  financeReceiveAndPaymentBillReviewV2,
  stockReceiveAndPaymentBillQueryTaskProgressUrl,
  type FinanceBillReviewParams,
} from '@slt/services';
import {
  BillOriginType,
  FinanceBillStatusType,
  SynergyStatusType,
} from '@slt/utils';
import { isNil } from 'lodash-es';
import { useCallback, useMemo } from 'react';

export type BillSynergyInfoDTO = {
  synergyStatus?: SynergyStatusType; // 协同状态
  rejectReason?: string; // 审核不通过原因
};

export type BillOperateBtnShowConfDTO = {
  synergyAudit: boolean;
  add: boolean;
  edit: boolean;
  synergyTempSave: boolean;
  saveDraft: boolean;
  save: boolean;
  settleByOrder: boolean;
};

export type BillActionType = 'add' | 'edit' | 'view' | 'settle';

type BillOperateBtnType = 'add' | 'edit' | 'synergyAudit' | 'settle';

type Props = {
  type: BillActionType;
  /** 是否预收付款单据
   * @default false
   */
  isAdvanceBill?: boolean;
  /** 是否为预付款单/付款单 */
  isPaymentBill: boolean;
  /** 单据id */
  billId: string;
  /** 协同状态 */
  synergyStatus?: SynergyStatusType;
  /** 单据详情 */
  initialData?: CommonRecord;
  /** 协同单据审核通过提示文案 */
  passText: string;
  /** 协同单据审核拒绝提示文案 */
  rejectText: string;
  /** 操作按钮权限id */
  authIdConf: Record<BillOperateBtnType, string>;
  /** 协同单据审核拒绝 */
  onReload?: () => void;
  /** 协同单据审核前校验 */
  onCheckBeforeReview?: (params: FinanceBillReviewParams) => Promise<boolean>;
};

type RejectFormValues = {
  rejectReason: string;
};

/**
 * primary按钮优先级
 * 1. 详情页：审核通过 → 按单结算 → 新增 → 编辑
 * 2. 编辑协同单据：保存 → 暂存
 * 3. 新增/编辑单据：保存 → 保存草稿
 */
const btnPrimaryOrder = [
  'synergyAudit',
  'settleByOrder',
  'add',
  'edit',
  'save',
  'synergyTempSave',
  'saveDraft',
];

export const useBillOperate = (props: Props) => {
  const {
    type,
    billId,
    passText,
    rejectText,
    synergyStatus,
    initialData,
    isAdvanceBill = false,
    isPaymentBill,
    authIdConf,
    onReload,
    onCheckBeforeReview,
  } = props;
  const { checkAuth } = useAuthCheck();
  const { startPolling } = useActions();

  const [rejectForm] = Form.useForm<RejectFormValues>();

  const { mutate: auditSynergyBillRunAsync } = useMutation(
    isAdvanceBill
      ? financeAdvanceFinanceBillDelReview
      : financeReceiveAndPaymentBillReviewV2,
  );

  const isDraftBill = useMemo(
    () => initialData?.billStatus === FinanceBillStatusType['草稿'],
    [initialData?.billStatus],
  );
  /** 单据是否为暂存状态(协同单据才有暂存状态) */
  const isTempSaveBill = useMemo(
    () => initialData?.billStatus === FinanceBillStatusType['暂存'],
    [initialData?.billStatus],
  );

  const isSynergyCreate = useMemo(
    () => initialData?.createType === BillOriginType['协同创建'],
    [initialData?.createType],
  );
  const isSynergyBill = useMemo(
    () => !isNil(synergyStatus) || isSynergyCreate,
    [synergyStatus, isSynergyCreate],
  );
  const isSynergyAuditReject = useMemo(
    () => synergyStatus === SynergyStatusType['审核不通过'],
    [synergyStatus],
  );
  const isSynergyWaitAudit = useMemo(
    () => synergyStatus === SynergyStatusType['待审核'],
    [synergyStatus],
  );

  /** 审核当前协同单据的账号的角色 */
  const roleTextDuringAudit = useMemo(() => {
    if (isSynergyCreate) {
      // 单据为协同创建
      return isPaymentBill ? '客户' : '供应商';
    } else {
      // 单据为手动创建
      return isPaymentBill ? '供应商' : '客户';
    }
  }, [isSynergyCreate, isPaymentBill]);

  const { billBtnShowMap, primaryBtn } = useMemo(() => {
    const isDetail = type === 'view';
    const isEdit = type === 'edit';
    const isAdd = type === 'add';
    const isSettle = type === 'settle';
    const synergyShowEdit = isTempSaveBill && isSynergyAuditReject;
    const alwaysShowSave = isAdd || isSettle;

    const billBtnShowMap: BillOperateBtnShowConfDTO = {
      /** 审核按钮(通过/拒绝)
       * 1. 详情页，协同创建的待审核的协同单据
       */
      synergyAudit:
        isDetail &&
        isSynergyWaitAudit &&
        isSynergyCreate &&
        checkAuth(authIdConf.synergyAudit ?? ''),
      /** 新增按钮
       * 1. 详情页
       */
      add: isDetail && checkAuth(authIdConf.add ?? ''),
      /** 编辑按钮
       * 1. 详情页，非协同单据 或 暂存的协同单据，有数据，存在异常情况进入页面没有数据
       */
      edit:
        isDetail &&
        (!isSynergyBill || isTempSaveBill) &&
        checkAuth(authIdConf.edit ?? '') &&
        !!initialData,
      /** 暂存按钮
       * 1. 编辑协同单据
       */
      synergyTempSave: isEdit && isTempSaveBill,
      /** 保存草稿按钮
       * 1. 新增非协同单据
       * 2. 编辑非协同单据
       */
      saveDraft: isAdd || (isEdit && isDraftBill),
      /**
       * 保存按钮
       * 1. 新增单据
       * 2. 按单结算单据
       * 3. 编辑单据(非协同单据 || 审核不通过的暂存的协同单据)，有数据(存在异常情况进入编辑页面没有数据)
       */
      // save: !isSynergyBill && (isAdd || ((isEdit || isSettle) && !!initialData)),
      save:
        alwaysShowSave ||
        (isEdit && !!initialData && (!isSynergyBill || synergyShowEdit)),
      /** 按单结算按钮
       * 1. 详情页，允许按单结算
       */
      settleByOrder:
        isDetail &&
        !!initialData?.allowSettlementByOrder &&
        checkAuth(authIdConf?.settle ?? ''),
    };

    const primaryBtn = btnPrimaryOrder.find(
      (r) => !!billBtnShowMap[r as keyof BillOperateBtnShowConfDTO],
    );
    return { billBtnShowMap, primaryBtn };
  }, [
    type,
    isTempSaveBill,
    isSynergyAuditReject,
    isSynergyWaitAudit,
    isSynergyCreate,
    checkAuth,
    authIdConf.synergyAudit,
    authIdConf.add,
    authIdConf.edit,
    authIdConf?.settle,
    isSynergyBill,
    initialData,
    isDraftBill,
  ]);

  const handleReviewBill = useCallback(
    (
      params: FinanceBillReviewParams,
      resolve: (value: boolean) => void,
      reject: () => void,
    ) => {
      void auditSynergyBillRunAsync(params).then((res) => {
        if (res.success) {
          // todo: 类型问题、稍后修复
          const { taskId } = res.data;
          if (taskId) {
            // 说明执行的是异步任务
            startPolling({
              request: {
                url:
                  stockReceiveAndPaymentBillQueryTaskProgressUrl + `/${taskId}`,
                method: 'get',
              },
              taskId: taskId,
              taskType: GlobalPollingTaskEnum.RECEIVEANDPAY,
              interval: 2000,
              successCondition: [
                {
                  field: 'data.taskStatus',
                  value: 2,
                },
                {
                  field: 'data.taskStatus',
                  value: 3,
                },
              ],
              msgConfig: {
                operateType: 'examine',
              },
            });
          }

          msgSuccess('操作成功');
          onReload?.();
          resolve(true);
        } else {
          reject();
        }
      });
    },
    [auditSynergyBillRunAsync, onReload, startPolling],
  );

  const handleSynergyPass = useCallback(() => {
    Modal.confirm({
      title: '提示',
      content: `确认审核通过？${passText}`,
      onOk: () => {
        return new Promise((resolve, reject) => {
          const params = { id: billId, status: SynergyStatusType['审核通过'] };
          if (onCheckBeforeReview) {
            void onCheckBeforeReview(params).then((checkRes) => {
              if (checkRes) {
                handleReviewBill(params, resolve, reject);
              } else {
                resolve(true);
              }
            });
          } else {
            handleReviewBill(params, resolve, reject);
          }
        });
      },
    });
  }, [passText, billId, onCheckBeforeReview, handleReviewBill]);

  const handleSynergyReject = useCallback(() => {
    Modal.confirm({
      title: '提示',
      content: (
        <Flex vertical gap={16}>
          <span>{rejectText}请务必告知其原因。</span>
          <Form enableUnsavedWarning={false} form={rejectForm}>
            <Form.Item
              name="rejectReason"
              label="拒绝原因"
              rules={[{ required: true, message: '请输入' }]}
            >
              <Input.TextArea
                maxLength={50}
                autoSize={{ minRows: 4, maxRows: 4 }}
              />
            </Form.Item>
          </Form>
        </Flex>
      ),
      onCancel: () => {
        rejectForm.resetFields();
      },
      onOk: () => {
        return new Promise((resolve, reject) => {
          rejectForm
            .validateFields()
            .then((values) => {
              const params = {
                id: billId,
                rejectReason: values.rejectReason,
                status: SynergyStatusType['审核不通过'],
              };

              if (onCheckBeforeReview) {
                void onCheckBeforeReview(params).then((checkRes) => {
                  if (checkRes) {
                    handleReviewBill(params, resolve, reject);
                  } else {
                    resolve(true);
                  }
                });
              } else {
                handleReviewBill(params, resolve, reject);
              }
            })
            .catch(() => {
              reject(false);
            });
        });
      },
    });
  }, [rejectText, rejectForm, billId, onCheckBeforeReview, handleReviewBill]);

  return {
    primaryBtn,
    billBtnShowMap,
    isSynergyWaitAudit,
    isSynergyAuditReject,
    roleTextDuringAudit,
    handleSynergyPass,
    handleSynergyReject,
  };
};
