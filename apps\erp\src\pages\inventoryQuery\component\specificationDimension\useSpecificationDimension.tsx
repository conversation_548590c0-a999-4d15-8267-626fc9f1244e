import { useOpenTab } from '@/hooks/useOpenTab';
import { useTabRefresh } from '@/hooks/useTabRefresh';
import { routerMap } from '@/router/routerMap';
import type { CollectionProps, TableColumnType } from '@slt/base-ui';
import {
  Flex,
  Form,
  GoodsImage,
  inputNumberProps,
  msgInfo,
  SysTooltip,
  Typography,
  useAppContext,
} from '@slt/base-ui';
import type { CommonRecord } from '@slt/base-utils';
import { type BizTableRef, type QueryFormListItem } from '@slt/biz-ui';
import { useMutation } from '@slt/net-query';
import {
  bossBookPublisherSelectInputAll,
  inventoryInfoTotalData,
  type SpecificationDimensionParams,
  type SpecificationDimensionTableListDTO,
  type SummaryDataType,
} from '@slt/services';
import { isNil } from 'lodash-es';
import { useCallback, useMemo, useRef, useState } from 'react';
import { DimensionType, NumberFilterType } from '../../type';
import { SelectionModalTabsType } from '@/components/biz';
import type { SpecificationDimensionForm } from './types';
import { enumToOptions } from '@slt/utils';

const { Link } = Typography;

export const useSpecificationDimension = () => {
  /**table表的合计项 */
  const [summaryData, setSummaryData] = useState<SummaryDataType>({});

  const tableRef =
    useRef<BizTableRef<SpecificationDimensionTableListDTO>>(null);
  useTabRefresh({
    refreshFunc: () => {
      tableRef.current?.reload();
    },
  });

  const { openTab } = useOpenTab();
  const [form] = Form.useForm<SpecificationDimensionForm>();
  const { authIds } = useAppContext();

  const initialValues = useMemo(() => {
    return {
      dimension: DimensionType.规格维度,
      separateWarehouse: false,
    };
  }, []);

  const { mutate: getTotal } = useMutation(inventoryInfoTotalData);

  const getWareInfo = useCallback(() => {
    // 1、获取当前选择的仓库
    const { warehouseIdInfo } = form.getFieldsValue();

    // 2、将值进行encodeURIComponent
    if (warehouseIdInfo) {
      const filterWarehouseList = warehouseIdInfo.map((v) => {
        return { label: v.label, value: v.value };
      });
      return {
        warehouseId: encodeURIComponent(JSON.stringify(filterWarehouseList)),
      };
    } else {
      return {};
    }
  }, [form]);

  /**
   * @description: 点击分仓库查询==>table表的下钻页面
   * @param {string} type 实际的tab页面
   * @param {any} params 跳转携带的url参数
   * @return {*}
   */
  const openType = useCallback(
    (type: string, params?: CommonRecord) => {
      switch (type) {
        case '实际库存':
          openTab(routerMap.inventoryWarehouseQueryByActual.route, {
            search: {
              ...params,
              ...getWareInfo(),
            },
          });
          break;
        case '可销售库存':
          openTab(routerMap.inventoryWarehouseQueryByMarketable.route, {
            search: {
              ...params,
              ...getWareInfo(),
            },
          });
          break;
        case '可发货库存':
          openTab(routerMap.inventoryWarehouseQueryByCanBeShipped.route, {
            search: {
              ...params,
              ...getWareInfo(),
            },
          });
          break;
        case '分仓明细':
          openTab(routerMap.inventoryWarehouseQuery.route, {
            search: {
              ...params,
              ...getWareInfo(),
            },
          });
          break;
        default:
          break;
      }
    },
    [openTab, getWareInfo],
  );

  /**
   * @description: 可销售库存的title的render
   * 同时提供了一个链接，用户点击后可快速跳转到系统配置页面查看库存设置。
   * @param {*}
   * @return {*}
   */
  const canSaleStockTitle = useCallback(() => {
    const goSystemConfiguration = () => {
      if (authIds?.includes('2033')) {
        openTab(routerMap.systemConfiguration.route, {
          search: {
            activeKey: 'inventorySetting',
          },
        });
      } else {
        msgInfo('没有库存设置权限');
      }
    };

    return (
      <div>
        根据设置-系统配置-库存设置中的可销售库存配置的公式进行计算,
        <Link onClick={goSystemConfiguration}>快速查看</Link>
      </div>
    );
  }, [authIds, openTab]);

  const nameOptions: CollectionProps['nameOptions'] = useMemo(() => {
    return [
      {
        type: 'input',
        formItemProps: {
          label: '快速查询',
          name: 'commonQuery',
        },
        fieldProps: {
          placeholder: `请输入商品名称/规格名称/商品编码/规格编码/ISBN查询`,
          allowClear: true,
        },
      },
      {
        type: 'batchInput',
        formItemProps: {
          label: '商品编码',
          name: 'goodsSpuNoList',
        },
        fieldProps: {
          placeholder: `请输入商品编码`,
          allowClear: true,
          showConfig: false,
        },
      },
      {
        type: 'batchInput',
        formItemProps: {
          label: '规格编码',
          name: 'goodsSkuNoList',
        },
        fieldProps: {
          placeholder: '请输入规格编码',
          allowClear: true,
          showConfig: false,
        },
      },
      {
        type: 'batchInput',
        formItemProps: {
          label: 'ISBN',
          name: 'isbnList',
        },
        fieldProps: {
          placeholder: '请输入ISBN',
          allowClear: true,
          showConfig: false,
        },
      },
    ];
  }, []);

  const searchColumns: QueryFormListItem[] = useMemo(() => {
    return [
      {
        type: 'collection',
        fieldProps: {
          nameOptions: nameOptions,
        },
      },
      {
        key: 'warehouseIdInfo',
        type: 'warehouseSelect',
        fieldProps: {
          mode: 'multiple',
          allowClear: true,
          manual: true,
          labelInValue: true,
          fieldNames: {
            label: 'label',
            value: 'value',
          },
        },
        formItemProps: {
          label: '仓库',
          name: 'warehouseIdInfo',
        },
      },
      {
        type: 'goodsTableSelect',
        key: 'goodsSkuIdList',
        fieldProps: {
          showDisabled: true,
          fieldNames: { value: 'id' },
          acceptGoodsTabs: [SelectionModalTabsType['普通自有']],
        },
        formItemProps: { label: '商品', name: 'goodsSkuIdList' },
      },
      {
        type: 'select',
        fieldProps: {
          options: [
            { label: '启用', value: true },
            { label: '停用', value: false },
          ],
        },
        formItemProps: {
          label: '商品状态',
          name: 'enable',
        },
      },
      {
        type: 'select',
        fieldProps: {
          options: enumToOptions(NumberFilterType),
        },
        formItemProps: {
          label: '数量过滤',
          name: 'numberFilterType',
        },
      },
      {
        key: 'stock',
        type: 'rangeInputNumber',
        fieldProps: {
          fieldNames: {
            start: 'stockMin',
            end: 'stockMax',
          },
          min: 0,
          ...inputNumberProps.int,
        },
        formItemProps: {
          label: '实际库存',
          name: 'stock',
        },
      },
      {
        key: 'canSaleStock',
        type: 'rangeInputNumber',
        fieldProps: {
          fieldNames: {
            start: 'canSaleStockMin',
            end: 'canSaleStockMax',
          },
          min: 0,
          ...inputNumberProps.int,
        },
        formItemProps: {
          label: '可销售库存',
          name: 'canSaleStock',
        },
      },
      {
        key: 'price',
        type: 'rangeInputNumber',
        fieldProps: {
          fieldNames: {
            start: 'priceMin',
            end: 'priceMax',
          },
          min: 0,
          ...inputNumberProps.price,
        },
        formItemProps: {
          label: '定价',
          name: 'price',
        },
      },
      {
        type: 'apiSelect',
        fieldProps: {
          mode: 'multiple',
          fieldNames: { label: 'key', value: 'value' },
          request: bossBookPublisherSelectInputAll,
        },
        formItemProps: {
          label: '出版社',
          name: 'publisherIdList',
        },
      },
    ];
  }, [nameOptions]);

  const tableColumns = useMemo(() => {
    const columns: TableColumnType<SpecificationDimensionTableListDTO>[] = [
      {
        title: '图片',
        key: 'image',
        render: (img?: string) => {
          if (!img) {
            return null;
          }

          return <GoodsImage src={img} size="small" />;
        },
      },
      {
        title: '商品名称',
        key: 'goodsSpuName',
      },
      {
        title: '商品编码',
        key: 'goodsSpuNo',
      },
      {
        title: '规格编码',
        key: 'goodsSkuNo',
      },
      {
        title: '供应商',
        key: 'supplierName',
      },
      {
        title: '规格名称',
        key: 'goodsSkuName',
      },
      {
        width: 'number',
        title: '定价',
        key: 'pricingPrice',
      },
      {
        title: '出版社',
        key: 'publisherName',
      },
      {
        title: 'ISBN',
        key: 'isbn',
      },

      {
        width: 'number',
        title: '实际库存',
        key: 'stock',
        render: (_, record) => {
          return record.stock ? (
            <Link
              authId={{
                authId: '1015',
                authType: 'text',
              }}
              onClick={() => {
                openType('实际库存', {
                  skuId: record.skuId,
                  goodsSpuName: record.goodsSpuName,
                });
              }}
            >
              {record.stock}
            </Link>
          ) : (
            '-'
          );
        },
      },
      {
        width: 'number',
        title: '成本均价',
        key: 'costAmount',
      },
      {
        width: 'number',
        title: '库存余额',
        key: 'totalAmount',
      },
      {
        title: '可销售库存',
        key: 'canSaleStock',
        titleRender: (title) => {
          return (
            <Flex gap={4}>
              <Typography.Text>{title}</Typography.Text>
              <SysTooltip title={canSaleStockTitle()} />
            </Flex>
          );
        },
        render: (_, record) => {
          return !isNil(record.canSaleStock) ? (
            <Typography.Link
              authId={{
                authId: '1016',
                authType: 'text',
              }}
              onClick={() => {
                openType('可销售库存', {
                  skuId: record.skuId,
                  goodsSpuName: record.goodsSpuName,
                  goodsSkuName: record.goodsSkuName,
                  goodsSkuNo: record.goodsSkuNo,
                  ...getWareInfo(),
                });
              }}
            >
              {record.canSaleStock}
            </Typography.Link>
          ) : (
            '-'
          );
        },
      },
      {
        width: 'number',
        title: '可发货库存',
        key: 'canDeliverStock',
        render: (_, record) => {
          return !isNil(record.canDeliverStock) ? (
            <Typography.Link
              authId={{
                authId: '1017',
                authType: 'text',
              }}
              onClick={() => {
                openType('可发货库存', {
                  skuId: record.skuId,
                  goodsSpuName: record.goodsSpuName,
                  goodsSkuName: record.goodsSkuName,
                  goodsSkuNo: record.goodsSkuNo,
                  ...getWareInfo(),
                });
              }}
            >
              {record.canDeliverStock}
            </Typography.Link>
          ) : (
            '-'
          );
        },
      },
      {
        width: 'number',
        title: '昨日销量',
        key: 'yesterdaySales',
        tooltips:
          '按单据日期统计前一天已确认的销售出库单，存在1天延迟（不包含今天新增的销售出库单）',
      },
      {
        width: 'number',
        title: '7天销量',
        key: 'weekSales',
        tooltips:
          '按单据日期统计前7天已确认的销售出库单，存在1天延迟（不包含今天新增的销售出库单）',
      },
      {
        width: 'number',
        title: '30天销量',
        key: 'monthSales',
        tooltips:
          '按单据日期统计前30天已确认的销售出库单，存在1天延迟（不包含今天新增的销售出库单）',
      },
      {
        width: 'remark',
        title: '昨日退货量',
        key: 'yesterdayReturn',
        tooltips:
          '按单据日期统计前一天已确认的销售退货单，存在1天延迟（不包含今天新增的销售退货单）',
      },
      {
        width: 'number',
        title: '7天退货量',
        key: 'weekReturn',
        tooltips:
          '按单据日期统计前7天已确认的销售退货单，存在1天延迟（不包含今天新增的销售退货单）',
      },
      {
        width: 'remark',
        title: '30天退货量',
        key: 'monthReturn',
        tooltips:
          '按单据日期统计前30天已确认的销售退货单，存在1天延迟（不包含今天新增的销售退货单',
      },
      {
        width: 'action',
        title: '操作',
        key: 'action',
        fixed: 'right',
        needExport: false,
        render: (_, record) => {
          return (
            <Link
              onClick={() => {
                openType('分仓明细', {
                  skuId: record.skuId,
                  goodsSkuName: record.goodsSkuName,
                });
              }}
            >
              分仓明细
            </Link>
          );
        },
      },
    ];

    return columns;
  }, [canSaleStockTitle, getWareInfo, openType]);

  const getTotalData = useCallback(
    async (params: CommonRecord) => {
      const res = await getTotal(params as SpecificationDimensionParams);
      if (res?.success && res?.data) {
        const {
          totalStock: stock,
          totalCanSaleStock: canSaleStock,
          totalPageAmount: totalAmount,
          totalCanDeliveryStock: canDeliverStock,
          yesterdaySales,
          weekSales,
          monthSales,
          yesterdayReturn,
          weekReturn,
          monthReturn,
        } = res.data;

        setSummaryData({
          stock,
          canSaleStock,
          totalAmount,
          canDeliverStock,
          yesterdaySales,
          weekSales,
          monthSales,
          yesterdayReturn,
          weekReturn,
          monthReturn,
        });
      }
    },
    [setSummaryData, getTotal],
  );

  /**发送请求前的参数处理 */
  const getSearchParam = useCallback((params: SpecificationDimensionForm) => {
    const newParams: SpecificationDimensionForm = {
      ...params,
    };

    if (newParams?.warehouseIdInfo) {
      newParams.warehouseIdList = newParams?.warehouseIdInfo?.map(
        (v) => v.value,
      );
      delete newParams?.warehouseIdInfo;
    }

    return newParams;
  }, []);

  /**发送请求前的参数处理 */
  const beforeSearchData = useCallback(
    (params: SpecificationDimensionForm) => {
      const newParams = getSearchParam(params);
      getTotalData(newParams);
      return newParams;
    },
    [getSearchParam, getTotalData],
  );

  const getExportParams = useCallback(() => {
    const param = getSearchParam(form.getFieldsValue());
    const { goodsSkuIdList = [] } = param;
    const goodsSkuIdListInfo = goodsSkuIdList.length
      ? goodsSkuIdList.map((v) => v.value)
      : [];
    const params = { ...param, goodsSkuIdList: goodsSkuIdListInfo };
    return {
      queryCondition: { ...params, ...initialValues },
    };
  }, [form, initialValues, getSearchParam]);

  return {
    form,
    tableRef,
    initialValues,
    summaryData,
    searchColumns,
    tableColumns,
    beforeSearchData,
    getExportParams,
  };
};
