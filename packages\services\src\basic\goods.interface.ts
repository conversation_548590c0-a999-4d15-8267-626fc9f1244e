import type { GoodsMaterialBO } from './goods/goods.type';

export type ListSpuParams = {
  page?: number;
  pageSize?: number;
  goodsName?: string;
  categoryCode?: number;
  goodsNo?: string;
  isbn?: string;
  bookName?: string;
  availability?: number;
  goodsTypeId?: number;
  productTagId?: string;
  status?: number;
};

export type ListSpuDTO = {
  bookName?: string;
  createWay?: number;
  createUserName?: string;
  gmtCreate?: string;
  goodsName?: string;
  goodsSkuName?: string;
  goodsNo?: string;
  goodsId?: string;
  goodsTypeName?: string;
  id: string;
  isbn?: string;
  mainPicUrlList?: {
    url?: string;
    orderDesc?: number;
  }[];
  pricingPrice?: number;
  productTagName?: string;
  publisherName?: string;
  referenceCost?: number;
  status?: boolean;
  brandInfoList?: {
    brandName?: string;
    brandId?: string;
  }[];
  tagInfoList?: {
    productTagName?: string;
    productTagId?: string;
  }[];
  resourceName?: string;
  specPicUrlList?: GoodsMaterialBO[];
  goodsShortName?: string;
  goodsRemarks?: string;
};
