// OrderStatus.ts
import type { SendInfo } from '@slt/services';
import { OrderStatus } from '../order/orderManagement/constants';

export type OrderAction =
  | 'Approve'
  | 'ReverseApprove'
  | 'ConvertToExceptionOrder'
  | 'CancelException'
  | 'CancelOrder'
  // | 'RevokeShipped'
  | 'ReverseCancelOrder'
  | 'Reshipment';

export const OrderActionsByStatus: Record<number, OrderAction[]> = {
  [OrderStatus.WaitPay]: [], // 待付款状态下可以支付订单
  [OrderStatus.WaitAudit]: [
    'Approve',
    'ConvertToExceptionOrder',
    'CancelOrder',
  ],
  [OrderStatus.ProcessingPayment]: [
    'ConvertToExceptionOrder',
    'CancelOrder',
    'ReverseApprove',
  ],
  [OrderStatus.Exception]: ['CancelOrder', 'CancelException'],
  [OrderStatus.Delivering]: [
    'ConvertToExceptionOrder',
    'CancelOrder',
    'ReverseApprove',
  ],
  [OrderStatus.Delivered]: ['Reshipment'],
  [OrderStatus.Cancel]: [], // 已取消状态下没有操作
};

// 新增 OrderAction 的中文映射
export const OrderActionTextMap: Record<OrderAction, string> = {
  Approve: '审核',
  ReverseApprove: '反审核',
  ConvertToExceptionOrder: '转异常',
  CancelException: '取消异常',
  CancelOrder: '取消订单',
  ReverseCancelOrder: '反取消订单',
  Reshipment: '重新发货',
  // RevokeShipped: '撤销已发货',
};

export const OrderDeadFlag: Record<string, string> = {
  1: 'red',
  2: 'orange',
};

export type ChangeGoodsListData = (
  index: number,
  flag: 'change' | 'delete',
  params?: Record<string, unknown>,
) => void;

export interface ReceiverFormValues {
  receiverName: string;
  receiverMobile: string;
  receiverTel: string;
  sendInfo: SendInfo;
}

export enum OrderSystemExceptionEnum {
  缺货 = '1',
  买家申请退款 = '3',
  测试订单 = '4',
  没有可发货商品 = '5',
  黑名单 = '6',
  不明确分销 = '8',
  暂停发货 = '9',
  供应商库存不足 = '10',
  换货未收到退货 = '12',
  线上发货 = '13',
  快递停发 = '14',
  其他 = '17',
  未匹配物流 = '19',
  店铺授权失效 = '20',
}
