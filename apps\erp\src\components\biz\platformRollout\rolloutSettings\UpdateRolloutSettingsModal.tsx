import {
  Button,
  Flex,
  Form,
  Modal,
  msgSuccess,
  Spin,
  Tabs,
  type ModalProps,
  type TabsProps,
} from '@slt/base-ui';
import { parseJSON, type CommonRecord } from '@slt/base-utils';
import { useMutation } from '@slt/net-query';
import {
  basicPlatformRolloutBaseConfGet,
  basicPlatformRolloutGoodsFixedDataUpdate,
  type BasicPlatformRolloutSupportTypesVO,
} from '@slt/services';
import {
  PlatformRolloutWayEnum,
  type PlatformRolloutConfValue,
} from '@slt/services';

import { PlatformTypeEnum } from '@slt/utils';
import {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { usePlatformRolloutModalContext } from '../contexts';

import PriceAndInventoryModal, {
  type InventoryAndPriceDataItem,
} from '../rolloutSettings/PriceAndInventoryModal';

import { freightTemplateListName } from '../rolloutSettings/constants';
import { useUpdateEffect } from 'ahooks';
import { TbContent } from '../rolloutSettings/TbContent';
import { CommonRolloutContent } from '../rolloutSettings/CommonContent';
import { convertToFixedSpuData } from '../executeRollout/util';
import { net } from '@slt/net';
import type { UpdateRolloutRecordedList } from '../types';
import { isEmpty, isNil } from 'lodash-es';
import { DyContent } from './DyContent';
import {
  formatYouZanAfterSaleServiceInitValue,
  formatYouZanAfterSaleServiceSubmit,
  formatYouZanAutoListingTimeSubmit,
  formatYouZanDeliverySettingInitValue,
  formatYouZanDeliverySettingSubmit,
} from '@/pages/storeSettingsDetail/components/Youzan/getFormConfig';

type Props = {
  maskArea: ModalProps['maskArea'];
  /** 铺货入口 */
  rolloutWay?: PlatformRolloutWayEnum;
  targetIds?: string[];
  onOk: (values: CommonRecord) => void;
};
type RolloutSettingsModalRef = {
  showModal: () => void;
  closeModal: () => void;
};

/**
 * 更新铺货的铺货设置
 */
export const UpdateRolloutSettingsModal = forwardRef<
  RolloutSettingsModalRef,
  Props
>((props, ref) => {
  const { maskArea, onOk, rolloutWay } = props;
  const [selectedType, setSelectedType] = useState<PlatformTypeEnum>();
  const priceAndInventoryModalRef = useRef(null);
  const [modalOpen, setModalOpen] = useState(false);

  const [tBaoForm] = Form.useForm<CommonRecord>();
  const [tMaoForm] = Form.useForm<CommonRecord>();
  const [jDongForm] = Form.useForm<CommonRecord>();
  const [dYinForm] = Form.useForm<CommonRecord>();
  const [kShouForm] = Form.useForm<CommonRecord>();
  const [pddForm] = Form.useForm<CommonRecord>();
  const [yZanForm] = Form.useForm<CommonRecord>();
  const [wXinForm] = Form.useForm<CommonRecord>();
  const [xhsForm] = Form.useForm<CommonRecord>();

  const [fixData, setFixData] = useState({});
  const {
    selectedPlatform,
    confirmUpdateProduct,
    setEditPriceSource,
    editPriceSource,
  } = usePlatformRolloutModalContext();

  //根据平台类型分组，查出对应的商品id
  const platformDistributionIdsByType = useMemo(() => {
    return confirmUpdateProduct.reduce((acc, item) => {
      const platformType = item.platformType;
      const platformDistributionId = item.platformDistributionId;

      if (!acc[platformType]) {
        acc[platformType] = [];
      }

      if (!acc[platformType].includes(platformDistributionId)) {
        acc[platformType].push(platformDistributionId);
      }

      return acc;
    }, {});
  }, [confirmUpdateProduct]);

  //根据平台类型分组，查出对应shopId
  const platformDistributionIdsByTypeShop = useMemo(() => {
    return confirmUpdateProduct.reduce((acc, item) => {
      const platformType = item.platformType;
      const shopId = item.shopId;
      const shopName = item.shopName;
      if (!acc[platformType]) {
        acc[platformType] = [];
      }

      if (!acc[platformType].includes(shopId)) {
        acc[platformType].push({
          shopId,
          shopName,
        });
      }

      return acc;
    }, {});
  }, [confirmUpdateProduct]);

  const platformDistributionIds = useMemo(() => {
    return [
      ...new Set(
        confirmUpdateProduct.map((item) => item.platformDistributionId),
      ),
    ];
  }, [confirmUpdateProduct]);

  const formRefs = useRef([]);

  const [loading, setLoading] = useState(false);

  const { mutate: mutateGetBaseConf } = useMutation(
    basicPlatformRolloutBaseConfGet,
  );

  const platformSettingList = useMemo(() => {
    const groupedData = confirmUpdateProduct
      ?.reduce((acc, item) => {
        const existingGroup = acc.find(
          (group) => group.platformInfo.key === item.platformType,
        );
        if (existingGroup) {
          existingGroup.shop.push({
            id: item.shopId,
            shopName: item.shopName,
          });
        } else {
          acc.push({
            platformInfo: {
              key: item.platformType,
              extra: item.platformLogoUrl,
              value: PlatformTypeEnum[item.platformType],
            },
            shop: [
              {
                id: item.shopId,
                ...item,
              },
            ],
          });
        }
        return acc;
      }, [])
      ?.map((platform) => {
        const uniqueShops = platform.shop.filter(
          (shop, index, self) =>
            index === self.findIndex((s) => s.id === shop.id),
        );

        return {
          ...platform,
          shop: uniqueShops,
        };
      });

    return groupedData;
  }, [confirmUpdateProduct]);

  const handleSaveAll = async () => {
    // 验证所有表单
    const validationResults = await Promise.all(
      formRefs.current.map(async (formItem) => {
        const isValid = await formItem.validate();
        return {
          key: formItem.key,
          isValid,
        };
      }),
    );

    const currentTabForm = formRefs.current.find(
      (formItem) => formItem.key === selectedType,
    );

    const isCurrentTabValid = currentTabForm
      ? await currentTabForm.validate()
      : true;

    // 如果当前tab有错误，只在当前tab显示错误
    if (!isCurrentTabValid) {
      return;
    }

    const invalidForms = validationResults.filter((result) => !result.isValid);

    if (invalidForms.length > 0) {
      // 找到第一个有错误的tab并切换到它
      const firstInvalidForm = invalidForms[0];

      setSelectedType(firstInvalidForm.key);
      return;
    }

    submitAllForms();
  };

  const formatValues = useCallback((values: CommonRecord, key: number) => {
    switch (Number(key)) {
      case PlatformTypeEnum['拼多多']: {
        return {
          ...values,
          shipmentLimitSecond: values.shipmentLimitSecond
            ? values.shipmentLimitSecond
            : undefined,
          deliveryOneDay: values.shipmentLimitSecond ? 0 : 1,
        };
      }
      case PlatformTypeEnum['小红书']: {
        return {
          ...values,
          deliveryTime: {
            time: values.deliveryTime ? `${values.deliveryTime}` : undefined,
            type: values.deliveryTime ? 'RELATIVE_TIME_NEW' : 'TODAY',
          },
        };
      }
      case PlatformTypeEnum['有赞']: {
        const {
          deliverySetting,
          afterSaleService,
          isDisplay,
          autoListingTime,
          ...restValues
        } = values;

        return {
          ...restValues,
          ...formatYouZanDeliverySettingSubmit(deliverySetting),
          ...formatYouZanAfterSaleServiceSubmit(afterSaleService),
          ...formatYouZanAutoListingTimeSubmit(
            'rollout',
            isDisplay,
            autoListingTime,
          ),
        };
      }
      default: {
        return values;
      }
    }
  }, []);

  const submitAllForms = async () => {
    const formValues = await Promise.all(
      formRefs.current.map(async (formItem) => {
        const values = await formItem.form.getFieldsValue(true);
        return {
          key: formItem.key,
          values: formatValues(values, formItem.key),
        };
      }),
    );
    const platformInfoKeys = platformSettingList.map((platform) =>
      Number(platform.platformInfo.key),
    );
    const newEditPriceSource = { ...editPriceSource };
    //在不是平台铺货资料的场景下：存在在选择店铺商品时，修改了价格，但最后在铺货之前未选择该平台或者该商品
    // 根据platformInfoKeys过滤newEditPriceSource中的platformType
    Object.keys(newEditPriceSource).forEach((platformType) => {
      // 如果platformInfoKeys中不包含当前platformType，则删除该platformType的数据
      if (!platformInfoKeys.includes(Number(platformType))) {
        delete newEditPriceSource[platformType];
      } else {
        // 如果platformInfoKeys中包含当前platformType，则检查goodsInfo.id
        const filteredGroups = [];

        for (const itemGroup of newEditPriceSource[platformType]) {
          const filteredGroup = {};
          Object.keys(itemGroup).forEach((goodsId) => {
            // 如果platformDistributionIds中包含当前goodsId，则保留该商品组
            if (platformDistributionIds.includes(goodsId.toString())) {
              filteredGroup[goodsId] = itemGroup[goodsId];
            }
          });

          if (Object.keys(filteredGroup).length > 0) {
            filteredGroups.push(filteredGroup);
          }
        }

        if (filteredGroups.length > 0) {
          newEditPriceSource[platformType] = filteredGroups;
        } else {
          delete newEditPriceSource[platformType];
        }
      }
    });

    let result = {};
    if (!isEmpty(newEditPriceSource)) {
      if (!isEmpty(fixData)) {
        result = { ...fixData };

        // 将newEditPriceSource中与fixData中键值不同的数据添加到result中
        Object.keys(newEditPriceSource).forEach((platformType) => {
          if (!fixData.hasOwnProperty(platformType)) {
            // 只有当fixData中不存在该platformType时，才添加newEditPriceSource中的数据
            const items = newEditPriceSource[platformType];
            const flattenedItems: any[] = [];
            items.forEach((itemGroup) => {
              Object.keys(itemGroup).forEach((goodsId) => {
                flattenedItems.push(...itemGroup[goodsId]);
              });
            });

            result[platformType] = flattenedItems;
          }
        });
      } else {
        result = Object.keys(newEditPriceSource).reduce(
          (acc, platformType) => {
            const items = newEditPriceSource[platformType];
            const flattenedItems: any[] = [];

            items.forEach((itemGroup) => {
              Object.keys(itemGroup).forEach((goodsId) => {
                flattenedItems.push(...itemGroup[goodsId]);
              });
            });

            acc[platformType] = flattenedItems;
            return acc;
          },
          {} as Record<string, any[]>,
        );
      }
    } else {
      result = { ...fixData };
    }

    onOk({
      formValues,
      fixedSpuDatas: result,
    });
  };

  const echoData = useCallback(
    async (key: number, shops: UpdateRolloutRecordedList[]) => {
      const { data } = await mutateGetBaseConf(Number(key));
      if (data) {
        const {
          fareTemplates,
          aftersaleAddresses,
          logisticPlans,
          ...restValues
        } = parseJSON<PlatformRolloutConfValue>(data.confValue, 'object');

        const templateList = shops.map((shop) => {
          const exist = fareTemplates?.find((r) => r.shopId === shop.id);
          if (exist) {
            return exist;
          } else {
            return {
              shopId: shop.id,
              shopName: shop.shopName,
            };
          }
        });

        switch (Number(key)) {
          case PlatformTypeEnum['抖音']: {
            dYinForm.setFieldsValue({
              ...restValues,
              [freightTemplateListName]: templateList,
            });
            break;
          }
          case PlatformTypeEnum['淘宝']: {
            tBaoForm.setFieldsValue({
              ...restValues,
              [freightTemplateListName]: templateList,
            });
            break;
          }
          case PlatformTypeEnum['天猫']: {
            tMaoForm.setFieldsValue({
              ...restValues,
              [freightTemplateListName]: templateList,
            });
            break;
          }

          case PlatformTypeEnum['京东']: {
            jDongForm.setFieldsValue({
              ...restValues,
              [freightTemplateListName]: templateList,
            });
            break;
          }
          case PlatformTypeEnum['拼多多']: {
            pddForm.setFieldsValue({
              ...restValues,
              shipmentLimitSecond: restValues.shipmentLimitSecond
                ? restValues.shipmentLimitSecond
                : 0,
              deliveryOneDay: restValues.shipmentLimitSecond ? 1 : 0,
              [freightTemplateListName]: templateList,
            });
            break;
          }
          case PlatformTypeEnum['小红书']: {
            const logisticPlansList = platformDistributionIdsByTypeShop[
              Number(key)
            ].map((shop) => {
              const exist = logisticPlans?.find(
                (r: any) => r.shopId === shop.shopId,
              );
              if (exist) {
                return exist;
              } else {
                return {
                  shopId: shop.shopId,
                  shopName: shop.shopName,
                };
              }
            });

            let deliveryTime: number | undefined = undefined;

            if (!isNil(restValues.deliveryTime)) {
              deliveryTime = restValues.deliveryTime?.time
                ? Number(restValues.deliveryTime.time)
                : 0;
            }

            xhsForm.setFieldsValue({
              ...restValues,
              deliveryTime,
              [freightTemplateListName]: templateList,
              logisticPlans: logisticPlansList,
            });
            break;
          }
          case PlatformTypeEnum['视频号小店']: {
            const aftersaleAddressList = platformDistributionIdsByTypeShop[
              Number(key)
            ].map((shop) => {
              const exist = aftersaleAddresses?.find(
                (r: any) => r.shopId === shop.shopId,
              );
              if (exist) {
                return exist;
              } else {
                return {
                  shopId: shop.shopId,
                  shopName: shop.shopName,
                };
              }
            });

            wXinForm.setFieldsValue({
              ...restValues,
              [freightTemplateListName]: templateList,
              aftersaleAddresses: aftersaleAddressList,
            });
            break;
          }
          case PlatformTypeEnum['快手']: {
            kShouForm.setFieldsValue({
              ...restValues,
              [freightTemplateListName]: templateList,
            });
            break;
          }
          case PlatformTypeEnum['有赞']: {
            const {
              deliverySetting,
              isSupportBarter,
              supportUnconditionalReturn,
              ...otherValues
            } = restValues;

            yZanForm.setFieldsValue({
              ...otherValues,
              [freightTemplateListName]: templateList,
              ...formatYouZanDeliverySettingInitValue(deliverySetting),
              ...formatYouZanAfterSaleServiceInitValue(
                isSupportBarter,
                supportUnconditionalReturn,
              ),
            });
            break;
          }
        }
      }
    },
    [
      dYinForm,
      jDongForm,
      kShouForm,
      mutateGetBaseConf,
      pddForm,
      platformDistributionIdsByTypeShop,
      tBaoForm,
      tMaoForm,
      wXinForm,
      xhsForm,
      yZanForm,
    ],
  );

  const createFormRef = useCallback((form: any, key: string) => {
    return {
      form,
      key,
      validate: async () => {
        try {
          await form.validateFields();
          return true;
        } catch (err) {
          return false;
        }
      },
    };
  }, []);

  const formRefMap = useCallback(
    (key: number) => {
      switch (Number(key)) {
        case PlatformTypeEnum.淘宝:
          return tBaoForm;
        case PlatformTypeEnum.天猫:
          return tMaoForm;
        case PlatformTypeEnum.京东:
          return jDongForm;
        case PlatformTypeEnum.抖音:
          return dYinForm;
        case PlatformTypeEnum.快手:
          return kShouForm;
        case PlatformTypeEnum.拼多多:
          return pddForm;
        case PlatformTypeEnum.有赞:
          return yZanForm;
        case PlatformTypeEnum.视频号小店:
          return wXinForm;
        case PlatformTypeEnum.小红书:
          return xhsForm;
      }
    },
    [
      dYinForm,
      jDongForm,
      kShouForm,
      pddForm,
      tBaoForm,
      tMaoForm,
      wXinForm,
      xhsForm,
      yZanForm,
    ],
  );

  const getTabChildren = useCallback(
    (
      key: number,
      shops: UpdateRolloutRecordedList[],
      platformInfo: BasicPlatformRolloutSupportTypesVO[],
    ) => {
      switch (key) {
        case PlatformTypeEnum.淘宝:
          return (
            <Form form={formRefMap(key)} enableUnsavedWarning={false}>
              <TbContent shops={shops} platformInfo={platformInfo} />
            </Form>
          );
        case PlatformTypeEnum.抖音:
          return (
            <Form form={formRefMap(key)} enableUnsavedWarning={false}>
              <DyContent shops={shops} platformInfo={platformInfo} />
            </Form>
          );
        default:
          return (
            <Form form={formRefMap(key)} enableUnsavedWarning={false}>
              <CommonRolloutContent shops={shops} platformInfo={platformInfo} />
            </Form>
          );
      }
    },
    [formRefMap],
  );

  const items = useMemo(() => {
    setSelectedType(platformSettingList[0]?.platformInfo?.key);
    return (
      platformSettingList?.map(
        (platform: { platformInfo: { key: number }; shop: any }) => {
          formRefs.current.push(
            createFormRef(
              formRefMap(platform.platformInfo.key),
              platform.platformInfo.key,
            ),
          );
          return {
            label: PlatformTypeEnum[platform.platformInfo.key],
            key: platform.platformInfo.key,
            forceRender: true,
            children: getTabChildren(
              platform.platformInfo.key,
              platform.shop,
              platform.platformInfo,
            ),
          };
        },
      ) || []
    );
  }, [platformSettingList, createFormRef, formRefMap, getTabChildren]);

  useUpdateEffect(() => {
    const fetchData = async () => {
      if (items?.length) {
        setLoading(true);
        await Promise.all(
          platformSettingList.map((platform) =>
            echoData(platform.platformInfo.key, platform.shop),
          ),
        ).finally(() => setLoading(false));
      }
    };

    fetchData();
  }, [items]);

  const showModal = useCallback(() => {
    setModalOpen(true);
  }, [platformSettingList]);

  const closeModal = useCallback(() => {
    setModalOpen(false);
    setFixData({});
    formRefs.current = [];
  }, []);

  useImperativeHandle(ref, () => ({ showModal, closeModal }));

  const handleChangeType = async (key: PlatformTypeEnum) => {
    setSelectedType(key);
  };

  return (
    <Modal
      open={modalOpen}
      title="铺货设置确认"
      type="edit"
      size="m"
      maskArea={maskArea}
      destroyOnClose
      onCancel={closeModal}
      footer={() => {
        return (
          <Flex justify="end" gap={4}>
            <Button onClick={closeModal}>上一步</Button>

            <PriceAndInventoryModal
              rolloutWay={rolloutWay}
              targetIds={platformDistributionIdsByType[selectedType]}
              platformType={selectedType}
              ref={priceAndInventoryModalRef}
              okText={
                rolloutWay === PlatformRolloutWayEnum['通用铺货资料']
                  ? '修改并更新铺货资料'
                  : '确定'
              }
              onSubmit={(dataList: InventoryAndPriceDataItem[]) => {
                const fixedSpuDatas = convertToFixedSpuData(
                  dataList,
                  selectedType,
                );
                return new Promise<void>((resolve, reject) => {
                  const updateFixData = () => {
                    setFixData((prev) => {
                      const newFixData = { ...prev };
                      newFixData[selectedType] = dataList;
                      return newFixData;
                    });
                  };

                  if (rolloutWay === PlatformRolloutWayEnum.通用铺货资料) {
                    net
                      .fetch(
                        basicPlatformRolloutGoodsFixedDataUpdate({
                          platformDistributionSpuIds:
                            platformDistributionIdsByType[selectedType],
                          platformType: Number(selectedType),
                          fixedSpuDatas,
                        }),
                      )
                      .then((res) => {
                        const { data, success, message } = res;
                        if (!data && !success) {
                          reject(new Error(`${message}`));
                          return;
                        }

                        updateFixData();
                        msgSuccess('修改成功');
                        resolve();
                      })
                      .catch((error) => {
                        reject(new Error(`${error}`));
                      });
                  } else {
                    updateFixData();
                    resolve();
                  }
                });
              }}
            >
              {(onClick) => {
                return <Button onClick={onClick}>查看价格与库存</Button>;
              }}
            </PriceAndInventoryModal>

            <Button
              type="primary"
              onClick={() => {
                handleSaveAll();
              }}
            >
              立即铺货
            </Button>
          </Flex>
        );
      }}
    >
      <div className="px-sm">
        <Spin spinning={loading}>
          <Tabs
            className="h-full w-full bg-white"
            activeKey={selectedType}
            items={items}
            onChange={(key) => handleChangeType(key)}
          />
        </Spin>
      </div>
    </Modal>
  );
});
