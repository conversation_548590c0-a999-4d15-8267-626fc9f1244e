import { Flex, Tag, Tooltip } from '@slt/base-ui';
import { UserTagType } from '@slt/services';

enum CommodityType {
  '自由' = 1,
  '组合' = 2,
}

enum DistributionType {
  '自由' = 1,
  '分销' = 2,
}

/** 获取客户/供应商类型tag */
export const getTagByUserType = (
  type?: UserTagType,
  collaborateTagTip?: string,
  shopTagTip?: string,
) => {
  if (type === UserTagType.协同) {
    const TagEle = (
      <Tag color="blue" className="pe-xxs ps-xxs text-[11px] leading-snug">
        协同
      </Tag>
    );
    if (collaborateTagTip) {
      return <Tooltip title={collaborateTagTip}>{TagEle}</Tooltip>;
    }
    return TagEle;
  }
  if (type === UserTagType.店铺) {
    const TagEle = (
      <Tag color="red" className="pe-xxs ps-xxs text-[11px] leading-snug">
        店铺
      </Tag>
    );
    if (shopTagTip) {
      return <Tooltip title={shopTagTip}>{TagEle}</Tooltip>;
    }
    return TagEle;
  }
  return;
};

/**
 * @Desc: 获取商品类型tag
 * @param {CommodityType} commodityType 商品类型
 * @param {DistributionType} distributionType 分销类型
 */
export const getTagByGoodsType = (
  commodityType?: number,
  distributionType?: number,
) => {
  if (commodityType === CommodityType.组合) {
    if (distributionType === DistributionType.分销) {
      return (
        <Flex>
          <Tag color="blue">组合</Tag>
          <Tag color="blue">分销</Tag>
        </Flex>
      );
    }
    return <Tag color="red">组合</Tag>;
  }
  if (distributionType === DistributionType.分销) {
    return <Tag color="blue">分销</Tag>;
  }
  return null;
};
