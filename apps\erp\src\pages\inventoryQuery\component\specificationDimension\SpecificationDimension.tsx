import { BizTable, ExportBusinessType } from '@slt/biz-ui';
import { inventoryQueryInfoList } from '@slt/services';
import { useSpecificationDimension } from './useSpecificationDimension';
import { ToolbarLeftTips } from '../toolbarLeftTips/ToolbarLeftTips';

/** 仓库查询-规格维度 */
const InventoryWarehouseQuery = () => {
  const {
    form,
    tableRef,
    initialValues,
    summaryData,
    searchColumns,
    tableColumns,
    getExportParams,
    beforeSearchData,
  } = useSpecificationDimension();

  return (
    <>
      <BizTable
        requestConfig={{
          api: inventoryQueryInfoList(),
          beforeData: {
            beforeLabelInValueKeys: ['goodsSkuIdList'],
            beforeSearchData,
          },
          extraParams: {
            ...initialValues,
          },
        }}
        table={{
          rowSelection: false,
          tableRef,
          id: 'specificationDimensionQuery',
          rowKey: 'skuId',
          columns: tableColumns,
          wrapperClassName: 'px-sm',
        }}
        summaryData={summaryData}
        showRowIndex
        exportConfig={{
          businessType: ExportBusinessType.库存查询规格维度导出,
          authId: '2176',
          getParams: getExportParams,
        }}
        search={{
          form,
          searchColumns,
          initialValues: {
            separateWarehouse: false,
            enable: true,
          },
        }}
        toolbar={{
          left: <ToolbarLeftTips />,
        }}
      />
    </>
  );
};

export default InventoryWarehouseQuery;
