import type { CommonRecord } from '@slt/base-utils';
import type { YTRequest } from '@slt/net';
import type { DataImageNumberType, DistributionLevel } from '@slt/utils';
import type { BatchOperationResultVO } from '../common.types';
import type {
  AddOrUpdateParams,
  BasicDistributionBatchOfflineParams,
  BasicDistributionDeListingParams,
  BasicDistributionMaterialSyncGoodsDTO,
  CalMaterialAmount,
  DistributionDetailDTO,
  DistributionFindEnumsDTO,
  DistributionOnlineResultDTO,
  DistributionRulesVO,
  DistributionUpdateSupplierSettingParams,
} from './distribution.interface';
import type { BasicDistributionBatchModifyGoodsNameParams } from './distributionPrice.interface';

const BASIC_PREFIX = '/basic/distribution';

export const basicDistributionFindMaterialInfoPageUrl =
  '/basic/distribution/find/material/info/page';

/*
 *
 * 新建铺货资料
 */
export const goodsStoreInfoSave = (
  data: AddOrUpdateParams & { uniqueKey: string },
): YTRequest<{ id: string }> => {
  return {
    url: `${BASIC_PREFIX}/save`,
    data,
  };
};

/**
 * 创建铺货资料
 */
export const goodsStoreInfoModify = (
  data: AddOrUpdateParams & { id: string },
): YTRequest<{ id: string; onlineErrorMessage?: string }> => {
  return {
    url: `${BASIC_PREFIX}/modify`,
    data,
  };
};

/**
 * 详情返回信息
 */
export const findOne = (data: {
  id: string;
}): YTRequest<DistributionDetailDTO> => {
  return {
    url: `${BASIC_PREFIX}/find/one`,
    method: 'GET',
    data,
  };
};

/**
 * 商品编码获取资料信息
 */
export const findMaterialInfoDistributionCode = (data: {
  distributionSpuCode: string;
}): YTRequest<DistributionDetailDTO[]> => {
  return {
    url: `${BASIC_PREFIX}/find/material/info/distribution/code`,
    method: 'GET',
    data,
  };
};

/**
 * 图片抓取接口
 */
export const findImages = (data: {
  urlPath: string;
}): YTRequest<
  Array<{
    url: string;
    urlType: DataImageNumberType;
  }>
> => {
  return {
    url: `${BASIC_PREFIX}/find/images`,
    method: 'GET',
    timeout: 5 * 60 * 1000,
    data,
  };
};

/**
 * 资料纬度查询
 */
export const basicDistributionFindMaterialInfoPageApi = `${BASIC_PREFIX}/find/material/info/page`;

/**
 * 规格纬度查询
 */
export const basicDistributionFindMaterialSkuPageApi = `${BASIC_PREFIX}/find/material/sku/page`;

/**
 * 枚举值
 */
export const basicDistributionFindEnums =
  (): YTRequest<DistributionFindEnumsDTO> => {
    return {
      url: `${BASIC_PREFIX}/material/enums`,
    };
  };

/**
 * 客户供货状态更新
 */
export const basicDistributionUpdateSupplierStatus = (
  data: DistributionUpdateSupplierSettingParams,
): YTRequest<boolean> => {
  return {
    url: `${BASIC_PREFIX}/supplier/status`,
    data,
  };
};

/**
 * 按资料修改成本价，最低售价，标准售价，基础分销价
 */
export const basicDistributionModifyMaterialPrice = (data: {
  ids: string[];
  materialAmount: CalMaterialAmount;
}): YTRequest<boolean> => {
  return {
    url: `${BASIC_PREFIX}/modify/material/price`,
    data,
  };
};

/** 批量设置名称前后缀 */
export const basicDistributionBatchModifyGoodsName = (
  data: BasicDistributionBatchModifyGoodsNameParams,
): YTRequest<boolean> => ({
  url: `${BASIC_PREFIX}/batch/modify/goods/name`,
  data,
});

/**
 * 按资料修改等级分销价
 */
export const basicDistributionModifyMaterialGradePrice = (data: {
  ids: string[];
  extensions: {
    extensionKey: DistributionLevel;
    calMaterialAmount: Omit<CalMaterialAmount, 'modifyPriceType'>;
  }[];
}): YTRequest<boolean> => {
  return {
    url: `${BASIC_PREFIX}/modify/material/grade/price`,
    data,
  };
};

/**
 * 按规格修改成本价，最低售价，标准售价，基础分销价
 */
export const basicDistributionModifyMaterialPriceSku = (data: {
  ids: string[];
  materialAmount: CalMaterialAmount;
}): YTRequest<boolean> => {
  return {
    url: `${BASIC_PREFIX}/modify/material/price/sku`,
    data,
  };
};

/**
 * 按规格修改等级分销价
 */
export const basicDistributionModifyMaterialGradePriceSku = (data: {
  ids: string[];
  extensions: {
    extensionKey: DistributionLevel;
    calMaterialAmount: Omit<CalMaterialAmount, 'modifyPriceType'>;
  }[];
}): YTRequest<boolean> => {
  return {
    url: `${BASIC_PREFIX}/modify/material/grade/price/sku`,
    data,
  };
};

/**
 * 批量删除资料信息
 */
export const basicDistributionBatchRemove = ({
  ids,
}: {
  ids: string[];
}): YTRequest<any> => {
  return {
    url: `${BASIC_PREFIX}/batch/remove`,
    data: {
      ids,
    },
  };
};

/**
 * 资料批量启用
 */
export const basicDistributionBatchEnable = ({
  ids,
}: {
  ids: string[];
}): YTRequest<boolean> => {
  return {
    url: `${BASIC_PREFIX}/batch/enable`,
    data: {
      ids,
    },
  };
};

/**
 * 批量停用
 */
export const basicDistributionBatchStop = ({
  ids,
}: {
  ids: string[];
}): YTRequest<boolean> => {
  return {
    url: `${BASIC_PREFIX}/batch/stop`,
    data: {
      ids,
    },
  };
};

/**
 * 管理后台下架
 */
export const basicDistributionDeListing = (
  params: BasicDistributionDeListingParams,
): YTRequest<boolean> => {
  return {
    url: `${BASIC_PREFIX}/de/listing`,
    data: params,
  };
};

/**
 * 商品上架---待上线列表
 */
export const basicDistributionOnlineList = (data: {
  ids: string[];
}): YTRequest<DistributionOnlineResultDTO> => {
  return {
    url: '/basic/distribution/online/list',
    data,
  };
};

/**
 * 批量上架
 */
export const basicDistributionBatchOnline = (data: {
  platformType: string;
  ids: string[];
}): YTRequest<BatchOperationResultVO> => {
  return {
    url: '/basic/distribution/online',
    data,
  };
};

/**
 * 批量下架
 */
export const basicDistributionBatchOffline = (
  data: BasicDistributionBatchOfflineParams,
): YTRequest<BatchOperationResultVO> => {
  return {
    url: '/basic/distribution/offline',
    data,
  };
};

/**
 * 平台资料同步商品
 */
export const basicDistributionMaterialSyncGoods = (data: {
  /**
   * 1 按商品同步
   * 2 按分销品同步
   * 3 按店铺同步
   */
  type: number;
  /**
   * 店铺同步使用
   */
  shopId?: string;
  /**
   * 不传表示同步所有
   */
  goodsIds?: string[];
}): YTRequest<BasicDistributionMaterialSyncGoodsDTO> => {
  return {
    url: '/basic/distribution/material/sync/goods',
    data,
  };
};

/**
 * 批量设置禁售平台
 */
export const basicDistributionBatchConfigProhibitPlatform = (data: {
  distributionSpuIdList: string[];
  platformId: string[];
}): YTRequest<boolean> => {
  return {
    url: '/basic/distribution/batch/config/prohibit/platform',
    data,
  };
};

/**
 * 设置可见规则
 */
export const basicDistributionBatchConfigVisibleRule = (data: {
  distributionSpuIdList: string[];
  distributionVisibleRuleInfo: {
    type: number;
    subType: number;
    distributionVisibleRuleDetail?: {
      distributionLevelList?: number[];
      distributorInfoList?: Array<{ id: string; distributorName: string }>;
    };
  };
}): YTRequest<boolean> => {
  return {
    url: '/basic/distribution/rules/visible/configs',
    data,
  };
};

/**
 * 设置选品规则
 */
export const basicDistributionBatchConfigProductSelect = (data: {
  distributionSpuIdList: string[];
  distributionVisibleRuleInfo: {
    type: number;
    distributionVisibleRuleDetail?: {
      distributionLevelList?: number[];
      distributorInfoList?: Array<{ id: string; distributorName: string }>;
    };
  };
}): YTRequest<boolean> => {
  return {
    url: '/basic/distribution/rules/product/select/batch/configs',
    data,
  };
};

/**
 * 设置运费模板
 */
export const basicDistributionBatchConfigShippingTemplate = (data: {
  templateId: string;
  templateName: string;
  distributionSpuIds: string[];
}): YTRequest<boolean> => {
  return {
    url: '/basic/distribution/batch/config/shipping/template',
    data,
  };
};

/**
 * 校验商品编码是否重复
 */
export const basicDistributionCheckGoodsSpuNoIsRepeat = (data: {
  distributionSpuCode: string;
  distributionType?: number;
  supplierId?: number;
  id?: string;
}): YTRequest<any> => {
  return {
    url: '/basic/distribution/checkGoodsSpuNoIsRepeat',
    // method: 'GET',
    data,
  };
};

/**
 * 查询可见规则 详情
 */
export const basicDistributionRulesVisibleFindOne = (
  id: string,
): YTRequest<DistributionRulesVO> => {
  return {
    url: `/basic/distribution/rules/visible/find/one/${id}`,
    data: {},
  };
};

/**
 * 查询选品规则 详情
 */
export const basicDistributionRulesProductSelectFindOne = (
  id: string,
): YTRequest<DistributionRulesVO> => {
  return {
    url: `/basic/distribution/rules/product/select/find/one/${id}`,
    data: {},
  };
};

/**
 * 下载导入模板
 */
export const basicDistributionImportTemplateDownload =
  (): YTRequest<string> => {
    return {
      url: '/basic/distribution/import/template/download',
    };
  };

/**
 * 导入平台资料
 */
export const basicDistributionImport = (data: {
  fileUrl: string;
  fileSize?: number;
  fileName: string;
  /**
   * 1 导入新增 2 导入修改
   */
  fileType: number;
}): YTRequest<boolean> => {
  return {
    url: '/basic/distribution/import',
    data,
  };
};

export const basicDistributionSpuExport = (data: CommonRecord) => {
  return {
    url: '/basic/distribution/spu/export',
    data,
  };
};

export const basicDistributionSkuExport = (data: CommonRecord) => {
  return {
    url: '/basic/distribution/sku/export',
    data,
  };
};
// 批量修改选品市场分类
export const basicDistributionBatchModifyCategory = (data: CommonRecord) => {
  return {
    url: '/basic/distribution/batch/modify/market/category',
    data,
  };
};
