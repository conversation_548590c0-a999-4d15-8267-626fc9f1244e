import type { ComponentType, SyntheticEvent } from 'react';
import { useState } from 'react';
import type { ResizeCallbackData } from 'react-resizable';
import { Resizable } from 'react-resizable';
import { useStyle } from './useStyle';

const ResizableComponent = Resizable as ComponentType<any>;

const setBodyStyle = (active: boolean) => {
  document.body.style.userSelect = active ? 'none' : '';
  document.body.style.webkitUserSelect = active ? 'none' : '';
  document.body.style.pointerEvents = active ? 'none' : '';
  document.documentElement.style.cursor = active ? 'col-resize' : '';
};

export const ResizableTitle = (props: {
  onResize: (e: SyntheticEvent, data: ResizeCallbackData) => void;
  width: number;
  className?: string;
}) => {
  const { onResize, width, className, ...restProps } = props;
  // 添加偏移量
  const [offset, setOffset] = useState(0);

  const { styles, cx } = useStyle();

  if (!width) {
    return <th className={className} {...restProps} />;
  }

  return (
    <ResizableComponent
      width={width + offset}
      height={0}
      handle={
        <span
          // 有偏移量显示竖线
          className={`${styles.react_resizable_handle} ${
            offset ? styles.active : ''
          }`}
          // 拖拽层偏移
          style={{ transform: `translateX(${offset}px)` }}
          onClick={(e) => {
            // 取消冒泡，不取消貌似容易触发排序事件
            e.stopPropagation();
            e.preventDefault();
          }}
        />
      }
      onResizeStart={() => {
        setBodyStyle(true);
      }}
      // 拖拽事件实时更新
      onResize={(_e: SyntheticEvent, { size }: ResizeCallbackData) => {
        // 只更新偏移量，数据列表其实并没有伸缩
        setOffset(size.width - width);
      }}
      // 拖拽结束更新
      onResizeStop={(e: SyntheticEvent, data: ResizeCallbackData) => {
        // 拖拽结束以后偏移量归零
        setOffset(0);
        onResize(e, data);
        setBodyStyle(false);
      }}
      draggableOpts={{ enableUserSelectHack: false }}
    >
      <th className={cx(className, 'select-none')} {...restProps} />
    </ResizableComponent>
  );
};
