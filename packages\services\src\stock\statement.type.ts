import type { StatementBillStatusType, AppendTypeEnum } from '@slt/utils';
import type { CommonRecord } from '@slt/base-utils';
import type { CheckResultParams } from '../bill/purchase.interface';

export enum UserTagType {
  '店铺' = 1,
  '协同' = 2,
}

/** 对账单列表，请求参数 */
export type StatementQueryPageParams = {
  id: string;
  /**
   * 完成时间结束时间
   */
  finishEndTime: string;
  /**
   * 完成时间开始时间
   */
  finishStartTime: string;
  /**
   * 客户id
   */
  merchantId: string;
  /**
   * 是否超期未支付 1-是 0-否
   */
  overdueStatus: number;
  /**
   * 收款状态
   * 1-未收款
   * 2-部分收款
   * 3-已收款
   */
  receiveStatus: number;
  /**
   * 还款结束时间
   */
  repayEndTime: string;
  /**
   * 还款开始时间
   */
  repayStartTime: string;
  /**
   * 对账开始时间
   */
  statementStartTime: string;
  /**
   * 对账结束时间
   */
  statementEndTime: string;
  /**
   * 对账单单号
   */
  statementNos: string[];
  /**
   * 账单状态
   * 1-未结算
   * 2-结算中
   * 3-结算成功
   */
  statementStatus: number;
  /** 账单状态-多选 */
  statementStatusList?: number[];
};

/** 对账单列表，响应结果 */
export type StatementListVO = {
  /** 账单来源名称 */
  statementSourceTypeName?: string;
  /** 协同对账单号 */
  synergyStatementNo?: string;
  /** 客户类型 */
  customerSourceType: UserTagType;
  /**
   * 调账金额
   */
  adjustAmount: string;
  /**
   * 附件
   */
  attachmentUrl: string;
  /**
   * 单据总数
   */
  billsCount: string;
  /**
   * 创建人名称
   */
  createUserName: string;
  /**
   * 创建时间
   */
  gmtCreate: string;
  /**
   * 修改时间
   */
  gmtModified: string;
  /**
   * 商品总数
   */
  goodsCount: string;
  /**
   * 商品应收
   */
  goodsPayableAmount: string;
  /**
   * 商品已收
   */
  goodsReceivedAmount: string;
  /**
   * 对账单id
   */
  id: string;
  /**
   * 发票单号
   */
  invoiceNo: string;
  /**
   * 最后还款时间
   */
  lastRepaymentTime: string;
  /**
   * 客户id
   */
  merchantId: string;
  /**
   * 客户名称
   */
  merchantName: string;
  /**
   * 更新人名称
   */
  modifiedUserName: string;
  /**
   * 其他应收
   */
  otherPayableAmount: string;
  /**
   * 其他已收
   */
  otherReceivedAmount: string;
  /**
   * 收款状态
   * 1-未收款
   * 2-部分收款
   * 3-已收款
   */
  receiveStatus: number;
  /**
   * 备注
   */
  remark: string;
  /**
   * 对账结束时间
   */
  statementEndTime: string;
  /**
   * 对账完成时间
   */
  statementFinishTime: string;
  /**
   * 对账单号
   */
  statementNo: string;
  /**
   * 对账开始时间
   */
  statementStartTime: string;
  /**
   * 账单状态
   * 1-未结算
   * 2-结算中
   * 3-结算成功
   */
  statementStatus: StatementBillStatusType;
  /**
   * 应收合计
   */
  totalPayableAmount: string;
  /**
   * 合计收款金额
   */
  totalReceivedAmount: string;
  /**
   * 剩余待收金额
   */
  waitReceiveAmount: string;
};

/** 对账单列表，设置最后还款时间，请求参数 */
export type StatementResetRepaymentTimeParams = {
  /** 单据id */
  statementIds: string[];
  /** 最后还款时间 */
  lastRepaymentTime: string;
};

/** 对账单短信校验，请求参数 */
export type StatementSmsVerifyParams = {
  /**
   * 验证码
   */
  code?: string;
  /**
   * 手机号
   */
  mobile?: string;
  /**
   * 验证场景
   */
  smsCodeScene?: number;
  /**
   * 校验类型
   */
  verificationType?: number;
};

/** 对账单详情，上传发票请求参数 */
export type StatementResetInvoiceParams = {
  /**
   * 附件地址（多个地址用;拼接）
   */
  attachmentUrl?: string;
  /**
   * 对账单id
   */
  id?: string;
  /**
   * 发票单号
   */
  invoiceNo?: string;
  /**
   * 最后还款时间
   */
  lastRepaymentTime?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 对账单id集合
   */
  statementIds?: string[];
};

/** 对账单详情，账单信息——收款信息 */
export type StatementDetailMoneyInfo = {
  /**
   * 调账金额
   */
  adjustAmount?: number;
  /**
   * 商品应收
   */
  goodsPayableAmount?: number;
  /**
   * 商品已收
   */
  goodsReceivedAmount?: number;
  /**
   * 其他应收
   */
  otherPayableAmount?: number;
  /**
   * 其他已收
   */
  otherReceivedAmount?: number;
  /**
   * 合计应收
   */
  totalPayableAmount?: number;
  /**
   * 合计收款金额
   */
  totalReceivedAmount?: number;
  /**
   * 剩余待收金额
   */
  waitReceiveAmount?: number;
};

/** 对账单详情，响应结果 */
export type StatementDetailBillInfoVO = {
  /** 客户/供应商类型 */
  customerSourceType: UserTagType;
  /** 前端自己需要收款信息字段 */
  moneyInfo?: StatementDetailMoneyInfo;
  /**
   * 调账金额
   */
  adjustAmount?: number;
  /**
   * 流向，1：应收，2：应付
   */
  amountType?: number;
  /**
   * 附件
   */
  attachmentUrl?: string;
  /**
   * 单据总数
   */
  billsCount?: number;
  /**
   * 创建人id
   */
  createUserId?: number;
  /**
   * 创建人名称
   */
  createUserName?: string;
  /**
   * 创建时间
   */
  gmtCreate?: string;
  /**
   * 修改时间
   */
  gmtModified?: string;
  /**
   * 商品总数
   */
  goodsCount?: number;
  /**
   * 商品应收
   */
  goodsPayableAmount?: number;
  /**
   * 商品已收
   */
  goodsReceivedAmount?: number;
  /**
   * id
   */
  id?: string;
  /**
   * 发票单号
   */
  invoiceNo?: string;
  /**
   * 是否删除
   */
  isDelete?: number;
  /**
   * 最后还款时间
   */
  lastRepaymentTime?: string;
  /**
   * 客户id
   */
  merchantId?: string;
  /**
   * 客户名称
   */
  merchantName?: string;
  /**
   * 更新人id
   */
  modifiedUserId?: string;
  /**
   * 更新人名称
   */
  modifiedUserName?: string;
  /**
   * 其他应收
   */
  otherPayableAmount?: number;
  /**
   * 其他已收
   */
  otherReceivedAmount?: number;
  /**
   * 收款状态
   * 1-未收款
   * 2-部分收款
   * 3-已收款
   */
  receiveStatus?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 对账结束时间
   */
  statementEndTime?: string;
  /**
   * 对账完成时间
   */
  statementFinishTime?: string;
  /**
   * 账单号
   */
  statementNo?: string;
  /**
   * 对账开始时间
   */
  statementStartTime?: string;
  /**
   * 账单状态
   */
  statementStatus?: number;
  /**
   * 租户id
   */
  tenantId?: string;
  /**
   * 合计应收
   */
  totalPayableAmount?: number;
  /**
   * 合计收款金额
   */
  totalReceivedAmount?: number;
  /**
   * 剩余待收金额
   */
  waitReceiveAmount?: number;
};

/** 对账单详情——获取协同创建的对账单单据数的差异信息的响应结果 */
export type StatementQueryDiffVO = {
  /** 当前对账单的单据数 */
  currentCount?: number;
  /** 协同创建的对账单的单据数 */
  synergyCount?: number;
  /** 协同创建的对账单单号 */
  synergyStatementNo?: string;
};

/** 对账单详情，商品应收/应付表头合计 */
export type GoodsReceivableStatisticsVO = {
  /**
   * 待收金额
   */
  arrearsAmount?: number;
  /**
   * 商品运费收入
   */
  freightAmount?: number;
  /**
   * 已收金额
   */
  receivedAmount?: number;
  /**
   * 销售出库金额
   */
  saleAmount?: number;
  /**
   * 销售出库单数量
   */
  saleBillCount?: number;
  /**
   * 合计销售数量
   */
  saleQuality?: number;
  /**
   * 销售退货金额
   */
  saleReturnAmount?: number;
  /**
   * 销售退货单数量
   */
  saleReturnBillCount?: number;
  /**
   * 商品合计应收
   */
  totalReceivableAmount?: number;
  /**
   * 合计销售金额
   */
  totalSaleAmount?: number;
};

/** 对账单详情——商品应收/付——商品汇总，请求参数 */
export type GoodsCollectQuery = {
  /** 对账单id */
  statementId: string;
  /** 商品编码 */
  goodsCodeList?: string[];
  /** 商品名称 */
  goodsName?: string;
  /** 规格编码 */
  skuCodeList?: string[];
  /** 规格名称 */
  skuName?: string;
};
/** 对账单详情——商品应收/付——商品汇总，响应结果 */
export type GoodsReceivableCollectVO = {
  /**
   * 待收金额
   */
  arrearsAmount?: number;
  /**
   * 成本均价
   */
  costAmount?: number;
  /**
   * 运费
   */
  freightAmount?: number;
  /**
   * 商品名称
   */
  goodsName?: string;
  /**
   * 商品编码
   */
  goodsNo?: string;
  /**
   * 商品类型
   * 1：自由
   * 2：组合
   */
  type: number;
  /**
   * 分销类型
   * 1：自由
   * 2：分销
   */
  distributionType?: number;
  /**
   * 商品重量
   */
  goodsWeight?: number;
  /**
   * 销售毛利
   */
  netProfitAmount?: number;
  /**
   * 销售毛利率
   */
  netProfitRate?: number;
  /**
   * 销售出库金额
   */
  outAmount?: number;
  /**
   * 出库单数量
   */
  outCount?: number;
  /**
   * 销售出库数量
   */
  outQuantity?: number;
  /**
   * 应收金额
   */
  receivableAmount?: number;
  /**
   * 已收金额
   */
  receivedAmount?: number;
  /**
   * 销售退货金额
   */
  returnAmount?: number;
  /**
   * 销售退回成本金额
   */
  returnCostAmount?: number;
  /**
   * 退货单数量
   */
  returnCount?: number;
  /**
   * 销售退货数量
   */
  returnQuantity?: number;
  /**
   * 退货率
   */
  returnRate?: number;
  /**
   * 销售成本金额
   */
  saleCostAmount?: number;
  /**
   * 销售均价
   */
  salePrice?: number;
  /**
   * sku名称
   */
  skuName?: string;
  /**
   * sku编码
   */
  skuNo?: string;
  /**
   * 合计销售金额
   */
  totalAmount?: number;
  /**
   * 合计成本金额
   */
  totalCostAmount?: number;
  /**
   * 合计销售数量
   */
  totalQuantity?: number;
};
/** 对账单详情——商品应收/付——商品汇总，表尾集合字段 */
export type GoodsReceivableCollectVOSummary = {
  /**
   * 待收金额
   */
  arrearsAmount?: number;
  /**
   * 运费
   */
  freightAmount?: number;
  /**
   * 商品重量
   */
  goodsWeight?: number;
  /**
   * 销售毛利
   */
  netProfitAmount?: number;
  /**
   * 销售出库金额
   */
  outAmount?: number;
  /**
   * 销售出库数量
   */
  outQuantity?: number;
  /**
   * 应收金额
   */
  receivableAmount?: number;
  /**
   * 已收金额
   */
  receivedAmount?: number;
  /**
   * 销售退货金额
   */
  returnAmount?: number;
  /**
   * 销售退回成本金额
   */
  returnCostAmount?: number;
  /**
   * 销售退货数量
   */
  returnQuantity?: number;
  /**
   * 销售成本金额
   */
  saleCostAmount?: number;
  /**
   * 合计销售金额
   */
  totalAmount?: number;
  /**
   * 合计成本金额
   */
  totalCostAmount?: number;
  /**
   * 合计销售数量
   */
  totalQuantity?: number;
};

/** 对账单详情——商品应收/付——商品销售/采购及退货明细，请求参数 */
export type GoodsWithDetailQuery = {
  /** 收货人信息，0：不包含，1：包含 */
  containReceiverInfo?: number;
  /** 对账单id */
  statementId: string;
  /** 单据编号 */
  billNoList?: string[];
  /** 内部订单号 */
  resourceBillNoList?: string[];
  /** 线上交易单号 */
  resourceOnlineBillNoList?: string[];
  /** 原始订单号 */
  resourcePlatformBillNoList?: string[];
  /** 单据类型 */
  billTypeList?: string[];
  /** 业务类型 */
  businessTypeList?: string[];
  /** 单据来源 */
  sourceTypeList?: string[];
  /** 订单来源 */
  srcChannelList?: string[];
  /** 商品编码 */
  goodsCodeList?: string[];
  /** 商品名称 */
  goodsName?: string;
  /** 规格编码 */
  skuCodeList?: string[];
  /** 规格名称 */
  skuName?: string;
};
/** 对账单详情——商品应收/付——商品销售/采购及退货明细，响应结果 */
export type GoodsReceivableWithDetailVO = {
  /**
   * 系统标签（是否是异常标签）
   * 1：异常，0：无
   */
  synergyExceptionStatus?: number;
  /**
   * 定价
   */
  assignPrice?: number;
  /**
   * 欠款
   */
  arrearsAmount?: number;
  /**
   * 单据日期
   */
  billDateTime?: string;
  billId?: string;
  /**
   * 单据编号
   */
  billNo?: string;
  /**
   * 单据类型
   */
  billType?: string;
  billTypeName?: string;
  businessName?: string;
  /**
   * 业务类型
   */
  businessType?: number;
  /**
   * 收件人地址
   */
  consigneeDetailAddress?: string;
  /**
   * 收件人名
   */
  consigneeName?: string;
  /**
   * 收件人手机号
   */
  consigneePhone?: string;
  /**
   * 收件人电话
   */
  consigneeTelephone?: string;
  departmentId?: string;
  departmentName?: string;
  /**
   * 运费
   */
  freightAmount?: number;
  goodsId?: string;
  /**
   * 商品名称
   */
  goodsName?: string;
  /**
   * 商品编码
   */
  goodsNo?: string;
  /**
   * 商品类型
   * 1：自由
   * 2：组合
   */
  type: number;
  /**
   * 分销类型
   * 1：自由
   * 2：分销
   */
  distributionType?: number;
  /**
   * 商品重量
   */
  goodsWeight?: number;
  id?: string;
  /**
   * 物流公司id
   */
  logisticsCompanyId?: string;
  /**
   * 物流公司
   */
  logisticsCompanyName?: string;
  /**
   * 物流单号
   */
  logisticsNo?: string;
  /**
   * 订单来源
   */
  srcChannel?: number;
  /**
   * 应收
   */
  receivableAmount?: number;
  /**
   * 收款单号
   */
  receiveBillNo?: string;
  /**
   * 已收
   */
  receivedAmount?: number;
  /**
   * 关联售后订单号
   */
  relatedAfterOrderNo?: string;
  /**
   * 关联售后订单id
   */
  relatedAfterOrderId?: string;
  /**
   * 关联售后退货单号
   */
  relatedAfterSaleNo?: string;
  /**
   * 关联售后退货单id
   */
  relatedAfterSaleId?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 内部单号
   */
  resourceBillNo?: string;
  /**
   * 线上交易单号
   */
  resourceOnlineBillNo?: string;
  /**
   * 原始单号
   */
  resourcePlatformBillNo?: string;
  salesManId?: string;
  salesManName?: string;
  /**
   * 店铺id
   */
  shopId?: string;
  /**
   * 店铺名称
   */
  shopName?: string;
  skuId?: string;
  /**
   * 规格名称
   */
  skuName?: string;
  /**
   * 规格编码
   */
  skuNo?: string;
  /**
   * 单据来源
   */
  sourceType?: number;
  /**
   * 单据来源描述
   */
  sourceTypeDesc?: string;
  /**
   * 单价
   */
  standardPrice?: number;
  /**
   * 商品总价
   */
  totalAmount?: number;
  /**
   * 商品数量
   */
  totalQuantity?: number;
  /**
   * 成本单价
   */
  stockCostAmount?: number;
  /**
   * 销售成本
   */
  stockTotalAmount?: number;
  /**
   * 销售毛利
   */
  netProfitAmount?: number;
  /**
   * 销售毛利率
   */
  netProfitRate?: number;
};
/** 对账单详情——商品应收/付——商品销售/采购及退货明细，表尾集合字段 */
export type GoodsReceivableWithDetailVOSummary = {
  /**
   * 欠款
   */
  arrearsAmount?: number;
  /**
   * 运费
   */
  freightAmount?: number;
  /**
   * 商品重量
   */
  goodsWeight?: number;
  /**
   * 销售毛利
   */
  netProfitAmount?: number;
  /**
   * 应收
   */
  receivableAmount?: number;
  /**
   * 已收
   */
  receivedAmount?: number;
  /**
   * 成本总价
   */
  stockTotalAmount?: number;
  /**
   * 商品总价
   */
  totalAmount?: number;
  /**
   * 商品数量
   */
  totalQuantity?: number;
};

/** 对账单详情——商品应收/付——单据明细，请求参数 */
export type GoodsReceivableQuery = {
  /** 收货人信息，0：不包含，1：包含 */
  containReceiverInfo?: number;
  /** 对账单id */
  statementId: string;
  /** 单据编号 */
  billNoList?: string[];
  /** 内部订单号 */
  resourceBillNoList?: string[];
  /** 线上交易单号 */
  resourceOnlineBillNoList?: string[];
  /** 原始订单号 */
  resourcePlatformBillNoList?: string[];
  /** 单据类型 */
  billTypeList?: string[];
  /** 业务类型 */
  businessTypeList?: string[];
  /** 单据来源 */
  sourceTypeList?: string[];
  /** 订单来源 */
  srcChannelList?: string[];
};
/** 对账单详情——商品应收/付——单据明细，响应结果 */
export type GoodsReceivableVO = {
  /**
   * 系统标签（是否是异常标签）
   * 1：异常，0：无
   */
  synergyExceptionStatus?: number;
  /**
   * 欠款
   */
  arrearsAmount?: number;
  /**
   * 单据日期
   */
  billDateTime?: string;
  billId?: string;
  /**
   * 单据编号
   */
  billNo?: string;
  /**
   * 单据类型
   */
  billType?: string;
  billTypeName?: string;
  businessName?: string;
  /**
   * 业务类型
   */
  businessType?: number;
  /**
   * 收件人地址
   */
  consigneeDetailAddress?: string;
  /**
   * 收件人名
   */
  consigneeName?: string;
  /**
   * 收件人手机号
   */
  consigneePhone?: string;
  /**
   * 收件人电话
   */
  consigneeTelephone?: string;
  departmentId?: string;
  departmentName?: string;
  /**
   * 运费
   */
  freightAmount?: number;
  /**
   * 商品重量
   */
  goodsWeight?: number;
  id?: string;
  /**
   * 物流公司id
   */
  logisticsCompanyId?: string;
  /**
   * 物流公司
   */
  logisticsCompanyName?: string;
  /**
   * 物流单号
   */
  logisticsNo?: string;
  /**
   * 销售毛利
   */
  netProfitAmount?: number;
  /**
   * 销售毛利率
   */
  netProfitRate?: number;
  /**
   * 订单来源
   */
  orderSourceType?: number;
  /**
   * 应收
   */
  receivableAmount?: number;
  /**
   * 收款单号
   */
  receiveBillNo?: string;
  /**
   * 收款状态
   */
  receiveStatus?: number;
  /**
   * 已收
   */
  receivedAmount?: number;
  /**
   * 关联售后订单号
   */
  relatedAfterOrderNo?: string;
  /**
   * 关联售后订单id
   */
  relatedAfterOrderId?: string;
  /**
   * 关联售后退货单号
   */
  relatedAfterSaleNo?: string;
  /**
   * 关联售后退货单id
   */
  relatedAfterSaleId?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 内部单号
   */
  resourceBillNo?: string;
  /**
   * 线上交易单号
   */
  resourceOnlineBillNo?: string;
  /**
   * 原始单号
   */
  resourcePlatformBillNo?: string;
  salesManId?: string;
  salesManName?: string;
  /**
   * 店铺id
   */
  shopId?: string;
  /**
   * 店铺名称
   */
  shopName?: string;
  /**
   * 单据来源
   */
  sourceType?: number;
  /**
   * 单据来源描述
   */
  sourceTypeDesc?: string;
  /**
   * 销售成本
   */
  stockTotalAmount?: number;
  tenantId?: string;
  /**
   * 商品总价
   */
  totalAmount?: number;
  /**
   * 商品数量
   */
  totalQuantity?: number;
};
/** 对账单详情——商品应收/付——单据明细，表尾合计字段 */

export type GoodsReceivableVOSummary = GoodsReceivableWithDetailVOSummary;

/** 对账单详情——其他应收/应付及调账，请求参数 */
export type OtherReceivableQuery = GoodsReceivableQuery & {
  /** 单据来源 */
  sourceTypes?: string[];
};
/** 对账单详情——其他应收/应付及调账，响应结果 */
export type OtherReceivableVO = GoodsReceivableVO;
/** 对账单详情——其他应收/应付及调账，表头合计字段 */
export type OtherReceivableStatisticsVO = {
  /**
   * 单据数量
   */
  billCount?: number;
  /**
   * 其他应收金额
   */
  otherReceivableAmount?: number;
  /**
   * 调账金额
   */
  adjustAmount?: number;
  /**
   * 合计应收
   */
  receivableAmount?: number;
  /**
   * 已收金额
   */
  receivedAmount?: number;
  /**
   * 待收金额
   */
  arrearsAmount?: number;
};
/** 对账单详情——其他应收/应付及调账，表尾合计字段 */
export type OtherReceivableVOSummary = {
  /** 合计应收金额 */
  tailReceivableAmount?: number;
  /** 合计已收金额 */
  tailReceivedAmount?: number;
  /** 合计未收金额 */
  tailArrearsAmount?: number;
};

/** 通过任务Id下载对账单，响应结果 */
export interface DownloadStatusVO {
  createTime?: string;
  errorMessage?: string;
  fileUrl?: string;
  finishTime?: string;
  /**
   * PROCESSING：开始下载
   * COMPLETED：下载完成
   * FAILED：下载失败
   */
  status?: number;
  taskId?: string;
}

/** 修改单据/明细备注 */
export type BatchModifyRemarkParams = {
  /** 备注方式 */
  appendFlag: AppendTypeEnum;
  saleOrderIdList?: string[];
  saleOrderPageQuery?: CommonRecord;
  remark?: string;
};

/** 对账单，去收款信息获取 */
export type StatementReceivableVO = {
  /**
   * 对账单数量
   */
  statementCount?: number;
  /**
   * 待收款金额
   */
  waitReceiveAmount?: number;
  /**
   * 当前收款金额
   */
  currentReceiveAmount?: number;
  /**
   * 其他收款单占用数量
   */
  occupyReceiveCount?: number;
  /**
   * 其他收款单占用的单据的编号
   */
  occupyReceiveBillNoList?: string[];
  /**
   * 金额为0单据数量
   */
  needlessReceiveCount?: number;
};

/** 对账单，去收款结果 */
export type StatementTransactionBillCreateVo = {
  /** 创建成功的收付款单据id */
  billId?: string;
  /** 客户名称 */
  customerName?: string;
  /** 创建成功的收付款单据编号 */
  billNo?: string;
  /** 需要异步的任务轮询id */
  taskId: string;
  success: boolean;
  failReason?: CheckResultParams;
};

export type UpdateStatementTagRequest = {
  /**
   * 对比对账单据id列表
   */
  contrastIdList?: string[];
  /**
   * 操作类型,1:新增,2:删除
   */
  operateType?: number;
  /**
   * 对账单id
   */
  statementId?: string;
  /**
   * 标签（名称）列表
   */
  tagList?: string[];
};

export type StatementDetailRestBillRemarkRequest = {
  /** 对账单id */
  statementId?: string;
  /** 单据id */
  contrastIdList?: string[];
  /** 备注 */
  remark?: string;
  /** 追加备注 */
  appendFlag: boolean;
  /** 操作类型 1:按勾选单据处理，2：按查询条件处理 */
  operateType: number;
  /** 查询条件 */
  goodsWithDetailQuery?: CommonRecord;
};

export type StatementRestBillRemarkRequest = {
  /** 对账单id */
  statementId?: string;
  /** 单据id */
  contrastIdList?: string[];
  /** 备注 */
  remark?: string;
  /** 追加备注 */
  appendFlag: boolean;
  /** 操作类型 1:按勾选单据处理，2：按查询条件处理 */
  operateType: number;
  /** 查询条件 */
  goodsReceivableQuery?: CommonRecord;
};

export type StatementRestOtherBillRemarkRequest = {
  /** 对账单id */
  statementId?: string;
  /** 单据id */
  contrastIdList?: string[];
  /** 备注 */
  remark?: string;
  /** 追加备注 */
  appendFlag: boolean;
  /** 操作类型 1:按勾选单据处理，2：按查询条件处理 */
  operateType: number;
  /** 查询条件 */
  otherReceivableQuery?: CommonRecord;
};

export type StatementRestBillDetailRemarkRequest = {
  /** 对账单id */
  statementId?: string;
  /** 单据详情 */
  detailUpdateDTOList?: {
    /** 单据详情id */
    detailId: string;
    /** 单据类型 */
    billType: string;
  };
  /** 备注 */
  remark?: string;
  /** 追加备注 */
  appendFlag: boolean;
  /** 操作类型 1:按勾选单据处理，2：按查询条件处理 */
  operateType: number;
  /** 查询条件 */
  goodsWithDetailQuery?: CommonRecord;
};

export type TransactionBillCreateOrderByStatementsRequest = {
  /** 业务类型 */
  businessType: string;
  /** 对账单id */
  statementIdList: string[];
  /** 客户id */
  customerId: string;
  /** 收款账户 */
  accountId: string;
  /** 对账单列表去收款需要额外添加的参数 */
  batchReceive?: boolean;
};
