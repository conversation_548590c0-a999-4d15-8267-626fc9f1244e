import {
  ConfigForm,
  Flex,
  Typography,
  type FormItemConfigDTO,
} from '@slt/base-ui';
import { type TaobaoSchemaDTO } from '@slt/services';
import { useMemo } from 'react';
import { formatSchemaToFormItem } from '../utils';
import { PublisherNameInputSelect } from './PublisherNameInputSelect';

interface Props {
  data?: TaobaoSchemaDTO[];
}

export const CategoryAttributes = (props: Props) => {
  const { data } = props;

  const list: FormItemConfigDTO[] = useMemo(() => {
    const formList =
      data?.map((item) =>
        formatSchemaToFormItem(['catProp'], item, data ?? []),
      ) ?? [];
    /**出版社名称使用InputSelect */
    const updatedFormList = formList.map((item) => {
      if (item.formItemProps?.label === '出版社名称') {
        const { type, ...rest } = item;
        return {
          ...rest,
          renderFormItem: () => {
            return (
              <PublisherNameInputSelect options={item?.fieldProps?.options} />
            );
          },
        };
      }
      return item;
    });

    return updatedFormList;
  }, [data]);

  return (
    <Flex vertical gap={12}>
      <Flex gap={4}>
        <Typography.Text className="w-[100px] text-right">
          类目属性
        </Typography.Text>
        <Typography.Text className="text-description">
          错误填写类目、属性，可能会引起商品下架，影响您的正常销售，请认真填写
        </Typography.Text>
      </Flex>
      {list.length ? (
        <Flex
          vertical
          className="my-sm ml-[84px] mr-[28px] bg-fill-quaternary p-sm"
        >
          <ConfigForm proportion={2} list={list} />
        </Flex>
      ) : null}
    </Flex>
  );
};
