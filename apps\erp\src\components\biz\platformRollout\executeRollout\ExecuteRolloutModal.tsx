import { routerMap } from '@/router/routerMap';
import {
  <PERSON>ton,
  CheckCircleFilled,
  CloseCircleFilled,
  Flex,
  message,
  Modal,
  Progress,
  Spin,
  Typography,
  type ModalProps,
} from '@slt/base-ui';
import { getValidatedValue, type CommonRecord } from '@slt/base-utils';
import { useMutation, useQuery } from '@slt/net-query';
import type {
  BasicPlatformRolloutTaskProgressVO,
  PlatformRolloutWayEnum,
} from '@slt/services';
import {
  basicOperateProductDistributionCheckDetail,
  basicPlatformRolloutExecuteRollout,
  basicPlatformRolloutTaskProgress,
} from '@slt/services';
import { windowOpen } from '@slt/utils';
import {
  forwardRef,
  Fragment,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
  type ReactElement,
} from 'react';
import { usePlatformRolloutModalContext } from '../contexts';
import type { ExecuteRolloutModalRef } from './types';
import {
  convertToFixedSpuData,
  convertToMultiPlatformFixedSpuData,
  groupByPlatformType,
  transformDistributionData,
} from './util';
import { cloneDeep, isEmpty } from 'lodash-es';
import { DistributeDetailsModal } from '@/pages/productListingMgmt/eCommercePlatformListingRecords/DistributeDetailsModal';
import { DuplicateProducts } from '../selectPlatform/constants';

type Props = {
  maskArea: ModalProps['maskArea'];
  /** 铺货入口 */
  rolloutWay: PlatformRolloutWayEnum;
};

/**
 * @description 铺货任务执行中
 * <AUTHOR>
 * @date 2025-01-13
 */
export const ExecuteRolloutModal = forwardRef<ExecuteRolloutModalRef, Props>(
  (props, ref) => {
    const { maskArea, rolloutWay } = props;

    const [modalOpen, setModalOpen] = useState(false);
    const [taskIds, setTaskIds] = useState<Array<string>>([]);
    const [isPolling, setIsPolling] = useState<boolean>();

    const {
      selectedPlatform,
      selectedShops,
      validTargetIds,
      duplicateProductsValue,
      isUpdateRollout,
      confirmUpdateProduct,
      editPriceSource,
      setEditPriceSource,
    } = usePlatformRolloutModalContext();

    //查看铺货明细id
    const recordIdRef = useRef('');
    const [checkDetail, setCheckDetail] = useState({});

    const { mutate: basicOperateProductDistributionCheckDetailMutate } =
      useMutation(basicOperateProductDistributionCheckDetail);

    const { data: taskCheckDetail } = useQuery(
      basicOperateProductDistributionCheckDetail(recordIdRef.current),
      {
        queryOptions: {
          enabled: modalOpen && !!recordIdRef.current && isPolling === false,
        },
      },
    );

    useEffect(() => {
      if (!isEmpty(taskCheckDetail)) {
        setCheckDetail(taskCheckDetail);
      }
    }, [taskCheckDetail]);

    const { data: taskProgress } = useQuery(
      basicPlatformRolloutTaskProgress({ taskIds }),
      {
        queryOptions: {
          enabled: modalOpen && isPolling !== false && !!taskIds.length,
          refetchInterval: 2000,
          refetchIntervalInBackground: true,
        },
      },
    );

    const titleText = useMemo(
      () => (isUpdateRollout ? '更新' : ''),
      [isUpdateRollout],
    );

    const modalTitle = useMemo(() => {
      return isPolling !== false
        ? `${titleText}铺货任务执行中`
        : `${titleText}铺货完成`;
    }, [isPolling, titleText]);

    const { mutate: mutateExecuteRollout } = useMutation(
      basicPlatformRolloutExecuteRollout,
    );

    const begin = useCallback(
      (values: CommonRecord) => {
        const params = cloneDeep(values);
        delete params.fixedSpuDatas;
        setModalOpen(true);

        const shopIds = selectedShops.map((r) => r.id);

        const skipRepeat = !isUpdateRollout
          ? {
              skipRepeat:
                duplicateProductsValue === DuplicateProducts['跳过重复商品']
                  ? true
                  : false,
            }
          : {};

        const forUpdateDatas = isUpdateRollout
          ? { forUpdateDatas: transformDistributionData(confirmUpdateProduct) }
          : {};

        const multiShopIds = isUpdateRollout
          ? groupByPlatformType(confirmUpdateProduct)
          : { [selectedPlatform.key]: shopIds };

        const isMulitPlatform =
          isUpdateRollout && Object.keys(multiShopIds).length > 1;

        const multiActualRolloutConf = isUpdateRollout
          ? params.formValues.reduce((acc, config) => {
              acc[config.key] = JSON.stringify(config.values);
              return acc;
            }, {})
          : { [selectedPlatform.key]: JSON.stringify(params) };

        const multiTargetIds = isUpdateRollout
          ? confirmUpdateProduct.reduce((acc, item) => {
              const platformType = item.platformType;
              const platformDistributionId = item.platformDistributionId;

              if (!acc[platformType]) {
                acc[platformType] = [];
              }

              if (!acc[platformType].includes(platformDistributionId)) {
                acc[platformType].push(platformDistributionId);
              }

              return acc;
            }, {})
          : { [selectedPlatform.key]: validTargetIds };

        let requestParams = {};

        if (isMulitPlatform) {
          /**
           * 多平台铺货参数
           */
          requestParams = {
            rolloutWay,
            multiShopIds,
            multiTargetIds,
            multiActualRolloutConf,
            multiFixedSpuDatas: convertToMultiPlatformFixedSpuData(
              values?.fixedSpuDatas,
            ),
            optType: 2,
            actionType: 2,
            ...forUpdateDatas,
            ...skipRepeat,
          };
        } else {
          /**
           * 单平台铺货参数
           */
          requestParams = {
            rolloutWay,
            platformType: Number(Object.keys(multiShopIds)[0]),
            shopIds: Object.values(multiShopIds)[0],
            targetIds: Object.values(multiTargetIds)[0],
            actualRolloutConf: Object.values(multiActualRolloutConf)[0],
            fixedSpuDatas: convertToFixedSpuData(
              Object.values(values?.fixedSpuDatas)[0],
            ),
            optType: isUpdateRollout ? 2 : 1,
            // actionType: 1,
            ...forUpdateDatas,
            ...skipRepeat,
          };
        }

        mutateExecuteRollout(requestParams)
          .then((res) => {
            const { taskIds } = res.data ?? {};
            if (taskIds?.length) {
              setTaskIds(taskIds);
            }
          })
          .catch(() => {})
          .finally(() => {
            setEditPriceSource([]);
          });
      },
      [
        confirmUpdateProduct,
        duplicateProductsValue,
        isUpdateRollout,
        mutateExecuteRollout,
        rolloutWay,
        selectedPlatform,
        selectedShops,
        setEditPriceSource,
        validTargetIds,
      ],
    );

    const closeModal = useCallback(() => {
      setModalOpen(false);
      setIsPolling(false);
      recordIdRef.current = '';
    }, []);

    const renderProgressItem = useCallback(
      (taskInfo: BasicPlatformRolloutTaskProgressVO): ReactElement => {
        let percent: number;

        if (taskInfo.successCount + taskInfo.failCount === 0) {
          percent = 0;
        } else if (
          taskInfo.successCount + taskInfo.failCount ===
          taskInfo.totalCount
        ) {
          percent = 100;
        } else {
          percent = Math.round(
            ((taskInfo.successCount + taskInfo.failCount) /
              taskInfo.totalCount) *
              100,
          );
        }

        return (
          <Flex vertical gap={16}>
            <Flex
              vertical={false}
              align="center"
              justify="space-between"
              gap={24}
            >
              <Typography.Text className="text-sm text-label" ellipsis>
                铺货店铺：{getValidatedValue(taskInfo.shopName)}
              </Typography.Text>

              <Flex className="shrink-0" vertical={false} gap={24}>
                <Typography.Text className="text-sm text-label">
                  铺货商品数量：
                  <Typography.Text className="text-lg text-text">
                    {getValidatedValue(taskInfo.totalCount)}
                  </Typography.Text>
                </Typography.Text>

                <Flex vertical={false} align="center" gap={4}>
                  <CheckCircleFilled className="text-lg text-success" />
                  <Typography.Text className="text-sm text-label">
                    成功：
                    <Typography.Text className="text-lg text-text">
                      {taskInfo.successCount}
                    </Typography.Text>
                  </Typography.Text>
                </Flex>

                <Flex vertical={false} align="center" gap={4}>
                  <CloseCircleFilled className="text-lg text-error" />
                  <Typography.Text className="text-sm text-label">
                    失败：
                    <Typography.Text className="text-lg text-text">
                      {taskInfo.failCount}
                    </Typography.Text>
                  </Typography.Text>
                </Flex>
              </Flex>
            </Flex>
            <Progress
              percent={percent}
              status={percent < 100 ? 'active' : 'success'}
            />
          </Flex>
        );
      },
      [],
    );

    useEffect(() => {
      if (taskProgress?.length) {
        recordIdRef.current = taskProgress[0]?.recordId;
      }

      const allFinished = taskProgress?.every((r) => {
        return r.successCount + r.failCount >= r.totalCount;
      });
      setIsPolling(allFinished !== true);
    }, [taskProgress]);

    useImperativeHandle(ref, () => ({ begin }));

    const footerRender = useMemo(() => {
      if (isPolling !== false) {
        return (
          <Flex justify="end" gap={4}>
            <Button type="primary" onClick={closeModal}>
              关闭弹窗，等待后台自动上架
            </Button>
          </Flex>
        );
      } else {
        return (
          <Flex justify="end" gap={4}>
            <Button onClick={closeModal}>关闭</Button>

            <DistributeDetailsModal
              closeExecuteRolloutModal={closeModal}
              rowData={checkDetail}
              isUpdate={isUpdateRollout}
              triggerRender={
                <Button type="primary">
                  {`查看${isUpdateRollout ? '更新' : '铺货'}结果`}
                </Button>
              }
            />
          </Flex>
        );
      }
    }, [checkDetail, closeModal, isPolling, isUpdateRollout]);

    return (
      <Modal
        title={modalTitle}
        type="edit"
        size="m"
        maskArea={maskArea}
        open={modalOpen}
        destroyOnClose
        okButtonProps={{ style: { display: 'none' } }}
        // cancelText="关闭弹窗，等待后台自动上架"
        onCancel={closeModal}
        footer={() => footerRender}
      >
        <Flex className="max-h-[450px] pr-sm" vertical gap={16}>
          <Typography.Text className="text-base">
            {`${titleText}铺货完成后即可进入店铺查看商品`}
          </Typography.Text>

          {taskProgress?.length ? (
            taskProgress.map((r) => {
              return (
                <Fragment key={r.taskId}>{renderProgressItem(r)}</Fragment>
              );
            })
          ) : (
            <Spin spinning />
          )}

          <Typography.Text type="secondary">
            可以前往
            <Typography.Link
              onClick={() => {
                windowOpen({
                  route: routerMap.productListingMgmt.route,
                  params: { activeTab: 'eCommercePlatformListingRecords' },
                });
              }}
            >
              【电商平台铺货记录】
            </Typography.Link>
            查看结果
          </Typography.Text>
        </Flex>
      </Modal>
    );
  },
);
