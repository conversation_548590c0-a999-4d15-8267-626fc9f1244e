import { StatisticsItem } from '@/components/biz/StatisticsItem';
import {
  ApiSelect,
  Button,
  Flex,
  Form,
  Modal,
  msgError,
  msgInfo,
  Typography,
  type ButtonProps,
} from '@slt/base-ui';
import {
  useCallback,
  useMemo,
  useState,
  type Dispatch,
  type SetStateAction,
} from 'react';
import {
  basicFinanceSettleAccountQueryDefaultAccount,
  financeSettleAccountSelectInputV2,
} from '@slt/services';
import { useQuery } from '@slt/net-query';
import { useOpenTab } from '@/hooks/useOpenTab';
import { routerMap } from '@/router/routerMap';

export enum TransactionObjectTypeEnum {
  '客户' = 1,
  '供应商' = 2,
}

type GoTransactionModalProps = {
  /** 是否是对账单的去收款 */
  isStatement?: boolean;
  /** 交易对象类型，1、客户，2、供应商 */
  type: TransactionObjectTypeEnum;
  /** 弹窗信息展示需要的信息 */
  dataInfo: GoTransactionDataInfo;
  buttonProps?: ButtonProps;
  /** 打开弹窗的方法 */
  handleOpen: (
    setModalVisible: Dispatch<SetStateAction<boolean>>,
  ) => void | Promise<void>;
  /** 点击确认后需要执行的异步（会直接关闭弹窗）操作 */
  handleConfirm: (values: GoTransactionFieldType) => void | Promise<void>;
};

export type GoTransactionDataInfo = {
  /** 本次交易单数 */
  count?: number;
  /** 本次待收金额 */
  waitAmount?: number;
  /** 本次收款金额 */
  amount?: number;
  /** 已交易的单据单号 */
  transactionIds: string[];
  /** 被过滤的单据数量 */
  filteredCount: number;
};

export type GoTransactionFieldType = {
  /** 本次收款账户 */
  accountId: string;
};

type MsgListType = {
  msg: string;
  onClick?: () => void;
};

export const GoTransactionModal = (props: GoTransactionModalProps) => {
  const {
    isStatement = false,
    type,
    dataInfo,
    buttonProps,
    handleOpen,
    handleConfirm,
  } = props;

  const { openTab } = useOpenTab();

  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();

  const typeText = useMemo(
    () => (type === TransactionObjectTypeEnum.客户 ? '收' : '付'),
    [type],
  );

  const { data: accountData } = useQuery(
    basicFinanceSettleAccountQueryDefaultAccount(),
  );

  const goToReceiptOrPaymentByType = useCallback(() => {
    if (type === TransactionObjectTypeEnum.客户) {
      openTab(routerMap.receivedBill.route, {
        tabSearch: {
          businessBillNoList: dataInfo.transactionIds,
        },
      });
    } else {
      openTab(routerMap.payBillList.route, {
        tabSearch: {
          businessBillNoList: dataInfo.transactionIds,
        },
      });
    }
  }, [openTab, type, dataInfo.transactionIds]);

  const msgList = useMemo(() => {
    if (dataInfo.transactionIds.length === 0 && dataInfo.filteredCount === 0) {
      return;
    }

    const list: MsgListType[] = [];

    if (dataInfo.transactionIds.length > 0) {
      list.push({
        msg: `已自动过滤${dataInfo.transactionIds.length}条被其他待审核${typeText}款单${typeText}款的单据`,
        onClick: goToReceiptOrPaymentByType,
      });
    }
    if (dataInfo.filteredCount > 0) {
      const msg = isStatement
        ? `已自动过滤${dataInfo.filteredCount}条待${typeText}金额为0的单据，可在对账单详情【商品应${typeText}-单据明细】【其他应${typeText}及调账】通过${typeText}款状态=已${typeText}款查询出来`
        : `已自动过滤${dataInfo.filteredCount}条待${typeText}金额为0的单据，可通过${typeText}款状态=已${typeText}款查询出来`;
      list.push({
        msg,
      });
    }
    return list;
  }, [isStatement, dataInfo, typeText, goToReceiptOrPaymentByType]);

  const handleClose = () => {
    setModalVisible(false);
    form.resetFields();
  };

  const handleSubmit = (values: GoTransactionFieldType) => {
    if (!values.accountId) {
      msgError(`请选择本次${typeText}款账户`);
      return;
    }

    msgInfo(`正在生成${typeText}款单，请稍后`);
    handleClose();
    void handleConfirm(values);
  };

  return (
    <Modal
      title={`去${typeText}款`}
      type="edit"
      size="l"
      open={modalVisible}
      onOk={() => form.submit()}
      onCancel={handleClose}
      trigger={
        <Button {...buttonProps} onClick={() => handleOpen(setModalVisible)}>
          {`去${typeText}款`}
        </Button>
      }
    >
      <Flex vertical>
        <Flex justify="space-between" className="mb-xl">
          <StatisticsItem
            label={`本次${typeText}款单数`}
            value={dataInfo.count}
          />
          <StatisticsItem
            label={`本次待${typeText}金额`}
            value={dataInfo.waitAmount}
          />
          <StatisticsItem
            label={`本次${typeText}款金额`}
            value={dataInfo.amount}
          />
        </Flex>
        <Form
          enableUnsavedWarning={false}
          form={form}
          onFinish={handleSubmit}
          wrapperCol={{ span: 10 }}
          autoComplete="off"
          initialValues={{
            accountId: accountData?.id,
          }}
        >
          <Form.Item
            label={
              <Typography.Text className="text-base">
                本次{typeText}款账户：
              </Typography.Text>
            }
            name="accountId"
          >
            <ApiSelect
              request={financeSettleAccountSelectInputV2}
              allowClear={false}
            />
          </Form.Item>
        </Form>
        {msgList ? (
          <Typography.Text type="secondary">
            <Typography.Text className="font-strong text-error">
              注：
            </Typography.Text>
            <br />
            {msgList.map((text, index) => (
              <Typography.Text className="font-strong text-error" key={index}>
                {index + 1}、{text.msg}
                {text.onClick ? (
                  <Typography.Link onClick={text.onClick} className="ml-md">
                    去查看
                  </Typography.Link>
                ) : null}
                <br />
              </Typography.Text>
            ))}
          </Typography.Text>
        ) : null}
      </Flex>
    </Modal>
  );
};
