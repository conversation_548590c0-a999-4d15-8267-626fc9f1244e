import {
  useCallback,
  useEffect,
  useMemo,
  useState,
  type SyntheticEvent,
} from 'react';
import type { ResizeCallbackData } from 'react-resizable';
import { ResizableTitle } from '../components/resizableTitle/ResizableTitle';
import '../components/resizeTableHeader/style.css';
import type { TableColumnType, TableProps } from '../types';
import { useStyles } from '../useStyles';
import { PLACEHOLDER } from '../utils/constant';
import { useLayoutChange } from './useLayoutChange';

/**
 * 可调整列宽的表格
 * 会额外添加一列 { key: PLACEHOLDER }，不设置 列宽，作为适应列宽的占位符
 * 可通过 resizableHeader 配置是否开启
 */
export const useResizableHeader = <RecordType>(
  props: TableProps<RecordType>,
  propsColumns: TableColumnType<RecordType>[],
) => {
  const { resizableHeader = true, id } = props;

  const [tableColumns, setTableColumns] = useState<
    TableColumnType<RecordType>[]
  >([]);

  const { styles } = useStyles();
  const { onColumnsWidthChange, onSaveLayoutConfig } = useLayoutChange(id);

  const handleResize = useCallback(
    (index: number) => {
      return (e: SyntheticEvent, { size }: ResizeCallbackData) => {
        setTableColumns((pre) => {
          const nextColumns = [...pre];
          nextColumns[index] = {
            ...nextColumns[index],
            width: size.width,
          };

          onColumnsWidthChange({
            key: nextColumns[index].key,
            width: size.width,
          });
          onSaveLayoutConfig();

          return nextColumns;
        });
      };
    },
    [onColumnsWidthChange, onSaveLayoutConfig],
  );

  const formatColumns = useMemo(
    () =>
      [
        ...(propsColumns ?? []),
        {
          key: PLACEHOLDER,
          fixed: 'right',
          // placeholder 极限情况下始终会有宽度问题
          className: '!p-0',
        } as TableColumnType<RecordType>,
      ]
        .map((col, index) => ({
          ...col,
          onHeaderCell: resizableHeader
            ? (column: { width: number }) => ({
                width: column.width,
                onResize: handleResize(index),
              })
            : undefined,
        }))
        .filter((item) => !item.hidden) as TableColumnType<RecordType>[],
    [handleResize, propsColumns, resizableHeader],
  );

  useEffect(() => {
    setTableColumns(formatColumns);
  }, [formatColumns]);

  const resizableComponents = useMemo(() => {
    return {
      header: {
        cell: ResizableTitle,
      },
    };
  }, []);

  const components = useMemo(() => {
    return resizableHeader ? resizableComponents : undefined;
  }, [resizableHeader, resizableComponents]);

  return {
    columns: tableColumns,
    components,
    className: styles.table,
  };
};
