import { createStyles } from 'antd-style';

export const useStyle = createStyles(({ css }) => {
  return {
    react_resizable_handle: css`
      position: absolute;
      right: -5px;
      bottom: 0;
      z-index: 1;
      width: 10px;
      height: 100%;
      cursor: col-resize;

      &:hover::before {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 50%;
        border-left: solid 2px #000;
        content: '';
      }
    `,
    active: css`
      &::before {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 50%;
        border-left: solid 2px #000;
        content: '';
      }
    `,
  };
});
