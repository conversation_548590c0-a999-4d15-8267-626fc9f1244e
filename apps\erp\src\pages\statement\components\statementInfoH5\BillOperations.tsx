import {
  Button,
  Flex,
  Space,
  Spin,
  SyncOutlined,
  Typography,
} from '@slt/base-ui';
import { TimeSetModal } from '../modal/TimeSetModal';
import {
  ContainReceiverInfoValue,
  ShareConfigTypeEnum,
  StatementInfoTypeEnum,
  StatementTypeEnum,
  type StatementInfoType,
  type StatementType,
} from '../../types';
import { useBillOperation } from '../../hooks/useBillOperation';
import {
  anonStatementBillDownload,
  anonStatementDownloadByTaskId,
  statementBillDownload,
  statementDownloadByTaskId,
  type StatementDetailBillInfoVO,
} from '@slt/services';
import {
  BillBusinessType,
  downloadFile,
  StatementBillStatusType,
} from '@slt/utils';
import { ShareModal } from '../modal/ShareModal';
import { BillConfigModal } from '../modal/BillConfigModal';
import { useMutation } from '@slt/net-query';
import { useMemo, useState } from 'react';
import { formatTimeRange } from '../../utils/formatData';
import { UploadInvoiceModal } from '../modal/UploadInvoiceModal';
import { useOpenTab } from '@/hooks/useOpenTab';
import { routerMap } from '@/router/routerMap';
import { transformInvoiceAttachment } from '../../utils';
import { authIdsMap } from '@/constants/authIds';
import useFileDownloadPolling from '../../hooks/useFileDownloadPolling';
import { SPIN_TIP } from '../../utils/constants';
import { useStatementDetailContextContext } from '../../utils/PageContext';
import { GoTransactionModal } from '@/components/biz';

type BillOperationsProps = {
  /** 对账单类型：1、客户，2、供应商 */
  type: StatementType;
  /** 对账单信息类型：1、详情，2、分享 */
  infoType: StatementInfoType;
  billId: string;
  billInfoData: StatementDetailBillInfoVO | undefined;
  reload: () => void;
};

export const BillOperations = (props: BillOperationsProps) => {
  const { type, infoType, billId, billInfoData, reload } = props;
  const {
    dataInfo,
    handleOpenGoTransactionModalByDetail,
    handleGoTransactionConfirm,
    handleVerifyStatement,
    handleMarkFinishStatement,
  } = useBillOperation({ type, infoType });
  const { getGoodsHeaderStatistics, refreshTableRef, handleCheckRemove } =
    useStatementDetailContextContext();
  const { mutate: mutateStatementBillDownload } = useMutation(
    infoType === StatementInfoTypeEnum.分享
      ? anonStatementBillDownload
      : statementBillDownload,
  );
  const { mutate: mutateStatementDownloadByTaskId } = useMutation(
    infoType === StatementInfoTypeEnum.分享
      ? anonStatementDownloadByTaskId
      : statementDownloadByTaskId,
  );
  const { isPolling, startPolling } = useFileDownloadPolling({
    requestMethod: mutateStatementDownloadByTaskId,
  });
  const { openTab } = useOpenTab();
  // 下载账单配置是否显示
  const [showShareConfig, setShowShareConfig] = useState<boolean>(false);

  const uploadInvoiceValues = useMemo(() => {
    return {
      invoiceNo: billInfoData?.invoiceNo,
      remark: billInfoData?.remark,
      invoiceAttachmentList: transformInvoiceAttachment(
        billInfoData?.attachmentUrl,
      ),
    };
  }, [billInfoData]);

  // 去调账页面
  const handleToAdjustBill = () => {
    const tabSearch = {
      statementId: billId,
      businessType:
        type === StatementTypeEnum.客户
          ? BillBusinessType.应收调账
          : BillBusinessType.应付调账,
      // 调账对象
      customer: {
        label: billInfoData?.merchantName,
        value: billInfoData?.merchantId,
      },
    };
    openTab(routerMap.adjustmentBillAdd.route, {
      tabSearch,
    });
  };

  const handleRefresh = () => {
    reload();
    getGoodsHeaderStatistics();
    refreshTableRef.current?.current?.reload();
  };

  const getDownloadTaskId = async (containReceiverInfo: number) => {
    const res = await mutateStatementBillDownload({
      statementId: billId,
      containReceiverInfo,
    });
    if (res.success && res.data) {
      return res.data;
    }
  };

  const getFileUrl = async (containReceiverInfo: number) => {
    const taskId = await getDownloadTaskId(containReceiverInfo);
    if (taskId) {
      const downloadUrl = await startPolling(taskId);
      if (downloadUrl) {
        return downloadUrl;
      }
    }
  };

  const handleDownloadBill = async (containReceiverInfo: number) => {
    const url = await getFileUrl(containReceiverInfo);
    const { statementStartTime = '', statementEndTime = '' } =
      billInfoData || {};
    const time = formatTimeRange([statementStartTime, statementEndTime]);
    const billName = StatementTypeEnum[type];
    const fileName = `${billName}对账单$${time}`;
    if (url) {
      downloadFile(url, fileName);
    }
  };

  const handleDownload = async () => {
    if (infoType === StatementInfoTypeEnum.分享) {
      if (handleCheckRemove) {
        const isDelete = await handleCheckRemove();
        if (isDelete) {
          return;
        }
      }

      void handleDownloadBill(ContainReceiverInfoValue.包含收货信息);
    } else {
      setShowShareConfig(true);
    }
  };

  const VerifyStatementBtn = (
    <Button
      className="text-sm"
      type="primary"
      onClick={() => handleVerifyStatement(true, reload, [billId])}
      {...(infoType === StatementInfoTypeEnum.详情
        ? {
            authId:
              type === StatementTypeEnum.客户
                ? authIdsMap.clientStatement.confirm
                : authIdsMap.supplierStatement.confirm,
          }
        : {})}
    >
      核实账单
    </Button>
  );
  const AdjustBtn = <Button onClick={handleToAdjustBill}>调账</Button>;
  const TimeSetBtn = (
    <TimeSetModal selectedIds={[billId]} type={type} cb={reload} />
  );
  const DownloadBtn = (
    <Button className="text-sm" onClick={handleDownload}>
      下载账单
    </Button>
  );
  // const DownloadBtn = null;
  const ShareBtn = <ShareModal id={billId} type={type} needTriggerBtn />;
  const MarkFinishBtn = (
    <Button
      type="primary"
      onClick={() => handleMarkFinishStatement(true, reload, [billId])}
    >
      标记对账完成
    </Button>
  );
  const GoReceiptOrPaymentBtn = (
    <GoTransactionModal
      isStatement
      type={type}
      dataInfo={dataInfo}
      handleOpen={handleOpenGoTransactionModalByDetail}
      handleConfirm={handleGoTransactionConfirm}
    />
  );
  const UploadBtn = (
    <UploadInvoiceModal
      type={type}
      selectedId={billId}
      cb={reload}
      initialValues={uploadInvoiceValues}
    />
  );

  const operationBtns = () => {
    if (infoType === StatementInfoTypeEnum.分享) {
      switch (billInfoData?.statementStatus) {
        case StatementBillStatusType.待核实:
          return [VerifyStatementBtn, DownloadBtn];
        case StatementBillStatusType.结算中:
        case StatementBillStatusType.已完成:
          return DownloadBtn;

        default:
          return [];
      }
    }

    switch (billInfoData?.statementStatus) {
      case StatementBillStatusType.待核实:
        return [
          VerifyStatementBtn,
          AdjustBtn,
          TimeSetBtn,
          DownloadBtn,
          ShareBtn,
        ];
      case StatementBillStatusType.结算中:
        return [
          MarkFinishBtn,
          GoReceiptOrPaymentBtn,
          TimeSetBtn,
          DownloadBtn,
          ShareBtn,
        ];
      case StatementBillStatusType.已完成:
        return [UploadBtn, DownloadBtn, ShareBtn];

      default:
        return [];
    }
  };

  return (
    <Flex justify="space-between" className="bg-white px-base py-xs">
      <Space size="small">
        {operationBtns()}
        <BillConfigModal
          title="下载账单配置"
          modalVisible={showShareConfig}
          setModalVisible={setShowShareConfig}
          cb={handleDownloadBill}
          configType={ShareConfigTypeEnum.下载}
        />
        <Spin spinning={isPolling} fullscreen tip={SPIN_TIP} />
      </Space>
      <Flex
        className="cursor-pointer select-none text-primary"
        align="center"
        justify="center"
        onClick={handleRefresh}
      >
        <SyncOutlined className="mr-[4px] text-lg" />
        <Typography.Text className="text-base text-primary">
          刷新
        </Typography.Text>
      </Flex>
    </Flex>
  );
};
