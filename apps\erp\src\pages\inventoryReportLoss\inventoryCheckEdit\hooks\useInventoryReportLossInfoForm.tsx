import type { EditableTableColumnType, FormItemConfigDTO } from '@slt/base-ui';
import {
  ColumnWidth,
  inputNumberProps,
  TableOperationGroup,
} from '@slt/base-ui';

import {
  iacUserSelectInputMore,
  type DepartmentListAllDTO,
} from '@slt/services';
import { maxInputDecimal } from '@slt/utils';

import dayjs from 'dayjs';
import { useMemo } from 'react';
import type { LossSpillBillDetailListDTO } from '../../type';

export const useInventoryReportLossInfoForm = (
  isDetail: boolean,
  departmentOptions: DepartmentListAllDTO[],
  billNoRepeatRef: React.RefObject<string | null>,
  handleDelete: (row: LossSpillBillDetailListDTO) => void,
) => {
  const formColumns: FormItemConfigDTO[] = useMemo(() => {
    return [
      {
        key: 'billNo',
        type: 'input',
        fieldProps: {
          maxLength: 20,
        },
        formItemProps: {
          label: '单据编号',
          name: 'billNo',
          rules: [
            { required: true, message: '请输入单据编号' },
            () => ({
              validator(_, value) {
                if (!value || !billNoRepeatRef.current) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error(billNoRepeatRef.current));
              },
            }),
          ],
        },
      },
      {
        key: 'billDateTime',
        type: 'datePicker',
        fieldProps: {
          showTime: true,
          needMinDate: true,
        },
        formItemProps: {
          label: '单据日期',
          name: 'billDateTime',
          rules: [{ required: true, message: '请选择单据日期' }],
          initialValue: dayjs(),
        },
      },
      {
        key: 'warehouseId',
        type: 'warehouseSelect',
        fieldProps: {
          maxTagCount: 'responsive',
          fieldNames: { label: 'label', value: 'value' },
          labelInValue: true,
          params: {
            permission: !isDetail,
            enable: true,
            filterOwnerEnableWarehouse: true,
          },
        },
        formItemProps: {
          label: '仓库',
          name: 'warehouseId',
          rules: [{ required: true, message: '请选择仓库' }],
        },
      },

      {
        key: 'salesManId',
        type: 'staffSelect',
        fieldProps: {
          allowClear: true,
          labelInValue: true,
          params: {
            enable: true,
          },
          placeholder: '请选择业务员',
          fieldNames: { label: 'label', value: 'value' },
          request: iacUserSelectInputMore,
        },
        formItemProps: {
          label: '业务员',
          name: 'salesManId',
        },
      },
      {
        key: 'departmentId',
        type: 'cascader',
        fieldProps: {
          placeholder: '请选择部门',
          allowClear: true,
          changeOnSelect: true,
          fieldNames: { label: 'title', value: 'key' },
          options: departmentOptions,
        },
        formItemProps: {
          label: '部门',
          name: 'departmentId',
        },
      },
      {
        key: 'remark',
        type: 'textarea',
        formItemProps: {
          label: '单据备注',
          name: 'remark',
        },
        fieldProps: {
          maxLength: 200,
        },
      },
    ];
  }, [isDetail, departmentOptions, billNoRepeatRef]);

  const tableColumns = useMemo(() => {
    const columns: EditableTableColumnType<LossSpillBillDetailListDTO>[] = [
      {
        width: ColumnWidth.RowIndex,
        title: '序号',
        dataIndex: 'idx',
        key: 'idx',
        editable: false,
        render: (_, idx) => Number(idx || 0) + 1,
      },

      {
        width: ColumnWidth.Common,
        title: '商品编码',
        dataIndex: 'goodsCode',
        key: 'goodsCode',
        editable: false,
      },

      {
        width: ColumnWidth.Common,
        title: '商品名称',
        dataIndex: 'goodsName',
        key: 'goodsName',
        editable: false,
      },
      {
        width: ColumnWidth.Common,
        title: '规格编码',
        key: 'skuCode',
        dataIndex: 'skuCode',
        editable: false,
      },
      {
        width: ColumnWidth.Common,
        title: '规格名称',
        key: 'skuName',
        dataIndex: 'skuName',
        editable: false,
      },
      {
        title: 'ISBN',
        key: 'isbn',
        dataIndex: 'isbn',
        width: 120,
        editable: false,
      },
      {
        title: '出版社',
        key: 'publisherName',
        dataIndex: 'publisherName',
        width: 120,
        editable: false,
      },
      {
        width: ColumnWidth.Number,
        title: '定价',
        key: 'pricingPrice',
        dataIndex: 'pricingPrice',
        editable: false,
      },
      {
        title: '品牌',
        key: 'brandName',
        dataIndex: 'brandName',
        width: 120,
        editable: false,
      },
      {
        width: ColumnWidth.Common,
        title: '数量',
        batchModify: !isDetail,
        key: 'stock',
        dataIndex: 'stock',
        valueType: 'inputNumber',
        formItemProps: {
          rules: [{ required: true, message: '请输入数量' }],
          validateTrigger: ['onChange', 'onBlur'],
        },
        fieldProps: {
          ...inputNumberProps.int,
          min: 1,
          max: maxInputDecimal,
        },
      },
      {
        width: ColumnWidth.Common,
        title: '成本单价',
        key: 'costAmount',
        dataIndex: 'costAmount',
        editable: false,
      },
      {
        width: ColumnWidth.Common,
        title: '成本金额',
        key: 'totalAmount',
        dataIndex: 'totalAmount',
        editable: false,
      },
      {
        width: ColumnWidth.Remark,
        title: '备注',
        key: 'remark',
        dataIndex: 'remark',
        fieldProps: {
          maxLength: 200,
        },
      },
      {
        width: ColumnWidth.Action,
        fixed: 'right',
        title: '操作',
        key: 'actions',
        render: (record: LossSpillBillDetailListDTO) => {
          return (
            <TableOperationGroup
              operationList={[
                {
                  label: (
                    <a
                      onClick={() => {
                        handleDelete(record);
                      }}
                    >
                      删除
                    </a>
                  ),
                  key: 'del',
                  show: !isDetail,
                },
              ]}
            />
          );
        },
      },
    ];

    return columns;
  }, [isDetail, handleDelete]);

  return {
    formColumns,
    tableColumns,
  };
};
