import {
  CheckBeforeEdit,
  DeleteReceipt,
  ModifyDatePickerChoose,
} from '@/components/biz';
import { authIdsMap } from '@/constants/authIds';
import { getSourceTypesByBill } from '@/utils/bill';
import type { TableColumnType } from '@slt/base-ui';
import {
  inputNumberProps,
  TableOperationGroup,
  Typography,
} from '@slt/base-ui';
import type { BizTableRef, QueryFormListItem } from '@slt/biz-ui';
import {
  billCheckCanEditOrDeleteSaleBill,
  billDeleteSaleBill,
  billQuerySaleBusinessTypes,
  distributorMerchantSelectInputMore,
  iacUserSelectInputMore,
  stockSaleBillPageQuery,
  type BillSalePageQueryDTO,
} from '@slt/services';
import {
  BillSourceType,
  BillTypeEnum,
  CommonBillStatusType,
  enumToOptions,
} from '@slt/utils';
import dayjs from 'dayjs';
import type { RefObject } from 'react';

export const getColumns = (props: {
  tableRef: RefObject<BizTableRef<BillSalePageQueryDTO>>;
  linkToOutWarehouseDetail: (id: string) => void;
}): TableColumnType<BillSalePageQueryDTO>[] => {
  const { tableRef, linkToOutWarehouseDetail } = props;
  return [
    {
      width: 'billNo',
      title: '单据编号',
      key: 'billNo',
      render: (value, record) => {
        const { id } = record;
        return (
          <Typography.Link
            onClick={() => linkToOutWarehouseDetail(id)}
            authId={{
              authId: '2049',
              authType: 'text',
            }}
          >
            {value}
          </Typography.Link>
        );
      },
    },
    {
      width: 'time',
      title: '单据日期',
      key: 'billDateTime',
    },
    {
      title: '单据类型',
      key: 'billTypeText',
    },
    {
      title: '业务类型',
      key: 'businessTypeText',
    },
    {
      title: '仓库',
      key: 'warehouseName',
    },
    {
      title: '客户',
      key: 'consumerName',
    },
    {
      title: '店铺',
      key: 'shopName',
    },
    {
      width: 'number',
      title: '单据金额',
      key: 'discountTotalAmount',
    },
    {
      width: 'number',
      title: '本单欠款',
      key: 'receivedArrears',
    },
    {
      width: 'number',
      title: '运费收入',
      key: 'freightAmount',
    },
    {
      width: 'number',
      title: '实际运费成本',
      key: 'freightCostAmount',
    },
    {
      width: 'number',
      title: '单据优惠金额',
      key: 'discountAmount',
    },
    {
      width: 'number',
      title: '单据商品成本',
      key: 'billCostAmount',
    },
    {
      width: 'number',
      title: '毛利',
      key: 'netProfitAmount',
    },
    {
      width: 'billNo',
      title: '原始订单号',
      key: 'resourcePlatformBillNo',
    },
    {
      width: 'billNo',
      title: '系统单号',
      key: 'resourceBillNo',
    },
    {
      title: '业务员',
      key: 'salesManName',
    },
    {
      width: 'time',
      title: '创建时间',
      key: 'gmtCreate',
    },
    {
      width: 'time',
      title: '最后更新时间',
      key: 'gmtModified',
    },
    {
      width: 'time',
      title: '打印时间',
      key: 'printTime',
    },
    {
      title: '单据状态',
      key: 'billStatusText',
    },
    {
      title: '单据来源',
      key: 'createTypeText',
    },
    {
      width: 'time',
      title: '还款到期时间',
      key: 'settlementTime',
      render: (value: string, record) => {
        return (
          <ModifyDatePickerChoose
            tableRef={tableRef}
            record={record}
            value={value}
            updateFunc={stockSaleBillPageQuery}
            authId={authIdsMap.outWarehouseManagement.edit}
          />
        );
      },
    },
    {
      width: 'time',
      title: '剩余还款天数',
      key: 'settlementDaysRemaining',
    },
    {
      fixed: 'right',
      width: 'action',
      title: '操作',
      key: 'operate',
      needExport: false,
      render: (_, record) => {
        const { id } = record;
        return (
          <TableOperationGroup
            operationList={[
              {
                label: (
                  <CheckBeforeEdit
                    authId="785"
                    receiptType={BillTypeEnum.销售出库单}
                    id={id}
                    beforeCheckRequest={billCheckCanEditOrDeleteSaleBill}
                  />
                ),
                key: 'edit',
                show: true,
                authId: '785',
              },
              {
                label: (
                  <DeleteReceipt
                    authId="787"
                    receiptType={BillTypeEnum.销售出库单}
                    id={id}
                    tableRef={tableRef}
                    beforeCheckRequest={billCheckCanEditOrDeleteSaleBill}
                    deleteRequest={billDeleteSaleBill}
                  />
                ),
                key: 'del',
                show: true,
                authId: '787',
              },
            ]}
          />
        );
      },
    },
  ];
};

export const getSearchColumns = (
  hasPrintCoordination: boolean,
): QueryFormListItem[] => {
  const createTypeListOptions = getSourceTypesByBill('销售出库单');
  return [
    {
      type: 'input',
      formItemProps: {
        label: '单据/订单编号',
        name: 'no',
      },
    },
    {
      type: 'clientSelect',
      fieldProps: {
        fieldNames: { label: 'label', value: 'value' },
        request: distributorMerchantSelectInputMore,
      },
      formItemProps: {
        label: '客户名称',
        name: 'consumerId',
      },
    },
    {
      type: 'warehouseSelect',
      fieldProps: {
        fieldNames: { label: 'label', value: 'value' },
      },
      formItemProps: {
        label: '仓库',
        name: 'warehouseId',
      },
    },
    {
      type: 'rangePicker',
      fieldProps: {
        needMinDate: true,
        fieldNames: {
          start: 'billDateTimeStart',
          end: 'billDateTimeEnd',
        },
        showTime: {
          defaultValue: [
            dayjs('00:00:00', 'HH:mm:ss'),
            dayjs('23:59:59', 'HH:mm:ss'),
          ],
        },
      },
      formItemProps: {
        label: '单据日期',
        name: 'billDateTime',
        initialValue: [
          dayjs().startOf('day').subtract(6, 'd'),
          dayjs().endOf('day'),
        ],
      },
    },
    {
      key: 'shopId',
      type: 'shopSelect',
      fieldProps: {
        placeholder: '请选择店铺',
      },
      formItemProps: {
        label: '店铺',
        name: 'shopId',
      },
    },
    {
      key: 'salesManId',
      type: 'staffSelect',
      fieldProps: {
        placeholder: '请选择业务员',
        fieldNames: { label: 'label', value: 'value' },
        request: iacUserSelectInputMore,
      },
      formItemProps: {
        label: '业务员',
        name: 'salesManId',
      },
    },
    {
      type: 'apiSelect',
      fieldProps: {
        // NOTE:v1.6补丁，由后端屏蔽印刷协同
        request: billQuerySaleBusinessTypes,
      },
      formItemProps: {
        label: '业务类型',
        name: 'businessType',
      },
    },
    {
      type: 'goodsTableSelect',
      key: 'skuIdList',
      fieldProps: {
        labelInValue: true,
      },
      formItemProps: { label: '包含商品', name: 'skuIdList' },
    },
    {
      type: 'select',
      formItemProps: {
        label: '单据状态',
        name: 'billStatus',
      },
      fieldProps: {
        options: enumToOptions(CommonBillStatusType),
      },
    },
    {
      type: 'select',
      formItemProps: {
        label: '单据来源',
        name: 'createTypeList',
      },
      fieldProps: {
        mode: 'multiple',
        options: hasPrintCoordination
          ? createTypeListOptions
          : createTypeListOptions?.filter(
              (item) => item.value !== BillSourceType.印刷生成,
            ),
      },
    },
    {
      type: 'rangePicker',
      formItemProps: {
        label: '还款到期时间',
        name: 'settlementTime',
      },
      fieldProps: {
        needMinDate: true,
        fieldNames: {
          start: 'settlementTimeStart',
          end: 'settlementTimeEnd',
        },
        startFormat: 'YYYY-MM-DD 00:00:00',
        endFormat: 'YYYY-MM-DD 23:59:59',
      },
    },

    {
      type: 'rangeInputNumber',
      formItemProps: {
        label: '剩余还款天数',
        name: 'settlementDaysRemaining',
      },
      fieldProps: {
        fieldNames: {
          start: 'startSettlementDaysRemaining',
          end: 'endSettlementDaysRemaining',
        },
        ...inputNumberProps.int,
      },
    },
  ];
};

export const summaryData = [
  {
    key: 'discountTotalAmount',
  },
  {
    key: 'receivedArrears',
    needDecimals: 2,
  },
  {
    key: 'freightAmount',
    needDecimals: 2,
  },
  {
    key: 'freightCostAmount',
    needDecimals: 2,
  },
  {
    key: 'discountAmount',
    needDecimals: 2,
  },
  {
    key: 'billCostAmount',
    needDecimals: 2,
  },
  {
    key: 'netProfitAmount',
    needDecimals: 2,
  },
];
