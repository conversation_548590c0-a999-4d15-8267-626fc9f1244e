diff --git a/es/Table.js b/es/Table.js
index f557e46cea863858e3d0193392e524e0e14d7156..ce003596c105c4e33d438824694adc2a4d4ae7e4 100644
--- a/es/Table.js
+++ b/es/Table.js
@@ -150,6 +150,11 @@ function Table(tableProps, ref) {
     mergedChildrenColumnName = _useExpand2[4],
     onTriggerExpand = _useExpand2[5];
 
+  var _React$useState7 = React.useState(0),
+    _React$useState8 = _slicedToArray(_React$useState7, 2),
+    scrollbarSize = _React$useState8[0],
+    setScrollbarSize = _React$useState8[1];
+
   // ====================== Column ======================
   var scrollX = scroll === null || scroll === void 0 ? void 0 : scroll.x;
   var _React$useState = React.useState(0),
@@ -167,7 +172,8 @@ function Table(tableProps, ref) {
       expandIconColumnIndex: expandableConfig.expandIconColumnIndex,
       direction: direction,
       scrollWidth: useInternalHooks && tailor && typeof scrollX === 'number' ? scrollX : null,
-      clientWidth: componentWidth
+      // 虚拟表格减去滚动条的宽度
+      clientWidth: componentWidth - (tailor && scrollbarSize ? scrollbarSize : 0)
     }), useInternalHooks ? transformColumns : null),
     _useColumns2 = _slicedToArray(_useColumns, 4),
     columns = _useColumns2[0],
@@ -402,10 +408,6 @@ function Table(tableProps, ref) {
   }, []);
 
   // ===================== Effects ======================
-  var _React$useState7 = React.useState(0),
-    _React$useState8 = _slicedToArray(_React$useState7, 2),
-    scrollbarSize = _React$useState8[0],
-    setScrollbarSize = _React$useState8[1];
   var _React$useState9 = React.useState(true),
     _React$useState10 = _slicedToArray(_React$useState9, 2),
     supportSticky = _React$useState10[0],
@@ -422,6 +424,13 @@ function Table(tableProps, ref) {
     setSupportSticky(isStyleSupport('position', 'sticky'));
   }, []);
 
+  React.useEffect(function () {
+    if (props.virtual) {
+      // 虚拟列表也使用滚动条，需要使用 scrollbar 的宽度
+      setScrollbarSize(getTargetScrollBarSize(fullTableRef.current).width);
+    }
+  }, [props.virtual]);
+
   // ================== INTERNAL HOOKS ==================
   React.useEffect(function () {
     if (useInternalHooks && internalRefs) {
@@ -517,7 +526,7 @@ function Table(tableProps, ref) {
       });
       headerProps.colWidths = flattenColumns.map(function (_ref6, index) {
         var width = _ref6.width;
-        var colWidth = index === flattenColumns.length - 1 ? width - scrollbarSize : width;
+        var colWidth = index === flattenColumns.length - 1 && !tailor && width - scrollbarSize > 0 ? width - scrollbarSize : width;
         if (typeof colWidth === 'number' && !Number.isNaN(colWidth)) {
           return colWidth;
         }
diff --git a/es/VirtualTable/BodyGrid.js b/es/VirtualTable/BodyGrid.js
index 23c68756dc12ac57c4558d03af95740ae952aacd..6148f8560da6fe9cc953b049abeeb11aabf241ca 100644
--- a/es/VirtualTable/BodyGrid.js
+++ b/es/VirtualTable/BodyGrid.js
@@ -10,7 +10,7 @@ import { GridContext, StaticContext } from "./context";
 var Grid = /*#__PURE__*/React.forwardRef(function (props, ref) {
   var data = props.data,
     onScroll = props.onScroll;
-  var _useContext = useContext(TableContext, ['flattenColumns', 'onColumnResize', 'getRowKey', 'prefixCls', 'expandedKeys', 'childrenColumnName', 'scrollX', 'direction']),
+  var _useContext = useContext(TableContext, ['flattenColumns', 'onColumnResize', 'getRowKey', 'prefixCls', 'expandedKeys', 'childrenColumnName', 'scrollX', 'direction', 'scrollbarSize']),
     flattenColumns = _useContext.flattenColumns,
     onColumnResize = _useContext.onColumnResize,
     getRowKey = _useContext.getRowKey,
@@ -18,7 +18,8 @@ var Grid = /*#__PURE__*/React.forwardRef(function (props, ref) {
     prefixCls = _useContext.prefixCls,
     childrenColumnName = _useContext.childrenColumnName,
     scrollX = _useContext.scrollX,
-    direction = _useContext.direction;
+    direction = _useContext.direction,
+    scrollbarSize = _useContext.scrollbarSize;
   var _useContext2 = useContext(StaticContext),
     sticky = _useContext2.sticky,
     scrollY = _useContext2.scrollY,
@@ -230,6 +231,7 @@ var Grid = /*#__PURE__*/React.forwardRef(function (props, ref) {
     component: wrapperComponent,
     scrollWidth: scrollX,
     direction: direction,
+    scrollbarSize: scrollbarSize,
     onVirtualScroll: function onVirtualScroll(_ref4) {
       var _listRef$current5;
       var x = _ref4.x;
diff --git a/es/hooks/useColumns/useWidthColumns.js b/es/hooks/useColumns/useWidthColumns.js
index 9bfe1b6bec0ed354cf338fce4576385b58e2c286..d0829056363e39adc2de4cb964699b9396c1a186 100644
--- a/es/hooks/useColumns/useWidthColumns.js
+++ b/es/hooks/useColumns/useWidthColumns.js
@@ -1,5 +1,6 @@
 import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
 import * as React from 'react';
+
 function parseColWidth(totalWidth) {
   var width = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';
   if (typeof width === 'number') {
@@ -63,6 +64,7 @@ export default function useWidthColumns(flattenColumns, scrollWidth, clientWidth
           restWidth -= colWidth;
         });
       }
+
       return [filledColumns, Math.max(realTotal, maxFitWidth)];
     }
     return [flattenColumns, scrollWidth];
