import { BizTable, ExportBusinessType } from '@slt/biz-ui';
import { inventoryQueryInfoList } from '@slt/services';
import { useGoodsDimension } from './useGoodsDimension';
import { ToolbarLeftTips } from '../toolbarLeftTips/ToolbarLeftTips';

/** 仓库查询-商品维度 */
const GoodsDimension = () => {
  const {
    form,
    tableRef,
    initialValues,
    summaryData,
    searchColumns,
    tableColumns,
    getExportParams,
    beforeSearchData,
  } = useGoodsDimension();

  return (
    <>
      <BizTable
        requestConfig={{
          api: inventoryQueryInfoList(),
          beforeData: {
            beforeSearchData,
            beforeLabelInValueKeys: ['goodsSpuIdList'],
          },
          extraParams: { ...initialValues },
        }}
        table={{
          rowSelection: false,
          tableRef,
          id: 'specificationDimensionQuery',
          rowKey: 'spuId',
          columns: tableColumns,
          wrapperClassName: 'px-sm',
        }}
        summaryData={summaryData}
        showRowIndex
        exportConfig={{
          businessType: ExportBusinessType.库存查询商品维度导出,
          authId: '2176',
          getParams: getExportParams,
        }}
        search={{
          form,
          searchColumns,
          initialValues: {
            separateWarehouse: false,
            enable: true,
          },
        }}
        toolbar={{
          left: <ToolbarLeftTips />,
        }}
      />
    </>
  );
};

export default GoodsDimension;
