/**
 * @description 订单详情Modal
 * @last-modifyer: 2025-1-24
 * */
import {
  Button,
  ConfigForm,
  Divider,
  Flex,
  Form,
  Modal,
  Partition,
  Tag,
  Typography,
  Spin,
  msgError,
  Condition,
  TypographyV2,
} from '@slt/base-ui';
import { useState, useCallback } from 'react';
import { OperationConfirm } from '@slt/biz-ui';
import { useStyles } from './OrderDetails.styles';
import {
  OrderStatusTextMap,
  OrderStatusTextMapColor,
  ReturnedStatus,
  OrderStatus,
} from './type';
import { useDetailSetting } from './useDetailSetting';
import {
  omsAfterSalesBillAudit,
  omsAfterSalesBillReverseAudit,
  omsAfterSalesCancel,
  omsAfterSalesReverseCancel,
  omsAfterSalesBillConfirmReceiveGoods,
  omsAfterSalesBillCancelReceiveGoods,
  type BatchOperationResultVO,
} from '@slt/services';
import { type YTRequest } from '@slt/net';
import { net } from '@slt/net';
import { AfterSaleOrderPermission } from '../permission';
import { OrderOperateLog } from '../components/orderOperateLog/OrderOperateLog';

const { Text } = Typography;
interface IProps {
  saleOrderId: string;
  text?: string;
  copyable?: boolean;
  isView?: boolean;
}

export const OrderDetails = (props: IProps) => {
  const {
    saleOrderId,
    text = '详情',
    copyable = false,
    isView = false,
  } = props;
  const saleOrderIds = saleOrderId?.split(',') || [];
  const texts = text?.split(',') || saleOrderIds;

  if (saleOrderIds.length > 1) {
    return (
      <Flex>
        {saleOrderIds.map((id, index) => (
          <Flex key={id}>
            <SingleOrderDetail
              saleOrderId={id.trim()}
              text={texts[index]?.trim() || id.trim()}
              copyable={copyable}
              isView={isView}
            />
            {index < saleOrderIds.length - 1 ? (
              <span className="text-text">；</span>
            ) : null}
          </Flex>
        ))}
      </Flex>
    );
  }

  return saleOrderIds.map((id, index) => (
    <>
      <SingleOrderDetail
        saleOrderId={id.trim()}
        text={texts[index]?.trim() || id.trim()}
        copyable={copyable}
        isView={isView}
      />
      {index < saleOrderIds.length - 1 ? (
        <span className="text-text">；</span>
      ) : null}
    </>
  ));
};

const SingleOrderDetail = (props: IProps) => {
  const {
    saleOrderId,
    text = '详情',
    copyable = false,
    isView = false,
  } = props;
  const [modalOpen, setModalOpen] = useState(false);
  const { styles } = useStyles();
  const state = 2;
  const {
    formValues,
    isUpdating,
    form,
    isEditAble,
    getDetailScreen,
    orderBaseInfoConfig,
    getMessageRemarksFlagConfig,
    AfterSaleAmountConfig,
    getDistributionInformationConfig,
    getOrderGoodsConfig,
  } = useDetailSetting({ modalOpen, saleOrderId, isView });
  const showModal = useCallback(
    (e: React.MouseEvent<HTMLAnchorElement>) => {
      e.stopPropagation();
      if (saleOrderId) {
        setModalOpen(true);
        void getDetailScreen();
      } else {
        msgError('请检查订单号是否存在');
      }
    },
    [getDetailScreen, saleOrderId],
  );

  const actionsRequest = useCallback(
    (
      title: string,
      request: (data: {
        afterSaleOrderIdList: string[];
      }) => YTRequest<BatchOperationResultVO>,
      authId: string,
    ) => {
      return (
        <OperationConfirm
          batchResult
          key={title}
          successMsg={`${title}成功`}
          reload={getDetailScreen}
          tableId="afterSaleBillDetailActions"
          getTableColumns={() => [
            {
              title: '订单号',
              key: 'key',
              dataIndex: 'key',
              width: '40%',
            },
            {
              title: '失败原因',
              key: 'failReason',
              dataIndex: 'failReason',
              width: '50%',
            },
          ]}
          modalProps={{
            title: '提示',
            content: `是否确认对该订单进行${title}`,
            type: 'info',
          }}
          onConfirm={async () => {
            const response = await net.fetch(
              request({ afterSaleOrderIdList: [formValues?.id] }),
            );
            return response;
          }}
        >
          {(onClick) => {
            return (
              <Button authId={authId} onClick={onClick} key={`${title}Action`}>
                {title}
              </Button>
            );
          }}
        </OperationConfirm>
      );
    },
    [getDetailScreen, formValues?.id],
  );
  return (
    <Flex
      onClick={(e: React.MouseEvent<HTMLAnchorElement>) => e.stopPropagation()}
      className="w-full"
    >
      <TypographyV2.Link copyable={copyable} onClick={showModal} title={text}>
        {text}
      </TypographyV2.Link>
      <Modal
        title="订单详情"
        type="edit"
        size="xl"
        titleBordered={false}
        footer={false}
        open={modalOpen}
        destroyOnClose
        onCancel={() => setModalOpen(false)}
      >
        <Spin spinning={isUpdating}>
          <Flex gap={8} justify="align-center">
            <Text className={styles.groupTitle}>售后单号: {saleOrderId}</Text>
            {formValues?.afterSaleStatus ? (
              <Tag
                bordered
                color={
                  OrderStatusTextMapColor[formValues?.afterSaleStatus || state]
                }
              >
                {OrderStatusTextMap[formValues?.afterSaleStatus]}
              </Tag>
            ) : null}
          </Flex>
          <Divider className="my-sm" />
          <Condition visible={!isView}>
            <Flex
              justify="flex-end"
              gap={8}
              hidden={isEditAble}
              className="mr-[8px]"
            >
              {formValues?.afterSaleStatus === OrderStatus.WaitAudited
                ? actionsRequest(
                    '审核',
                    omsAfterSalesBillAudit,
                    AfterSaleOrderPermission.审核,
                  )
                : null}
              {formValues?.afterSaleStatus === OrderStatus.HasAudited
                ? actionsRequest(
                    '反审核',
                    omsAfterSalesBillReverseAudit,
                    AfterSaleOrderPermission.反审核,
                  )
                : null}
              {formValues?.afterSaleStatus === OrderStatus.WaitAudited
                ? actionsRequest(
                    '取消',
                    omsAfterSalesCancel,
                    AfterSaleOrderPermission.取消,
                  )
                : null}
              {formValues?.afterSaleStatus === OrderStatus.Canceled
                ? actionsRequest(
                    '反取消',
                    omsAfterSalesReverseCancel,
                    AfterSaleOrderPermission.反取消,
                  )
                : null}
              {formValues?.returnStatus === ReturnedStatus.SellerNotGoods
                ? actionsRequest(
                    '确认收货',
                    omsAfterSalesBillConfirmReceiveGoods,
                    AfterSaleOrderPermission.确认收货,
                  )
                : null}
              {formValues?.returnStatus === ReturnedStatus.SellerAllReceived ||
              formValues?.returnStatus === ReturnedStatus.SellerPartReceived
                ? actionsRequest(
                    '取消收货',
                    omsAfterSalesBillCancelReceiveGoods,
                    AfterSaleOrderPermission.取消收货,
                  )
                : null}
            </Flex>
          </Condition>

          <Form
            enableUnsavedWarning={false}
            className={styles.formContainer}
            labelWidth={88}
            form={form}
          >
            <Partition title="订单基本信息">
              <ConfigForm list={orderBaseInfoConfig} isShowAll proportion={3} />
            </Partition>
            <Partition title="留言/备注/旗帜">
              <ConfigForm
                list={getMessageRemarksFlagConfig}
                isShowAll
                proportion={1}
              />
            </Partition>
            <Partition title="供分销信息">
              <ConfigForm
                list={getDistributionInformationConfig}
                isShowAll
                proportion={3}
              />
            </Partition>
            <Partition title="售后金额">
              <ConfigForm
                list={AfterSaleAmountConfig}
                isShowAll
                proportion={3}
              />
            </Partition>
            <Partition title="订单商品">
              <ConfigForm list={getOrderGoodsConfig} isShowAll proportion={3} />
            </Partition>
            <Partition title="订单日志">
              <OrderOperateLog
                id={saleOrderId}
                tableProps={{
                  scroll: { y: 200 },
                }}
                showReloadBtn
              />
            </Partition>
          </Form>
        </Spin>
      </Modal>
    </Flex>
  );
};
