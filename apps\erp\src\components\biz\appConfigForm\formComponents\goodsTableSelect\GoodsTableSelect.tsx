import { useCallback, useMemo, useRef, useState } from 'react';
import type { GoodsTableSelectProps } from './GoodsTableSelect.types';
import { useStyles } from './GoodsTableSelect.styles';
import {
  basicGoodsCategorySpuQueryTree,
  basicGoodsPageSkuList,
  basicGoodsPageSkuUrl,
  basicGoodsPageSpuList,
  basicGoodsPageSpuUrl,
  type BasicGoodsListQueryParams,
  type BasicGoodsPageSkuDTO,
  type CategorySpuQueryTreeDTO,
} from '@slt/services';
import {
  getModalTableColumns,
  getModalTableSearchColumns,
  getTableSelectColumns,
  goodsSelectModalTabsMap,
  queryParamsByTabsType,
  SelectionModalTabsType,
} from './data';
import { useTabRefresh } from '@/hooks/useTabRefresh';
import { useMutation } from '@slt/net-query';
import {
  SelectionModal,
  type BizTableRef,
  type SelectionModalRef,
} from '@slt/biz-ui';
import {
  ApiTableSelect,
  Checkbox,
  Flex,
  Form,
  PlusOutlined,
  Tabs,
  Typography,
  type TableColumnType,
  type TreeProps,
} from '@slt/base-ui';
import type { CommonRecord } from '@slt/base-utils';
import { GoodsCombineType, type CommonSelectValue } from '@slt/utils';
import { useOpenTab } from '@/hooks/useOpenTab';
import { routerMap } from '@/router/routerMap';
import { useAuthCheckMap } from '@/hooks/useAuthCheckMap';
import type { GoodsTableSelectFormValues } from './types';

/**
 * 商品下拉列表选择框，带有商品选择弹窗
 * @description 默认查询sku维度商品数据，可查询spu维度
 */
export const GoodsTableSelect = (props: GoodsTableSelectProps) => {
  const {
    showDisabled = false,
    onChange: onSelectedChange,
    fieldNames,
    extraQueryGoodsParams,
    isSpuDimension = false,
    multiple = true,
    labelInValue = true,
    acceptGoodsTabs: tabsKeys = [],
    ...restProps
  } = props;
  const { label = 'goodsName', value = 'id' } = fieldNames || {};
  const { styles, cx } = useStyles();
  const [form] = Form.useForm<GoodsTableSelectFormValues>();

  const [treeCategoryId, setTreeCategoryId] = useState<string>();
  const [treeData, setTreeData] = useState<CategorySpuQueryTreeDTO[]>([]);
  const [activeKey, setActiveKey] = useState<
    SelectionModalTabsType | undefined
  >(tabsKeys?.[0]);
  // 下来选择的值
  const [selectValues, setSelectValues] = useState<
    CommonRecord[] | CommonRecord
  >();
  const [enable, setEnable] = useState<boolean>(false);

  const modalRef = useRef<SelectionModalRef>(null);
  const modalTableRef = useRef<BizTableRef<BasicGoodsPageSkuDTO>>(null);

  const { openTab } = useOpenTab();
  const { authCheckMap } = useAuthCheckMap();

  const { mutate: queryGoodsCategoryTree, isMutating: isLoading } = useMutation(
    basicGoodsCategorySpuQueryTree,
  );

  useTabRefresh({
    refreshFunc: () => {
      modalTableRef.current?.reload();
    },
  });

  /** 跳转新增商品页 */
  const toGoodsAddPage = useCallback(() => {
    openTab(routerMap.goodsInfoAdd.route);
  }, [openTab]);

  /** 获取商品分类 */
  const handleQueryCategoryTree = useCallback(
    async (tabsType: SelectionModalTabsType) => {
      if (tabsType === SelectionModalTabsType['普通分销']) {
        return;
      }

      let goodsType: GoodsCombineType | '' = '';
      switch (tabsType) {
        case SelectionModalTabsType['普通自有']:
          goodsType = GoodsCombineType['普通'];
          break;
        case SelectionModalTabsType['组合']:
          goodsType = GoodsCombineType['组合'];
          break;
      }

      const res = await queryGoodsCategoryTree({
        goodsType,
        defaultCategory: true,
      });
      if (res.success && res.data) {
        setTreeData(res.data);
      }
    },
    [queryGoodsCategoryTree],
  );

  const handleClickShowModal = useCallback(() => {
    modalRef.current?.showModal();
    if (activeKey) {
      handleQueryCategoryTree(activeKey);
    }
  }, [activeKey, handleQueryCategoryTree]);

  /** 查询分销商品时不展示分类树 */
  const hideTree = useMemo(
    () => !activeKey || activeKey === SelectionModalTabsType['普通分销'],
    [activeKey],
  );

  /** 商品分类树props */
  const treeProps: TreeProps | undefined = useMemo(() => {
    if (hideTree) {
      return;
    }

    return {
      treeData,
      draggable: false,
      showSearch: true,
      treeLoading: isLoading,
      topContainerClassName: '!pt-xs',
      onClick: (_, node) => {
        setTreeCategoryId(`${node.key || -1}`);
      },
    };
  }, [hideTree, treeData, isLoading]);

  /** 是否展示顶部商品类型Tabs */
  const showTopSlot = useMemo(() => tabsKeys.length > 1, [tabsKeys.length]);

  /** 商品类型Tabs */
  const topSlot = useMemo(() => {
    if (!showTopSlot) {
      return null;
    }

    const tabItems = tabsKeys.map((key) => ({
      label: goodsSelectModalTabsMap[key],
      key: key,
    }));

    return (
      <Tabs
        items={tabItems}
        activeKey={activeKey}
        onChange={(key) => {
          form.resetFields();
          setActiveKey(key as SelectionModalTabsType);
          setTreeCategoryId(undefined);
          handleQueryCategoryTree(key as SelectionModalTabsType);
        }}
      />
    );
  }, [showTopSlot, tabsKeys, activeKey, form, handleQueryCategoryTree]);

  /**  */
  const crossPageTableWrapClassName = useMemo(() => {
    if (hideTree) {
      return '';
    } else {
      return showTopSlot ? 'h-[calc(100%-64px)]' : 'h-full';
    }
  }, [hideTree, showTopSlot]);

  /** 选择商品弹窗列表请求参数 */
  const selectionModalQueryParams: BasicGoodsListQueryParams = useMemo(() => {
    return {
      ...(activeKey ? queryParamsByTabsType[activeKey] : {}),
      ...extraQueryGoodsParams,
    };
  }, [activeKey, extraQueryGoodsParams]);

  /** tableSelect请求参数 */
  const tableSelectQueryParams = useMemo(() => {
    if (tabsKeys.length === 1) {
      return {
        ...queryParamsByTabsType[tabsKeys[0]],
        ...extraQueryGoodsParams,
        enable: enable === true ? undefined : 1,
      };
    }
    return {
      ...extraQueryGoodsParams,
      enable: enable === true ? undefined : 1,
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [enable]);

  /** 选择商品弹窗列表请求接口 */
  const selectionModalRequestUrl = useMemo(
    () => (isSpuDimension ? basicGoodsPageSpuUrl : basicGoodsPageSkuUrl),
    [isSpuDimension],
  );

  const selectionModalSearchColumns = useMemo(() => {
    return getModalTableSearchColumns({
      activeKey,
      isSpuDimension,
      showDisabled,
    });
  }, [activeKey, isSpuDimension, showDisabled]);

  const selectionModalColumns = useMemo(() => {
    const combineTab = activeKey === SelectionModalTabsType['组合'];
    const distributionTab = activeKey === SelectionModalTabsType['普通分销'];
    return getModalTableColumns({
      isSpuDimension,
      combineTab,
      distributionTab,
    });
  }, [isSpuDimension, activeKey]);

  const selectTableColumns = useMemo(() => {
    return getTableSelectColumns(
      isSpuDimension,
    ) as TableColumnType<CommonRecord>[];
  }, [isSpuDimension]);

  return (
    <ApiTableSelect
      {...restProps}
      onChange={(
        value?: CommonSelectValue,
        record?: CommonRecord[] | CommonRecord,
      ) => {
        setSelectValues(record);
        onSelectedChange?.(value, record);
      }}
      needLimitWidth={false}
      searchKey="commonQuery"
      mode={multiple ? 'multiple' : undefined}
      labelInValue={labelInValue}
      params={tableSelectQueryParams}
      request={isSpuDimension ? basicGoodsPageSpuList : basicGoodsPageSkuList}
      moreIconClick={handleClickShowModal}
      onDoubleClick={handleClickShowModal}
      moreContent={(displayFunction) => {
        return (
          <SelectionModal
            ref={modalRef}
            title="选择商品"
            slots={{ top: topSlot }}
            className={cx({ [styles.modal]: showTopSlot })}
            wrapperClassName={crossPageTableWrapClassName}
            tableId="1742548101119"
            bizTable={{
              search: {
                form,
                proportion: 2,
                style: { marginBottom: 0, padding: 0 },
                searchColumns: selectionModalSearchColumns,
                initialValues: showDisabled ? { enable: 1 } : {},
              },
              treeProps,
              requestConfig: {
                api: { url: selectionModalRequestUrl },
                extraParams: {
                  categoryId: treeCategoryId,
                  ...selectionModalQueryParams,
                },
                beforeData: {
                  beforeSearchData: (params) => {
                    const newParams = { ...params };
                    if (params?.goodsNo?.length) {
                      // 查询商品编码时，精确匹配
                      newParams.goodsNoVagueType = 0;
                    }
                    if (params?.isbns?.length) {
                      // 查询ISBN时，精确匹配
                      newParams.isbnsVagueType = 0;
                    }

                    return newParams;
                  },
                },
              },
              table: {
                // NOTE:让不同tab的列配置不共享
                id: '1742548101119' + (activeKey ?? ''),
                rowKey: 'id',
                tableRef: modalTableRef,
                columns: selectionModalColumns,
                rowSelection: { type: multiple ? 'checkbox' : 'radio' },
                columnsSetting: true,
              },
              toolbar: {
                left: authCheckMap.goodsInfo.add ? (
                  <Typography.Link onClick={toGoodsAddPage}>
                    <PlusOutlined className="mr-xxs" />
                    新增
                  </Typography.Link>
                ) : null,
              },
            }}
            echoData={() => {
              if (!selectValues) {
                return [];
              }
              if (Array.isArray(selectValues)) {
                return selectValues;
              } else {
                return [selectValues];
              }
            }}
            panel={{ title: '已选商品', nameKey: label }}
            onOk={(value) => {
              displayFunction(value);
              form.resetFields();
            }}
            onCancel={() => {
              setTreeCategoryId(undefined);
              form.resetFields();
            }}
          />
        );
      }}
      selectTableProps={{
        id: '1756523387071',
        selectField: label,
        selectValueField: value,
        columns: selectTableColumns,
      }}
      extraNode={
        <Flex
          className="relative h-full w-full"
          align="center"
          justify="center"
        >
          {showDisabled ? (
            <Flex className="absolute left-xxs">
              <Checkbox
                value={enable}
                onChange={(e) => setEnable(e.target.checked)}
              >
                显示停用
              </Checkbox>
            </Flex>
          ) : null}
          {authCheckMap.goodsInfo.add ? (
            <Typography.Link onClick={toGoodsAddPage}>新增商品</Typography.Link>
          ) : null}
        </Flex>
      }
    />
  );
};
