import {
  ConfigForm,
  Flex,
  Form,
  Input,
  InputNumber,
  inputNumberProps,
  msgError,
  msgSuccess,
  PlusOutlined,
  PopoverFormItem,
  SysTooltip,
  Select,
  Table,
  Typography,
  type TableColumnType,
} from '@slt/base-ui';
import { BatchModify } from '../../BatchModify';
import {
  basicPlatformRolloutGoodsDataSkuExtra,
  type BasicPlatformRolloutGoodsDataSkuExtraDTO,
  type TaobaoSchemaDTO,
} from '@slt/services';
import { SpecificationInput } from './SpecificationInput';
import {
  useCallback,
  useEffect,
  useMemo,
  useState,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { FixedPriceInput } from './FixedPriceInput';
import { SpecificationSelect } from './SpecificationSelect';
import {
  generateSalePropAndSkuList,
  getRandomValue,
  isExistInSku,
} from './utils';
import { v4 as uuidv4 } from 'uuid';
import type { CommonRecord } from '@slt/base-utils';
import { SpecificationSort } from '../SpecificationSort';
import { isNil } from 'lodash-es';
import { AddPlatformInfoSkuModal } from '@/components/biz';
import { echoUploadImg, PlatformTypeEnum } from '@slt/utils';
import { usePlatformInfoContext } from '../../platformInfoContext';
import { cartesian, getDefaultValueBySku } from '../utils';

import { useRequest } from 'ahooks';
import { net } from '@slt/net';
import { useGoodsStoreInfoContext } from '../../../context';

import { SelectSales } from '../SelectSales';
import { useSalesInfoColumns } from './useSalesInfoColumns';
import { TBaoTMaoSalesAttributeDescModal } from './TBaoTMaoSalesAttributeDescModal';

interface Props {
  skuConfig?: TaobaoSchemaDTO[];
  salePropConfig?: TaobaoSchemaDTO[];
  noSkuData?: any;
  categoryList?: any[];
  runAsyncSchema?: any[];
}

export const SalesInfo = forwardRef((props: Props, ref) => {
  const { skuConfig, salePropConfig, noSkuData } = props;

  const { isNoSku, isMultiplePlatformSku } = noSkuData;

  const { data: platformInfoData } = usePlatformInfoContext();
  const { platformInfo } = useGoodsStoreInfoContext();

  const originalSkuIds = useMemo(() => {
    return platformInfo?.distributionSku?.map((item: any) => item.id) ?? [];
  }, [platformInfo]);

  const form = Form.useFormInstance();
  const sku = Form.useWatch('sku', form);
  const saleProp = Form.useWatch('saleProp', form);

  //销售属性
  const saleAttribute = Form.useWatch('saleAttribute', form);

  /**当前为多个销售规格属性的场景,类目有多个销售属性
   * 场景1: 只勾选了一个不支持传规格图且销售规格值不可自定义维护的销售属性;
   * 场景2: 只勾选了一个不支持传规格图且销售规格值可自定义维护的销售属性 或 只勾选了一个支持传规格图的销售属性
   * 场景3: 选择2个销售属性
   * */
  const [saleAttributeScenario, setSaleAttributeScenario] = useState<
    'scenario1' | 'scenario2' | 'scenario3' | null
  >(null);

  useImperativeHandle(ref, () => ({
    setSaleAttributeScenario: (scenario) => {
      setSaleAttributeScenario(scenario);
    },
  }));

  const skuQualityTip = useMemo(() => {
    return skuConfig
      ?.find((item) => item.id === 'skuQuality')
      ?.rules?.find((rule) => rule.name === 'tipRule')?.value;
  }, [skuConfig]);

  const skuBarcodeTips = useMemo(() => {
    return skuConfig
      ?.find((item) => item.id === 'skuBarcode')
      ?.rules?.filter((rule) => rule.name === 'tipRule')
      .map((item) => item.value);
  }, [skuConfig]);

  // 是否是多规格
  const isMultiSaleProp = (salePropConfig ?? []).length > 1;

  //带入平台资料所有规格
  const setAllPlatformSkuList = useCallback(
    async (newSalePropId = undefined) => {
      const { distributionSku = [] } = platformInfo ?? {};
      const { salePropList, skuList, outerId, minPrice } =
        generateSalePropAndSkuList(
          distributionSku,
          newSalePropId
            ? salePropConfig?.filter((val) => val.id === newSalePropId)
            : salePropConfig,
          platformInfoData,
        );
      form.setFieldsValue({
        saleProp: {
          [newSalePropId ?? salePropConfig[0].id]: salePropList,
        },
        sku: skuList,
        outerId,
        price: minPrice,
      });
    },
    [form, platformInfo, platformInfoData, salePropConfig],
  );

  useEffect(() => {
    // 勾选单个规格时不自动处理销售规格
    if ((saleAttribute ?? []).length <= 1) {
      return;
    }

    const sku = form.getFieldValue('sku') ?? [];

    const reversedSalePropConfig = salePropConfig?.toReversed();

    const list =
      reversedSalePropConfig?.map(
        (item) =>
          saleProp?.[item.id]
            ?.filter((item: any) => item.value)
            .map((item: any) => item.id) ?? [],
      ) ?? [];

    const cartesianList = cartesian(list);
    const fields = reversedSalePropConfig?.map((item) => item.id) ?? [];

    const hasSameContent =
      cartesianList.length === sku.length &&
      cartesianList.every((item) => isExistInSku(sku, fields, item));

    const hasSameOrder = (sku as any[]).every((item, index) => {
      const targetCartesian = cartesianList[index] || [];

      return isExistInSku([item], fields, targetCartesian);
    });

    if (cartesianList.length && (!hasSameContent || !hasSameOrder)) {
      const newSku = cartesianList.map((ids) => {
        if (isExistInSku(sku, fields, ids)) {
          return sku.find((item: any) => isExistInSku([item], fields, ids));
        }

        return {
          props: ids
            .filter((id) => id)
            .reduce((acc, id, index) => {
              acc[fields[index]] = id;
              return acc;
            }, {} as CommonRecord),
        };
      });

      form.setFieldValue('sku', newSku);
    }
  }, [form, salePropConfig, saleProp, saleAttribute]);

  // 计算总库存
  const quantity = useMemo(() => {
    return sku?.reduce(
      (acc: number, item: any) =>
        acc + (item.skuStock ? Number(item.skuStock) : 0),
      0,
    );
  }, [sku]);

  useEffect(() => {
    form.setFieldValue('quantity', quantity);
  }, [form, quantity]);

  // sku 分类 options
  const skuCategoryOptions = useMemo(() => {
    return skuConfig
      ?.find((item) => item.id === 'skuQuality')
      ?.options?.map((item) => ({
        label: item.displayName,
        value: item.value,
      }));
  }, [skuConfig]);

  const [skuExtraList, setSkuExtraList] = useState<
    BasicPlatformRolloutGoodsDataSkuExtraDTO[]
  >([]);

  const { runAsync: getSkuExtra } = useRequest(
    (ids: string[]) =>
      net.fetch(
        basicPlatformRolloutGoodsDataSkuExtra({
          platformType: PlatformTypeEnum.淘宝,
          platformDistributionSkuIds: ids,
          /**暂时注释 */
          // platformDistributionSpuId: platformInfo?.id,
        }),
      ),
    {
      manual: true,
    },
  );

  useEffect(() => {
    if (sku?.length) {
      const skuIds = sku
        .map((item: any) => item.skuId)
        ?.filter(
          (skuId?: string) =>
            skuId &&
            !skuExtraList.some(
              (extra) => extra.platformDistributionSkuId === skuId,
            ),
        );

      if (skuIds.length) {
        getSkuExtra(skuIds).then((res) => {
          setSkuExtraList([
            ...skuExtraList,
            ...(res.data?.skuExtraInfoList ?? []),
          ]);
        });
      }

      //当平台铺货资料有多个规格时,商家编码默认取规格编码
      if (
        !saleAttribute?.length &&
        isMultiplePlatformSku &&
        skuExtraList.length
      ) {
        form.setFieldsValue({
          outerId: skuExtraList.find(
            (item) => item.platformDistributionSkuId === sku[0]?.skuId,
          )?.platformDistributionSkuCode,
        });
      }
    }
  }, [getSkuExtra, sku]);

  const isExistControlPrice = useMemo(() => {
    return skuExtraList
      .filter((item) =>
        sku?.some((sku: any) => sku.skuId === item.platformDistributionSkuId),
      )
      .some((item) => item.priceControls?.length);
  }, [sku, skuExtraList]);

  // 单规格：添加商品规格
  const handleAddGoodsSaleProp = (id: string, data: any[]) => {
    const addSaleProp: any[] = [];
    const addSku: any[] = [];

    data.forEach((item: any) => {
      const randomValue = getRandomValue();
      const valueId = uuidv4();

      const { stock, price } = getDefaultValueBySku(item, platformInfoData);

      const { skuId, skuName, mainImageUrl } = item;

      addSaleProp.push({
        id: valueId,
        value: randomValue,
        label: skuName,
        img: mainImageUrl ? [echoUploadImg(mainImageUrl)] : undefined,
      });

      addSku.push({
        props: {
          [id]: valueId,
        },
        skuId,
        skuName,
        skuStock: stock,
        skuPrice: price,
      });
    });

    form.setFieldValue(
      ['saleProp', id],
      [...(saleProp?.[id] ?? []), ...addSaleProp],
    );

    form.setFieldValue('sku', [...(sku ?? []), ...addSku]);
  };

  // 多规格：添加规格值，无需选择 sku
  const handleAddSalePropValue = (id: string) => {
    const valueId = uuidv4();

    form.setFieldValue(['saleProp', id, saleProp?.[id]?.length ?? 0], {
      id: valueId,
    });

    //只勾选了一个不支持传规格图且销售规格值不可自定义维护的销售属性的场景
    if (saleAttributeScenario === 'scenario1') {
      const currentSku = form.getFieldValue('sku') || [];

      form.setFieldValue('sku', [
        ...currentSku,
        ...[
          {
            props: {
              [id]: valueId,
            },
          },
        ],
      ]);
    }
  };

  // 删除规格值
  const handleDeleteSaleProp = (id: string, valueId: string) => {
    if (isMultiSaleProp && saleProp?.[id]?.length === 1) {
      msgError('平台规格下至少添加一个规格值');
      return;
    }

    if (!isMultiSaleProp) {
      // 单规格：至少保留1个当前商品规格
      const isExistOriginalSku = sku
        ?.filter((item: any) => item.props?.[id] !== valueId)
        .some((item: any) => originalSkuIds.includes(item.skuId));

      if (!isExistOriginalSku) {
        msgError('至少保留1个当前商品规格');
        return;
      }
    }

    form.setFieldValue(
      ['saleProp', id],
      saleProp?.[id]?.filter((item: any) => item.id !== valueId),
    );

    form.setFieldValue(
      ['sku'],
      sku?.filter((item: any) => item.props?.[id] !== valueId),
    );

    msgSuccess('删除成功');
  };

  // 排序规格值
  const handleSortSaleProp = (id: string, data: any[]) => {
    form.setFieldValue(['saleProp', id], data);

    if (!isMultiSaleProp) {
      const skuList: any = [];

      data.forEach((item) => {
        const targetSku = sku?.find(
          (sku: any) =>
            salePropConfig?.[0]?.id &&
            sku.props?.[salePropConfig?.[0]?.id] === item.id,
        );

        if (targetSku) {
          skuList.push(targetSku);
        }
      });

      form.setFieldValue('sku', skuList);
    } else {
      //是多个销售规格，但只勾选了一个规格
      if (saleAttribute?.length === 1) {
        const dataOrderMap = new Map();
        data.forEach((item, index) => {
          dataOrderMap.set(item.id, index);
        });
        const targetSku = sku.sort((a, b) => {
          const skuDataIdA = a.props?.[id];
          const skuDataIdB = b.props?.[id];

          const orderA = dataOrderMap.get(skuDataIdA);
          const orderB = dataOrderMap.get(skuDataIdB);

          return orderA - orderB;
        });

        form.setFieldValue('sku', targetSku);
      }
    }
  };

  const handleBatchModify = (key: string, val: number) => {
    form.setFieldsValue({
      sku: sku?.map((item: any) => ({ ...item, [key]: val })),
    });
  };

  const skuPriceList = useMemo(() => {
    return (
      sku
        ?.filter((item: any) => !isNil(item.skuPrice))
        .map((item: any) => Number(item.skuPrice)) ?? []
    );
  }, [sku]);

  useEffect(() => {
    if (skuPriceList.length) {
      const minPrice = Math.min(...skuPriceList);
      form.setFieldValue('price', minPrice);
    }
  }, [form, skuPriceList]);

  const { noSkuColumns, columns } = useSalesInfoColumns({
    form,
    sku,
    saleProp,
    salePropConfig,
    saleAttribute,
    saleAttributeScenario,
    skuExtraList,
    isMultiplePlatformSku,
    skuBarcodeTips,
    skuCategoryOptions,
    skuQualityTip,
    isExistControlPrice,
    handleBatchModify,
    platformInfoData,
  });

  const handleSaleAttributeOnChange = useCallback(
    (currentCheckedValues: string[]) => {
      // 当销售规格全部取消勾选时
      if (!currentCheckedValues?.length) {
        setSaleAttributeScenario(null);

        if (!isMultiplePlatformSku) {
          //平台铺货资料为单规格时
          setAllPlatformSkuList();
        } else {
          form.setFieldsValue({
            sku: [{}],
            price: undefined,
          });
        }
        return;
      }

      if (!isMultiSaleProp) {
        setAllPlatformSkuList();
        return;
      }

      /**类目有多个销售属性
       * 场景1: 只勾选了一个不支持传规格图且销售规格值不可自定义维护的销售属性;
       * 场景2: 只勾选了一个不支持传规格图且销售规格值可自定义维护的销售属性 或 只勾选了一个支持传规格图的销售属性
       * 场景3: 选择2个销售属性
       * */
      if (isMultiSaleProp) {
        // 获取当前所有带img属性的销售属性
        const exitImgs = salePropConfig?.filter((item) =>
          item.rules?.some(
            (rule) =>
              rule.name === 'valueAttributeRule' && rule.value === 'img',
          ),
        );

        if (currentCheckedValues.length === 1) {
          // 只勾选了一个属性
          const singleItem = salePropConfig?.find(
            (item) => item.id === currentCheckedValues[0],
          );
          const isSingleImg = exitImgs?.some(
            (imgItem) => imgItem.id === currentCheckedValues[0],
          );

          if (singleItem) {
            const singleOptions =
              singleItem?.options
                ?.map((opt) => ({
                  label: opt.displayName,
                  value: opt.value,
                }))
                .filter((opt) => opt.value !== '-1') ?? [];

            const isSingleCustomizable = singleOptions.length === 0;

            if (!isSingleImg && !isSingleCustomizable) {
              // 场景1: 只勾选了一个不支持传规格图且销售规格值不可自定义维护的销售属性
              setSaleAttributeScenario('scenario1');

              const uuid = uuidv4();

              form.setFieldsValue({
                saleProp: {
                  [currentCheckedValues[0]]: [
                    {
                      id: uuid,
                    },
                  ],
                },
                sku: [
                  {
                    props: {
                      [currentCheckedValues[0]]: uuid,
                    },
                  },
                ],
              });
            } else if ((!isSingleImg && isSingleCustomizable) || isSingleImg) {
              // 场景2: 只勾选了一个不支持传规格图且销售规格值可自定义维护的销售属性 或 只勾选了一个支持传规格图的销售属性
              setAllPlatformSkuList(currentCheckedValues[0]);
              setSaleAttributeScenario('scenario2');
            }
          }
        } else if (currentCheckedValues.length === 2) {
          // 场景3: 选择2个销售属性，清除所有规格信息
          setSaleAttributeScenario('scenario3');

          const uuid = uuidv4();
          const initialObj = {};
          currentCheckedValues.forEach((value) => {
            initialObj[value] = [
              {
                id: uuid,
              },
            ];
          });

          form.setFieldsValue({
            saleProp: initialObj,
            sku: [
              {
                props: {
                  [currentCheckedValues[0]]: uuid,
                },
              },
            ],
          });
        }
      }
    },
    [
      form,
      isMultiSaleProp,
      isMultiplePlatformSku,
      salePropConfig,
      setAllPlatformSkuList,
    ],
  );

  const checkBoxOptions = useMemo(() => {
    return (
      salePropConfig?.map((item) => ({
        label: `${item.name}${
          isMultiSaleProp &&
          item.rules?.some(
            (field) =>
              field.name === 'valueAttributeRule' && field.value === 'img',
          )
            ? '（支持上传规格图）'
            : ''
        }`,
        value: item.id,
      })) ?? []
    );
  }, [salePropConfig, isMultiSaleProp]);

  return (
    <Flex vertical gap={12} className="mx-lg">
      <Flex vertical gap={8}>
        <Flex justify="space-between">
          {isNoSku ? (
            <Flex gap={4} align="center">
              <Typography.Text className="text-base">销售属性</Typography.Text>
              <Typography.Text className="text-description">
                平台在当前类目下不支持添加销售属性，仅支持发布单品，如果平台资料存在多个规格，请选择其中1个规格进行维护
              </Typography.Text>
            </Flex>
          ) : (
            <Form.Item
              labelCol={{ flex: '60px' }}
              style={{ marginBottom: '0' }}
              label={
                <Typography.Text className="text-base">
                  销售属性
                </Typography.Text>
              }
              rules={[{ required: false }]}
              name="saleAttribute"
            >
              <SelectSales
                options={checkBoxOptions}
                onResetSku={handleSaleAttributeOnChange}
              />
            </Form.Item>
          )}

          <TBaoTMaoSalesAttributeDescModal />
        </Flex>

        <Flex vertical gap={8}>
          {(saleAttribute?.length
            ? salePropConfig?.filter((config) =>
                saleAttribute.includes(config.id),
              )
            : []
          )?.map((item) => {
            // 单规格必须图片 || 多规格根据 rules 判断
            const showImage =
              salePropConfig?.length === 1 ||
              item.rules?.some(
                (field) =>
                  field.name === 'valueAttributeRule' && field.value === 'img',
              );

            const options =
              item.options
                ?.map((item) => ({
                  label: item.displayName,
                  value: item.value,
                }))
                .filter((item) => item.value !== '-1') ?? [];

            const isInput = options.length === 0;
            const canInput =
              !isInput &&
              item.rules?.some((item) => item.name === 'maxLengthRule');

            return (
              <Flex
                vertical
                className="w-full bg-fill-quaternary p-sm"
                key={item.id}
              >
                <Flex justify="space-between">
                  <Form.Item
                    labelCol={{ flex: '50px' }}
                    label={
                      <Typography.Text className="text-base font-strong">
                        规格名
                      </Typography.Text>
                    }
                  >
                    <Input disabled value={item.name} />
                  </Form.Item>
                  <SpecificationSort
                    data={saleProp?.[item.id] ?? []}
                    onSuccess={(data) => handleSortSaleProp(item.id, data)}
                  />
                </Flex>
                <Flex vertical gap={16}>
                  {showImage ? (
                    <Typography.Text className="text-description">
                      规格图要求：宽高不超过5000px，格式JPG/JPEG/PNG，大小3M以内
                    </Typography.Text>
                  ) : null}
                  <Flex vertical>
                    <Form.List
                      name={['saleProp', item.id]}
                      initialValue={[{ id: uuidv4() }]}
                    >
                      {(fields) => {
                        return (
                          <Flex className="gap-x-base" wrap="wrap">
                            {fields.map((field, index) => (
                              <Flex key={field.key}>
                                {isInput ? (
                                  <SpecificationInput
                                    showImage={showImage}
                                    name={['saleProp', item.id]}
                                    index={field.name}
                                    onDelete={(valueId) =>
                                      handleDeleteSaleProp(item.id, valueId)
                                    }
                                  />
                                ) : (
                                  <SpecificationSelect
                                    showImage={showImage}
                                    name={['saleProp', item.id]}
                                    index={field.name}
                                    options={options}
                                    canInput={canInput}
                                    onDelete={(valueId) =>
                                      handleDeleteSaleProp(item.id, valueId)
                                    }
                                  />
                                )}
                              </Flex>
                            ))}
                          </Flex>
                        );
                      }}
                    </Form.List>
                    <Flex>
                      {isMultiSaleProp &&
                      saleAttributeScenario !== 'scenario2' ? (
                        <Typography.Link
                          className="flex items-center gap-xxs"
                          onClick={() => handleAddSalePropValue(item.id)}
                        >
                          <PlusOutlined />
                          新增规格值
                        </Typography.Link>
                      ) : (
                        <AddPlatformInfoSkuModal
                          title="添加商品规格"
                          tableId="1738995347762"
                          selectionType="checkbox"
                          disabledItems={sku?.map((item: any) => item.skuId)}
                          trigger={(onClick) => (
                            <Typography.Link
                              className="flex items-center gap-xxs"
                              onClick={onClick}
                            >
                              <PlusOutlined />
                              添加商品规格
                            </Typography.Link>
                          )}
                          onOk={(data: any[]) =>
                            handleAddGoodsSaleProp(item.id, data)
                          }
                        />
                      )}
                    </Flex>
                  </Flex>
                </Flex>
              </Flex>
            );
          })}
        </Flex>
      </Flex>
      <Flex vertical gap={8}>
        <Flex gap={4}>
          <Typography.Text>销售规格</Typography.Text>
          <Typography.Text className="text-description">
            设置选择的规格价格等信息
          </Typography.Text>
        </Flex>
        <Form.List name="sku">
          {(fields) =>
            fields.length ? (
              <Table
                id="1736590641353"
                rowKey="skuId"
                resizableHeader={false}
                columnsSetting={false}
                pagination={false}
                dataSource={fields}
                columns={isNoSku ? noSkuColumns : columns}
              />
            ) : null
          }
        </Form.List>
        <Flex className="mt-xs w-full">
          <ConfigForm
            className="w-full"
            proportion={1}
            list={[
              {
                formItemProps: {
                  label: '一口价',
                  name: 'price',
                  required: true,
                  rules: [
                    () => ({
                      validator(_, value) {
                        if (isNil(value)) {
                          return Promise.reject(new Error('一口价必填'));
                        }

                        const isExistSku = sku?.some(
                          (item: any) => Number(item.skuPrice) === value,
                        );

                        if (!isExistSku) {
                          return Promise.reject(
                            new Error(
                              '商品"一口价"必须与"商品销售规格"列表里有库存的sku其中之一保持一致',
                            ),
                          );
                        }

                        return Promise.resolve();
                      },
                    }),
                  ],
                  wrapperCol: { span: 12 },
                },
                renderFormItem: () => {
                  return <FixedPriceInput />;
                },
              },
              {
                type: 'input',
                formItemProps: {
                  label: '总库存',
                  name: 'quantity',
                  required: true,
                  wrapperCol: { span: 12 },
                },
                fieldProps: {
                  disabled: true,
                },
              },
              {
                type: 'input',
                formItemProps: {
                  label: '商家编码',
                  name: 'outerId',
                  wrapperCol: { span: 12 },
                },
                fieldProps: {
                  showCount: true,
                  maxLength: 64,
                  disabled: !saleAttribute?.length,
                },
              },
              {
                type: 'specialCountsInput',
                formItemProps: {
                  label: '商品条形码',
                  name: 'barcode',
                  wrapperCol: { span: 12 },
                },
                fieldProps: {
                  showCount: true,
                  maxLength: 32,
                },
              },
            ]}
          />
        </Flex>
      </Flex>
    </Flex>
  );
});
