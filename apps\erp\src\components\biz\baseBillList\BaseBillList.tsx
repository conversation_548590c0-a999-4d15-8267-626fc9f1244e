import { useTabRefresh } from '@/hooks/useTabRefresh';
import {
  Button,
  CustomTabs,
  Flex,
  Form,
  Space,
  type TableColumnType,
  type TabsProps,
} from '@slt/base-ui';
import { formatDateAndTime, type CommonRecord } from '@slt/base-utils';
import {
  BatchDropdown,
  BizTable,
  ExportBusinessType,
  getOperateLogTabItem,
  type BizTableRef,
  type QueryFormListItem,
} from '@slt/biz-ui';
import { useMutation } from '@slt/net-query';
import {
  financeExpenseAndOtherIncomeBillBatchDel,
  financeExpenseAndOtherIncomeBillList,
  financeExpenseAndOtherIncomeBillStatisticsAmount,
  financeReceiveAndPaymentBillBatchDelete,
  financeReceiveAndPaymentBillQueryOrderList,
  financeReceiveAndPaymentBillStatisticsAmount,
  stockExpenseAndOtherIncomeBillList,
  stockReceiveAndPaymentBillQueryTaskProgressUrl,
  type FinanceAdvanceFinanceBillListParams,
  type FinanceBillDTO,
} from '@slt/services';
import {
  BillTypeEnum,
  getDateTimeRangesMap,
  SystemLogType,
  type RawValue,
} from '@slt/utils';
import { useCallback, useMemo, useState, type RefObject } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useStyles } from './BaseBillList.styles';
import { BillDetail } from './components/billDetail/BillDetail';
import { BusinessBillList } from './components/businessBillList/BusinessBillList';
import { useColumns, type BaseBillType } from './hooks/useColumns';
import clsx from 'clsx';
import { useActions, useTabSearch } from '@/store/system';
import ModifyExpirationTimeModal from '../modifyExpirationTimeModal/ModifyExpirationTimeModal';
import { GlobalPollingTaskEnum } from '@/store/type';

type Props = {
  tableRef: RefObject<BizTableRef<FinanceBillDTO>>;
  type: BaseBillType;
  operateModule: string;
  tableColumns: TableColumnType<FinanceBillDTO>[];
  searchColumns: QueryFormListItem[];
  authIdObj?: Record<string, string>;
  toDetailPage: (id: string) => void;
  toAddPage: () => void;
  /** 跳转按单结算页 */
  toSettlePage?: (id: string) => void;
};

enum TabKeyType {
  '明细' = '1',
  '结算单据' = '2',
}

type FormBaseBillListType = FinanceAdvanceFinanceBillListParams;

export const BaseBillList = (props: Props) => {
  const {
    tableRef,
    type,
    operateModule,
    authIdObj,
    tableColumns,
    searchColumns,
    toDetailPage,
    toAddPage,
    toSettlePage,
  } = props;

  const { styles } = useStyles();
  const [form] = Form.useForm<FormBaseBillListType>();
  const { startPolling } = useActions();

  const { tabSearch } = useTabSearch<{
    billDateTime: string[];
    billNoList: string[];
    settleBillNo: string;
    businessBillNoList?: string[];
  }>();

  const {
    billNoList: tabSearchBillNoList,
    settleBillNo: tabSearchSettleBillNo,
  } = tabSearch || {};

  const [searchParams] = useSearchParams();
  const searchBillNo = searchParams.get('billNo');

  const [tabActiveKey, setTabActiveKey] = useState<string>(
    tabSearchSettleBillNo ? TabKeyType['结算单据'] : TabKeyType['明细'],
  );

  const isBatchQueryBillNo = useMemo(
    () => [BillTypeEnum.付款单, BillTypeEnum.收款单].includes(type),
    [type],
  );
  const initialBillNoList: string[] | undefined = useMemo(() => {
    if (tabSearchBillNoList?.length) {
      return tabSearchBillNoList;
    } else if (isBatchQueryBillNo && searchBillNo) {
      // 付款单、收款单单据编号改为批量查询
      return [searchBillNo];
    }
  }, [tabSearchBillNoList, searchBillNo, isBatchQueryBillNo]);

  const initialValues = useMemo(() => {
    // NOTE：在外面解构会导致参数一样的tabSearch跳转无响应
    const { billDateTime: tabSearchBillDateTime, businessBillNoList } =
      tabSearch || {};
    const [tabSearchStartTime, tabSearchEndTime] = tabSearchBillDateTime || [];
    const thisWeek = getDateTimeRangesMap().thisWeek;

    const values: CommonRecord = {
      billDateTime: tabSearchBillDateTime ?? thisWeek,
      billDateTimeStart: tabSearchBillDateTime
        ? tabSearchStartTime
        : formatDateAndTime(thisWeek[0]),
      billDateTimeEnd: tabSearchBillDateTime
        ? tabSearchEndTime
        : formatDateAndTime(thisWeek[1]),
    };
    if (!isBatchQueryBillNo && searchBillNo) {
      values.labelValue = searchBillNo;
    }
    if (isBatchQueryBillNo && initialBillNoList?.length) {
      values.billNoList = initialBillNoList;
    }
    if (businessBillNoList?.length) {
      values.businessBillNoList = businessBillNoList;
    }
    form.setFieldsValue(values);
    // NOTE:tabSearch改变重新请求
    tableRef?.current?.reload();
    return values;
  }, [searchBillNo, isBatchQueryBillNo, form, tabSearch, tableRef]);

  const [selectedRows, setSelectedRows] = useState<FinanceBillDTO[]>([]);
  const [clickedRow, setClickedRow] = useState<FinanceBillDTO>();

  const onDeleteSuccess = useCallback(
    (data: { taskId?: string } | boolean) => {
      if (typeof data === 'object') {
        const { taskId } = data;
        if (taskId) {
          // 说明是异步逻辑
          startPolling({
            request: {
              url:
                stockReceiveAndPaymentBillQueryTaskProgressUrl + `/${taskId}`,
              method: 'get',
            },
            taskId,
            taskType: GlobalPollingTaskEnum.RECEIVEANDPAY,
            interval: 2000,
            successCondition: [
              {
                field: 'data.taskStatus',
                value: 2,
              },
              {
                field: 'data.taskStatus',
                value: 3,
              },
            ],
            msgConfig: {
              operateType: 'delete',
            },
          });
        }
      }

      setSelectedRows([]);
    },
    [startPolling],
  );

  const { columns } = useColumns({
    tableRef,
    type,
    authIdObj,
    tableColumns,
    onDeleteSuccess,
    toDetailPage,
    toSettlePage,
  });

  const billStatisticsAmountRequest = useMemo(() => {
    switch (type) {
      case BillTypeEnum['付款单']:
      case BillTypeEnum['收款单']:
        return financeReceiveAndPaymentBillStatisticsAmount;
      case BillTypeEnum['费用单']:
      case BillTypeEnum['其他收入单']:
        return financeExpenseAndOtherIncomeBillStatisticsAmount;
      default:
        return null;
    }
  }, [type]);

  const { mutate: getBillStatisticsAmount, data: billStatisticsAmount } =
    useMutation(billStatisticsAmountRequest);

  const { mutate: receivedAndPaymentBatchDel } = useMutation(
    financeReceiveAndPaymentBillBatchDelete,
  );

  const { mutate: expenseAndOtherIncomeBatchDel } = useMutation(
    financeExpenseAndOtherIncomeBillBatchDel,
  );

  const mutateBatchDelete = useMemo(() => {
    switch (type) {
      case BillTypeEnum['付款单']:
      case BillTypeEnum['收款单']:
        return receivedAndPaymentBatchDel;
      case BillTypeEnum['费用单']:
      case BillTypeEnum['其他收入单']:
        return expenseAndOtherIncomeBatchDel;
    }
  }, [expenseAndOtherIncomeBatchDel, receivedAndPaymentBatchDel, type]);

  useTabRefresh({
    refreshFunc: () => {
      form.resetFields();
      tableRef.current?.reload();
    },
  });

  const totalAmount = useMemo(() => {
    return billStatisticsAmount?.data?.totalAmount;
  }, [billStatisticsAmount?.data?.totalAmount]);

  const totalAllocatedAmount = useMemo(() => {
    return billStatisticsAmount?.data?.allocatedAmount;
  }, [billStatisticsAmount?.data?.allocatedAmount]);

  const totalWaitAllocatedAmount = useMemo(() => {
    return billStatisticsAmount?.data?.waitAllocatedAmount;
  }, [billStatisticsAmount?.data?.waitAllocatedAmount]);

  const getExportBusinessType = useCallback(() => {
    switch (type) {
      case BillTypeEnum['付款单']:
        return ExportBusinessType['付款单导出'];
      case BillTypeEnum['收款单']:
        return ExportBusinessType['收款单导出'];
      case BillTypeEnum['费用单']:
        return ExportBusinessType['费用单导出'];
      case BillTypeEnum['其他收入单']:
        return ExportBusinessType['其他收入单导出'];
      default:
        return ExportBusinessType.其他收入单导出;
    }
  }, [type]);

  const customItems = useMemo(() => {
    if (type === BillTypeEnum['其他收入单']) {
      return [
        {
          key: 'tag',
          authId: '930',
          label: (
            <ModifyExpirationTimeModal
              tableRef={tableRef}
              updateFunc={stockExpenseAndOtherIncomeBillList}
            />
          ),
        },
      ];
    }
  }, [tableRef, type]);

  const leftToolbar = useMemo(() => {
    return (
      <Space>
        <Button type="primary" authId={authIdObj?.add} onClick={toAddPage}>
          新增
        </Button>
        <BatchDropdown
          tableRef={tableRef}
          reload={() => {
            tableRef.current?.reload();
          }}
          customItems={{ after: customItems }}
          deleteProps={{
            authId: authIdObj?.deleteDraft,
            tableId: 'outWarehouseList',
            content: (
              <>
                <div>是否确认删除所选数据</div>
                <div className="text-text-secondary">
                  注：删除时会自动过滤单据状态=已确认的单据
                </div>
              </>
            ),
            batchResult: true,
            optName: '批量删除草稿',
            onConfirm: (ids: RawValue[]) => {
              return mutateBatchDelete({ ids: ids.join(',') });
            },
          }}
        />
      </Space>
    );
  }, [
    tableRef,
    authIdObj?.add,
    authIdObj?.deleteDraft,
    customItems,
    mutateBatchDelete,
    toAddPage,
  ]);

  const requestApi = useMemo(() => {
    switch (type) {
      case BillTypeEnum['付款单']:
      case BillTypeEnum['收款单']:
        return financeReceiveAndPaymentBillQueryOrderList;
      case BillTypeEnum['费用单']:
      case BillTypeEnum['其他收入单']:
        return financeExpenseAndOtherIncomeBillList;
      // default:
      //   return undefined;
    }
  }, [type]);

  const tabsItems: TabsProps['items'] = useMemo(() => {
    const detailName = () => {
      switch (type) {
        case BillTypeEnum['付款单']:
          return '付款明细';
        case BillTypeEnum['收款单']:
          return '收款明细';
        case BillTypeEnum['费用单']:
          return '费用明细';
        case BillTypeEnum['其他收入单']:
          return '其它收入明细';
      }
    };

    const settlementTab = () => {
      switch (type) {
        case BillTypeEnum['付款单']:
        case BillTypeEnum['收款单']:
          return [
            {
              key: TabKeyType['结算单据'],
              label: '结算单据',
              children: (
                <BusinessBillList
                  key={clickedRow?.id}
                  record={clickedRow}
                  tabSearchSettleBillNo={tabSearchSettleBillNo}
                />
              ),
            },
          ];
        default:
          return [];
      }
    };

    return [
      {
        key: TabKeyType['明细'],
        label: detailName(),
        children: (
          <BillDetail key={clickedRow?.id} record={clickedRow} type={type} />
        ),
      },
      ...settlementTab(),
      getOperateLogTabItem({
        operateModule,
        searchSource: SystemLogType.ERP,
        recordId: clickedRow?.id ?? '',
      }),
    ];
  }, [clickedRow, type, operateModule, tabSearchSettleBillNo]);

  const summaryData = useMemo(() => {
    switch (type) {
      case BillTypeEnum['付款单']:
      case BillTypeEnum['收款单']:
        return {
          receiveTotalAmount: totalAmount,
          allocatedAmount: totalAllocatedAmount,
          waitAllocateAmount: totalWaitAllocatedAmount,
        };
      case BillTypeEnum['费用单']:
      case BillTypeEnum['其他收入单']:
        return {
          detailTotalAmount: totalAmount,
          totalAmount: totalAllocatedAmount,
          waitAllocateAmount: totalWaitAllocatedAmount,
        };
      default:
        return undefined;
    }
  }, [totalAllocatedAmount, totalAmount, totalWaitAllocatedAmount, type]);

  const getExportParams = useCallback(() => {
    const param = form.getFieldsValue();
    const params = {
      queryCondition: {
        labelValue: searchBillNo,
        billNoList: initialBillNoList,
        ...param,
        billType: type,
      },
    };

    return params;
  }, [form, searchBillNo, initialBillNoList, type]);

  return (
    <Flex vertical className="flex-1">
      <BizTable
        isTableFullScreen
        table={{
          id: '1733903964420',
          columns,
          tableRef,
          rowKey: 'id',
          rowSelection: {
            type: 'checkbox',
            fixed: 'left',
            columnWidth: 50,
          },
          wrapperClassName: 'px-[12px]',
          autoRowClickStyle: true,
          onRow: (record) => ({
            onClick: () => {
              setClickedRow(record);
            },
          }),
        }}
        search={{
          form,
          searchColumns,
          initialValues,
          onQuery: () => {
            setSelectedRows([]);
          },
          className: 'px-[8px]',
        }}
        exportConfig={{
          businessType: getExportBusinessType(),
          authId: authIdObj?.export ?? '',
          getParams: getExportParams,
        }}
        requestConfig={{
          api: requestApi,
          extraParams: {
            billType: type,
          },
          beforeData: {
            beforeListData: (data, res: CommonRecord) => {
              const { list } = res;
              if (list?.length) {
                setClickedRow(list[0]);
              } else {
                setClickedRow(undefined);
              }

              return data;
            },
            beforeSearchData: (data) => {
              getBillStatisticsAmount(data);
              return data;
            },
          },
        }}
        summaryData={summaryData}
        toolbar={{ left: leftToolbar }}
        bottomView={
          <Flex
            className={clsx(
              styles.finance_bill_tabs_wrap,
              'h-full overflow-hidden',
            )}
          >
            <CustomTabs
              className="w-full"
              items={tabsItems}
              activeKey={tabActiveKey}
              onChange={(key) => {
                setTabActiveKey(key);
              }}
            />
          </Flex>
        }
      />
    </Flex>
  );
};
