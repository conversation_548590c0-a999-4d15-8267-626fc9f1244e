import {
  GoodsImage,
  message,
  Modal,
  TableOperationGroup,
  Typography,
  type TableColumnType,
} from '@slt/base-ui';
import {
  BizTable,
  type BizTableRef,
  type QueryFormListItem,
} from '@slt/biz-ui';
import { platformDataSearchApi, type PlatformDataDTO } from '@slt/services';
import type { TemplateTypeEnum } from '../utils';
import { templateTypeMap } from '../utils';
import { useRef } from 'react';
import { PLACEHOLDER } from '@slt/utils';
import type { CommonRecord } from '@slt/base-utils';
import { isNil } from 'lodash-es';

/** 添加关联平台资料 */

interface Props {
  disabledValue?: string[];
  templateType: TemplateTypeEnum;
  trigger: JSX.Element;
  onSuccess: (data: PlatformDataDTO[]) => Promise<boolean> | boolean;
}

export const AddRelationPlatformDataModal = (props: Props) => {
  const { trigger, templateType, onSuccess, disabledValue } = props;

  const tableRef = useRef<BizTableRef<PlatformDataDTO>>(null);

  const searchColumns: QueryFormListItem[] = [
    {
      type: 'batchInput',
      formItemProps: {
        label: '资料ID',
        name: 'platformIdList',
      },
    },
    {
      type: 'batchInput',
      formItemProps: {
        label: '商品编码',
        name: 'platformCodeList',
      },
    },
    {
      type: 'input',
      formItemProps: {
        label: '资料名称',
        name: 'platformName',
      },
    },
  ];

  const columns: TableColumnType<PlatformDataDTO>[] = [
    {
      key: 'spuMainImage',
      title: '主图',
      render: (img?: string) => {
        if (!img) {
          return PLACEHOLDER;
        }

        return <GoodsImage src={img} size="small" />;
      },
    },
    {
      key: 'platformDataId',
      title: '资料ID',
    },
    {
      key: 'distributionName',
      title: '资料名称',
    },
    {
      key: 'distributionCode',
      title: '商品编码',
    },
    {
      key: 'distributionPrice',
      title: '基础分销价',
      render: (_, record: CommonRecord) => {
        const { distributionPriceMin, distributionPriceMax } = record;

        if (distributionPriceMin !== distributionPriceMax) {
          return `${distributionPriceMin}-${distributionPriceMax}`;
        }

        return distributionPriceMin;
      },
    },
    {
      key: 'status',
      title: '状态',
      render: (text: number) => {
        if (isNil(text)) {
          return <Typography.Text>{PLACEHOLDER}</Typography.Text>;
        }

        const txt = Number(text) === 0 ? '启用' : '停用';
        return <Typography.Text title={txt}>{txt}</Typography.Text>;
      },
    },
  ];

  const handleOk = async (cb: () => void) => {
    const selectedRows = tableRef.current?.getSelectedRows().selectedRows;

    if (!selectedRows?.length) {
      message.warning('请至少选择一项');
      return;
    }

    const result = await onSuccess(selectedRows);

    if (result) {
      cb();
    }
  };

  return (
    <Modal
      fixedMaxHeight
      type="edit"
      trigger={trigger}
      size="xl"
      title="添加关联平台资料"
      tableId="1733228691875"
      onOk={handleOk}
    >
      <BizTable
        search={{
          searchColumns,
          proportion: 3,
          style: { marginBottom: 0, padding: '0 4px' },
        }}
        requestConfig={{
          api: () => ({ url: platformDataSearchApi }),
          extraParams: {
            templateType: templateTypeMap[templateType],
          },
        }}
        table={{
          id: '1733228691875',
          rowKey: 'platformDataId',
          columnsSetting: false,
          resizableHeader: false,
          columns: columns,
          tableRef,
          rowSelection: {
            getCheckboxProps: (record: any) => ({
              disabled:
                !record.allow ||
                Boolean(disabledValue?.includes(record.platformDataId)),
            }),
          },
        }}
      />
    </Modal>
  );
};
