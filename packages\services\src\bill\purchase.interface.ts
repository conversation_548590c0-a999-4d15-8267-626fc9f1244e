import type {
  BillBusinessType,
  BillCreateTypeEnum,
  BillTypeEnum,
  CommonBillStatusType,
} from '@slt/utils';
import type { StrOption } from '@slt/base-ui';
import type {
  BasicGoodsPageSkuDTO,
  BasicSubGoodsDTO,
} from '../basic/goods/goods.type';
import type { Dayjs } from 'dayjs';
import type { ErrorResultCheckedType } from '../common.types';

/** 采购单，数据合计请求参数 */
export type BillPurchaseTotalDataParams = {
  /**
   * 单据时间
   * billDateTimeStart：开始时间
   * billDateTimeEnd：结束时间
   */
  billDateTimeEnd?: string;
  /**
   * 单据时间
   * billDateTimeStart：开始时间
   * billDateTimeEnd：结束时间
   */
  billDateTimeStart?: string;
  /**
   * 单据编号
   */
  billNo?: string;
  /**
   * 单据状态，1:草稿，2:确认，(审核)
   */
  billStatus?: number;
  /**
   * 单据类型
   */
  billType?: string;
  /**
   * 是否为云仓单据
   */
  cloudWarehouseBill?: boolean;
  /**
   * 部门id
   */
  departmentId?: number;
  /**
   * 外部单号
   */
  extOriginalId?: string;
  /**
   * 过滤掉已完全退货的出库单
   */
  filterCompleteReturn?: boolean;
  page?: number;
  pageSize?: number;
  /**
   * 收货状态
   */
  receivedStatus?: number;
  /**
   * 收货状态
   */
  receivedStatusList?: number[];
  /**
   * 备注
   */
  remark?: string;
  /**
   * 业务员id
   */
  salesManId?: number;
  /**
   * 商品规格id
   */
  skuId?: number;
  skuIdList?: number[];
  /**
   * 供应商id
   */
  supplierId?: number;
  /**
   * 租户id
   */
  tenantId?: number;
  /**
   * 仓库id
   */
  warehouseId?: number;
};
/** 采购单，数据合计响应参数 */
export type BillPurchaseTotalDataResponse = {
  /** 本单金额合计 */
  totalDiscountTotalAmount: number;
  /** 本单欠款合计 */
  totalBillArrears: number;
};
export type BillPurchasePageQueryDTO = {
  id: string;
  tenantId: string;
  billNo: string;
  billType: string;
  billTypeText: string;
  businessType: number;
  billDateTime: string;
  supplierId: string;
  supplierName: string;
  warehouseId: string;
  warehouseName: string;
  totalAmount: number;
  totalQty: number;
  discountTotalAmount: number;
  paymentAmount: number;
  advancePayment: number;
  billArrears: number;
  billStatus: number;
  billStatusText: string;
  billCreateType: number;
  billCreateTypeText: string;
  createUserId: string;
  createUserName: string;
  delete: boolean;
  gmtCreate: string;
  gmtModified: string;
  [key: string]: any;
};

/**
 * 采购单接口定义
 */
export type AddPurchaseBillParams = {
  /**
   * 单据id
   */
  id?: string;

  /**
   * 业务类型
   */
  businessType?: string;

  /**
   * 单据编号
   */
  billNo?: string;

  /**
   * 单据日期时间
   */
  billDateTime?: string;

  /**
   * 供应商id
   */
  supplierId?: string;

  /**
   * 仓库id
   */
  warehouseId?: string;

  /**
   * 业务员id
   */
  salesManId?: string;

  /**
   * 部门id
   */
  departmentId?: string;

  /**
   * 单据备注
   */
  remark?: string;

  /**
   * 付款账户id
   */
  paymentAccount?: string;

  /**
   * 结算方式id
   */
  settlementType?: string;

  /**
   * 付款金额
   */
  paymentAmount?: number;

  /**
   * 使用预付款余额额度
   */
  advancePayment?: number;

  /**
   * 本单欠款
   */
  billArrears?: number;

  /**
   * 采购单状态
   * 保存时需要，如果保存为草稿则传1，如果直接保存为数据则传2
   */
  billStatus?: 1 | 2;
  /** 商品明细 */
  purchaseBillDetailList?: PurchaseBillGoodsDTO[];
  /**
   * 单据创建来源
   */
  billCreateType?: BillCreateTypeEnum;
  /** 省市区地址 */
  receiveAddressInfo?: string;
};

/**商品信息价格明细数据 */
export type BasicPurchaseBillDetailDTO = {
  id: string;
  /**
   * 商品id
   */
  goodsId: string;

  /**
   * 规格id
   */
  skuId: string;

  /**
   * 采购数量
   */
  purchaseQty?: number;

  /**
   * 折前单价
   */
  costAmount?: number;

  /**
   * 折前总金额
   * 计算得来的，通过采购数量 * 折前单价
   */
  totalAmount?: number;

  /**
   * 折扣
   * 默认为1，折扣以小数计数，如0.9为9折，支持两位小数
   */
  discount?: number;

  /**
   * 折后单价
   * 折后单价 = 折前单价 * (1-折扣)
   */
  discountCostAmount?: number;

  /**
   * 折后总金额
   * 折后总金额 = 折后单价 * 采购数量
   */
  discountTotalAmount?: number;

  /**
   * 备注
   */
  remark: string;

  /**
   * 是否套餐
   */
  isBom: boolean;

  /**
   * 是否赠品
   */
  isGift: boolean;
  /**子商品分摊比例 */
  ratio: number;
  /**  */
  goodsNo?: string;
  /**商品名称 */
  goodsName: string;
  /**商品编码 */
  goodsCode: string;
  /**  */
  goodsSkuName?: string;
  /**  */
  goodsSkuNo?: string;
  /**规格名称 */
  skuName: string;
  /**规格编码 */
  skuCode: string;
  /**组合品-子商品信息价格明细数据 */
  detailList: BasicPurchaseBillDetailDTO[];
  /**商品所在行号 */
  resourceRowId: number;
  /** 供应商id */
  supplierId?: string;
  /** 供应商名称 */
  supplierName?: string;
  /**  */
  warehouseId?: string;
  /**  */
  warehouseName?: string;
  /** 定价 */
  price?: number;
  /**  */
  originQuality?: number;
  /**  */
  billNo?: string;
  /**  */
  billId?: string;
  /**  */
  pricingPrice?: number | string;
};

export type PurchaseBillDetailParams = Omit<
  AddPurchaseBillParams,
  'billDateTime'
> & {
  /** 单据日期 */
  billDateTime?: Dayjs;
  /**制表人 */
  createUserName?: string;
  /**制表时间 */
  gmtCreate?: string;
  /**保存时间 */
  gmtModified?: string;
};

export type CheckResultParams = {
  resultList?: CheckResultDTO[];
  limitedResultList?: CheckResultDTO[];
  continuesResultList?: CheckResultDTO[];
  /**是否能够进行删除 */
  continues: boolean;
};

export type CheckResultDTO = {
  type: ErrorResultCheckedType;
  errorData: ErrorDataDTO;
};

export type ErrorDataDTO = {
  errorMsg: string;
  billList?: ResultBillDTO[];
  goodsList?: ResultGoodsDTO[];
  failCount?: number;
  businessBillList?: BusinessBillListDTO[];
};

export type ResultBillDTO = {
  /**单据编号 */
  billNo: string;
  resourceId: string;
  resourceType: string;
  resourceTypeText: string;
  resourceStatus: string;
};

export type ResultGoodsDTO = {
  rowNo: string;
  goodsCode: string;
  goodsName: string;
  skuName: string;
  skuCode: string;
};

export type BusinessBillListDTO = {
  businessBillId: string;
  businessBillNo: string;
  businessBillType: string;
  businessBillTypeText: string;
  financeBillList?: { billType: string; billNo: string }[];
  financeBillLists?: string;
};

export type PurchaseBillDetailGoodsCommonDTO = BasicSubGoodsDTO & {
  id?: string;
  /** 商品编码 */
  goodsCode?: string;
  /** 已入库数量 */
  inStorageQty?: number;
  /** 组合商品 */
  isBom?: boolean;
  /** 赠品 */
  isGift?: boolean;
  /** 数量 */
  purchaseQty?: number;
  /** 规格编码 */
  skuCode?: string;
  /** 规格id */
  skuId?: string;
  /** 规格名称 */
  skuName?: string;
};

/** 采购入库单详情商品明细 */
export type PurchaseBillDetailGoodsDetailDTO =
  PurchaseBillDetailGoodsCommonDTO & {
    detailList?: ({
      extJson?: string;
    } & PurchaseBillDetailGoodsCommonDTO)[];
  };

/** 采购入库单详情 */
export type BillQueryPurchaseBillDetailDTO = {
  /** 供应商id */
  supplierId?: string;
  /** 供应商名称 */
  supplierName?: string;
  /** 业务对象 */
  bizUserId?: number;
  /** 仓库id */
  warehouseId?: string;
  /** 仓库名称 */
  warehouseName?: string;
  /** 业务员id */
  salesManId?: string;
  /** 部门id */
  departmentId?: number;
  /** 部门ids */
  wholeDepartmentId?: string[];
  /** 备注 */
  remark?: string;
  /** 付款账户 */
  paymentAccount?: string;
  /** 结算方式 */
  settlementType?: string;
  /** 使用预付款 */
  advancePayment?: number;
  /** 本单欠款 */
  billArrears?: number;
  /**  */
  billCreateType?: number;
  /**  */
  billCreateTypeText?: string;
  /** 单据日期 */
  billDateTime?: string;
  /** 单据编号 */
  billNo?: string;
  /** 单据状态 */
  billStatus?: CommonBillStatusType;
  /** 单据状态文本 */
  billStatusText?: string;
  /** 单据类型 */
  billType?: string;
  /** 单据类型文本 */
  billTypeText?: string;
  /** 业务类型 */
  businessType?: BillBusinessType;
  /** 创建类型 */
  createTypeText?: string;
  /** 创建人id */
  createUserId?: string;
  /** 创建人 */
  createUserName?: string;
  /** 本单应付 */
  discountTotalAmount?: number;
  /** 付款金额 */
  paymentAmount?: number;
  /** 本单应付 */
  totalAmount?: number;
  /** 商品明细 */
  purchaseBillDetailList?: PurchaseBillDetailGoodsDetailDTO[];
  /**  */
  receiveAddressInfoVO?: {
    /** 省市区 */
    cascadeNodeList?: StrOption[];
    /** 详细地址 */
    detailAddress?: string;
  };
};

/** 采购订单列表查询 */
export type BillPurchasePageQueryParams = {
  /** 单据类型 */
  billType?: BillTypeEnum;
  /** 单据编号 */
  billNo?: string;
  /** 供应商 */
  supplierId?: string;
  /** 单据结束日期 */
  billDateTimeEnd?: string;
  /** 单据开始日期 */
  billDateTimeStart?: string;
  /** 仓库 */
  warehouseId?: string;
  /** 单据状态 */
  billStatus?: number;
  /** 收货状态 */
  receivedStatus?: number;
  /** 业务员 */
  salesManId?: string;
  /** 部门 */
  departmentId?: string;
  /** 包含商品 */
  skuIdList?: string[];
  /** 外部单号 */
  extOriginalId?: string;
  /** 备注 */
  remark?: string;
};

/** 采购类单据金额相关 */
export type PurchaseBillWatchFieldsFormValues = {
  /** 本单应付 */
  discountTotalAmount?: number;
  /** 收/付款金额 */
  paymentAmount?: number;
  /** 使用预付款 */
  advancePayment?: number;
  /** 本单欠款 */
  billArrears?: number;
};

/** 采购单据表单公共字段 */
export type PurchaseBillBasicFormValues = {
  /** 单据编号 */
  billNo?: string;
  /** 单据日期 */
  billDateTime?: Dayjs;
  /** 供应商 */
  supplierId?: StrOption;
  /** 仓库 */
  warehouseId?: StrOption & { cloudWarehouseDockingId?: string };
  /** 业务员 */
  salesManId?: StrOption;
  /** 部门 */
  departmentId?: string[];
  /** 单据备注 */
  remark?: string;
  /** 商品明细 */
  goodsList?: PurchaseBillGoodsDTO[];
};

/** 采购订单-单据商品 */
export type BillQueryPurchaseGoodsDetailDTO = {
  id: string;
  /**  */
  billId?: string;
  /** 单据编号 */
  billNo?: string;
  /** 单据类型 */
  billType?: BillTypeEnum;
  /** 折前单价 */
  costAmount?: string;
  /** 折前金额 */
  totalAmount?: string;
  /** 折扣 */
  discount?: string;
  /**  */
  discountAmount?: string;
  /** 折后单价 */
  discountCostAmount?: string;
  /** 折后金额 */
  discountTotalAmount?: string;
  extJson?: string;
  /** 商品编码 */
  goodsCode?: string;
  /** 商品id */
  goodsId?: string;
  /** 商品名称 */
  goodsName?: string;
  /** 已入库数量 */
  inStorageQty?: number | string;
  /** 是否组合品 */
  isBom?: boolean;
  /** 是否赠品 */
  isGift?: boolean;
  /** 数量 */
  purchaseQty?: number | string;
  /** 已退货数量 */
  returnNum?: number | string;
  /** 未退货数量 */
  returnableNum?: number | string;
  /**  */
  maxReturnableNum?: number | string;
  /** 规格编码 */
  skuCode?: string;
  /** 规格id */
  skuId?: string;
  /** 规格名称 */
  skuName?: string;
  /** 待入库数量 */
  waitStorageQty?: number | string;
};

/** 采购类单据商品明细列表 */
export type PurchaseBillGoodsDTO = BasicGoodsPageSkuDTO &
  BasicSubGoodsDTO & {
    uid?: string;
    /**  */
    parentDetailId?: string;
    /**  */
    parent_uid?: string;
    /** 商品编码 */
    goodsCode?: string;
    /** 规格编码 */
    skuCode?: string;
    /** 规格名称 */
    skuName?: string;
    /** 定价(接口返回:导入商品/单据商品) */
    pricingPrice?: string;
    /** 数量 */
    purchaseQty?: number;
    /** 折前单价 */
    costAmount?: number;
    /** 折前金额 */
    totalAmount?: number;
    /** 折扣 */
    discount?: number;
    /** 折后单价 */
    discountCostAmount?: number;
    /** 折后金额 */
    discountTotalAmount?: number;
    /** 优惠分摊金额 */
    discountAmount?: number;
    /** 明细备注 */
    remark?: string;
    /** 子商品分摊比例（子商品才有该字段） */
    ratio?: number;
    /** 子商品原始数量（子商品才有该字段） */
    originQuality?: number;

    /** 是否组合品 */
    isBom?: boolean;
    /** 是否赠品 */
    isGift?: boolean;
    /** 是否展开 */
    expand?: boolean;
    /** 是否子商品 */
    isChild?: boolean;

    /** 关联原单 */
    resourceNo?: string;
    /** 商品所在行号 */
    resourceRowId?: number;
    /**  */
    resourceType?: string;
    /**  */
    skuId?: string;
    /**  */
    orderId?: string;
    /** 商品所在单据编号(采购订单商品) */
    billNo?: string;
    /** 商品所在单据id(采购订单商品) */
    billId?: string;
    /** 待入库数量(采购订单商品) */
    waitStorageQty?: string;
    /** 已入库得数量(采购订单商品) */
    inStorageQty?: number;
    /** 已退货数量(采购订单商品) */
    returnNum?: number;
    /** 未退货数量(采购订单商品) */
    returnableNum?: number;
    /**  */
    maxReturnableNum?: number;
    /** 供应商id(采购订单商品) */
    supplierId?: string;
    /** 供应商名称(采购订单商品) */
    supplierName?: string;
    /** 仓库id(采购订单商品) */
    warehouseId?: string;
    /** 仓库名称(采购订单商品) */
    warehouseName?: string;
    /** (采购订单商品) */
    receivedQty?: number;

    /** 商品关联的采购退货单编号 */
    returnBillNo?: string;
    /** 商品关联的采购单id */
    returnBillId?: string;
    /** 商品关联的采购退货单明细id */
    returnBillDetailId?: string;

    /** 固定成本价(采购退货单) */
    handWriteCostPrice?: number;
    extJson?: string;
  };
