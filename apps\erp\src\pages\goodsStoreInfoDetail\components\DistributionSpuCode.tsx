import { useEffect, useMemo, useState, type MutableRefObject } from 'react';
import { Flex, Form, Input, Typography } from '@slt/base-ui';
import { useQuery } from '@slt/net-query';
import type { DistributionDetailDTO } from '@slt/services';
import {
  basicDistributionCheckGoodsSpuNoIsRepeat,
  findMaterialInfoDistributionCode,
} from '@slt/services';
import { GoodsMatchModal } from './GoodsMatchModal';
import type { CommonRecord } from '@slt/base-utils';
import { useDistributionLevelPriceConfig } from '@/hooks/data/useDistributionLevelPriceConfig';
import { handleBatchModifyDistributionPrice } from '@/utils/price';
import type { GoodsStoreInfoFormValues } from './GoodsStoreInfoForm';
import type { DistributionTypeEnum } from '@slt/utils';

interface Props {
  pricingPriceRef: MutableRefObject<number | undefined>;
  type: 'add' | 'edit' | 'view';
  value?: string;
  isDistributedGoods?: boolean;
  oldDistributionSpuCode?: string;
  onChange?: (value: string) => void;
  onApply?: (data: DistributionDetailDTO) => void;
  onRepeatable?: (msg?: string) => void;
  disabled?: boolean;
  editId?: string;
  distributionType?: DistributionTypeEnum;
  supplier?: { id: number; name: string };
}

export const DistributionSpuCode = (props: Props) => {
  const {
    pricingPriceRef,
    value,
    oldDistributionSpuCode,
    isDistributedGoods,
    type,
    onChange,
    onApply,
    onRepeatable,
    disabled,
    editId,
    distributionType,
    supplier,
  } = props;
  const { levelPriceConfig } = useDistributionLevelPriceConfig({ type: 2 });

  const [searchValue, setSearchValue] = useState('');
  const [isApply, setIsApply] = useState(false);

  const isSameCode = useMemo(() => {
    return searchValue === oldDistributionSpuCode;
  }, [searchValue, oldDistributionSpuCode]);

  const {
    code: editCheckCode,
    message: editCheckMsg,
    isLoading: editCheckLoading,
  } = useQuery(
    basicDistributionCheckGoodsSpuNoIsRepeat({
      distributionSpuCode: searchValue,
      distributionType,
      supplierId: supplier?.id,
      id: editId,
    }),
    {
      queryOptions: {
        enabled: !!searchValue && !isSameCode && !!editId,
      },
    },
  );

  const {
    data,
    code: addCheckCode,
    message: addCheckMsg,
    isLoading: addCheckLoading,
  } = useQuery(
    findMaterialInfoDistributionCode({
      distributionSpuCode: searchValue,
    }),
    {
      queryOptions: {
        enabled: !!searchValue && !isSameCode && !editId,
      },
    },
  );

  useEffect(() => {
    if ((editCheckCode === 3001 || editCheckCode === 3002) && editCheckMsg) {
      // 编辑资料，商品编码未通过校验
      onRepeatable?.(editCheckMsg);
    } else if (addCheckCode === 3001 && addCheckMsg) {
      // 新增资料，商品编码未通过校验
      onRepeatable?.(addCheckMsg);
    } else if (editCheckCode || addCheckCode) {
      onRepeatable?.();
    }
  }, [editCheckCode, addCheckCode, editCheckMsg, addCheckMsg, onRepeatable]);

  const form = Form.useFormInstance<GoodsStoreInfoFormValues>();

  const handleBlur = async (e: React.FocusEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value ?? '');
    if (searchValue !== value) {
      // 情况报错信息
      onRepeatable?.();
      setIsApply(false);
      const { specificationDetails } = form.getFieldsValue();
      form.setFieldValue(
        'specificationDetails',
        specificationDetails?.map((item, index) => {
          if (index === 0 && type === 'add' && !isDistributedGoods) {
            return {
              ...item,
              distributionSkuCode: value,
            };
          }
          return item;
        }),
      );
    }
  };

  useEffect(() => {
    if (data && data.length > 0) {
      if (!data[0]?.pricingPrice) {
        return;
      }

      pricingPriceRef.current = data[0].pricingPrice;
      const specificationDetails = form.getFieldValue(
        'specificationDetails',
      ) as CommonRecord[];

      const newSpecificationDetails = handleBatchModifyDistributionPrice(
        specificationDetails,
        'price',
        Number(data[0].pricingPrice),
        levelPriceConfig,
      );
      form.setFieldsValue({
        specificationDetails: newSpecificationDetails,
      });
    }
  }, [form, value, levelPriceConfig, data, pricingPriceRef]);

  const renderMatchContent = useMemo(() => {
    if (isSameCode) {
      return null;
    }

    if (addCheckLoading || editCheckLoading) {
      return <Typography.Text>匹配中...</Typography.Text>;
    }

    if (editId) {
      // TODO: caomu -
    } else {
      if (searchValue && addCheckCode !== 3001 && !data) {
        return (
          <Typography.Text className="!text-description">
            未匹配到商品
          </Typography.Text>
        );
      }

      if (!isApply && data?.length) {
        return (
          <Flex gap={8}>
            <Typography.Text>
              {data.length === 1 ? '匹配到商品' : '匹配到多个商品'}
            </Typography.Text>
            {data.length === 1 && type === 'add' ? (
              <Typography.Link
                onClick={() => {
                  setIsApply(true);
                  onApply?.(data[0]);
                }}
              >
                点击快速导入
              </Typography.Link>
            ) : (
              <GoodsMatchModal
                data={data}
                trigger={<Typography.Link>请选择一个快速导入</Typography.Link>}
                onApply={(data) => {
                  setIsApply(true);
                  onApply?.(data);
                }}
              />
            )}
          </Flex>
        );
      }
    }

    return null;
  }, [
    isSameCode,
    addCheckLoading,
    editCheckLoading,
    searchValue,
    addCheckCode,
    data,
    isApply,
    onApply,
    type,
  ]);

  return (
    <Flex vertical>
      <Input
        value={value}
        disabled={disabled}
        onChange={(e) => onChange?.(e.target.value)}
        placeholder="请输入商品编码，若该商品编码在商品信息或分销商品管理中存在，可快速导入"
        onBlur={handleBlur}
        showCount
        maxLength={50}
      />
      {renderMatchContent}
    </Flex>
  );
};
