import { TextItemEdit } from '@/components/biz';
import { OrderMarkTag } from '@/pages/order/components/orderMarkTag/OrderMarkTag';
import { OrderTag } from '@/pages/order/components/orderTag/OrderTag';
import type { TableColumnType, IOption } from '@slt/base-ui';
import {
  ColumnWidth,
  Flex,
  GoodsImage,
  inputNumberProps,
  msgWarn,
  Space,
  Tag,
  TypographyV2,
} from '@slt/base-ui';
import {
  omsAfterSalesUpdateLogistics,
  omsAfterSalesUpdateReason,
  omsAfterSalesUpdateRemark,
  omsAfterSalesUpdateType,
  omsAfterSalesUpdateWarehouse,
  omsAfterSalesUpdateDetailPostAmount,
  type AfterSaleOrderDetailDTO,
  type OmsAfterSaleMapListSelectInputVO,
  AfterSaleViewModeEnum,
} from '@slt/services';
import {
  AfterSalesBusinessTypeView,
  AfterSalesBusinessType,
  BillTypeEnum,
  enumToOptions,
  maxInputDecimal,
  SupplierAfterSaleStatusEnum,
  SupplierRefundStatusEnum,
} from '@slt/utils';
import { OrderDetails } from '../../orderDetails/OrderDetails';
import { OrderDetails as AfterSaleDetails } from '../afterSaleOrderDetail/OrderDetails';
import { ReturnedStatus } from '../afterSaleOrderDetail/type';
import {
  SupplierAfterSaleReturnStatusColor,
  SupplierAfterSaleStatusColor,
} from './types';
import { AfterSalesBusinessTypeColor } from '../types';
import { AfterSaleOrderPermission } from '../permission';
import { OrderStatus } from './constants';
import { AfterSaleRemarkAttachment } from '../components/supplierDistributionAttachment';
import { getIsOnlineOrder } from '../constants';
import { parseJSON } from '@slt/base-utils';

export const getColumns = (props: {
  onReload: () => void;
  dataEnum?: OmsAfterSaleMapListSelectInputVO;
  editAuthAble: boolean;
  openBillDetailTabByBillType: (
    id: string,
    billType: BillTypeEnum | string,
  ) => void;
}): TableColumnType<AfterSaleOrderDetailDTO>[] => {
  const { onReload, dataEnum, editAuthAble, openBillDetailTabByBillType } =
    props;
  return [
    {
      width: ColumnWidth.Action,
      title: '标记',
      dataIndex: 'orderMark',
      key: 'orderMark',
      render: (_, record) => (
        <OrderMarkTag orderMarkList={record.orderMarkList} />
      ),
    },
    {
      width: ColumnWidth.Action,
      title: '售后订单号',
      dataIndex: 'id',
      key: 'id',
      render: (_, record) => {
        return record.id ? (
          <AfterSaleDetails saleOrderId={record.id} text={record.id} copyable />
        ) : null;
      },
      batchCopy: true,
      sort: true,
    },
    {
      width: ColumnWidth.Action,
      title: '内部订单号',
      dataIndex: 'saleOrderId',
      key: 'saleOrderId',
      render: (value, record) => {
        return value ? (
          <OrderDetails
            saleOrderId={record.saleOrderId!}
            text={`${value}`}
            copyable
          />
        ) : null;
      },
      batchCopy: true,
      sort: true,
    },
    {
      width: 'time',
      title: '剩余处理时间',
      dataIndex: 'leftProcessTime',
      key: 'leftProcessTime',
      render: (_, record) => {
        const { deadlineProcessFlag, leftProcessTime } = record;
        let color = 'gray';
        if (deadlineProcessFlag === 1 && leftProcessTime) {
          color = 'red';
        }
        if (deadlineProcessFlag === 2) {
          color = 'orange';
        }
        return (
          <TypographyV2.Text style={{ color }}>
            {deadlineProcessFlag === 2 ? (
              <Tag color="orange">即将超时</Tag>
            ) : null}
            {deadlineProcessFlag === 1 && leftProcessTime ? (
              <Tag color="red">已超时</Tag>
            ) : null}
            {leftProcessTime || '-'}
          </TypographyV2.Text>
        );
      },
    },
    {
      width: 'time',
      title: '截止处理时间',
      dataIndex: 'deadlineProcessTime',
      key: 'deadlineProcessTime',
      sort: true,
    },
    {
      width: ColumnWidth.Remark,
      title: '售后状态',
      dataIndex: 'afterSaleStatusCaption',
      key: 'afterSaleStatusCaption',
    },
    {
      width: ColumnWidth.Remark,
      title: '系统售后类型',
      dataIndex: 'afterSaleTypeCaption',
      key: 'afterSaleTypeCaption',
      render: (value, record) => {
        const { afterSaleType, afterSaleStatus, afterSaleTypeCaption } = record;

        return (
          <TextItemEdit
            isEditAble={
              afterSaleStatus === OrderStatus.WaitAudit && editAuthAble
                ? true
                : false
            }
            authId={AfterSaleOrderPermission.编辑}
            request={omsAfterSalesUpdateType}
            value={{
              label: afterSaleTypeCaption,
              value: `${afterSaleType}`,
            }}
            labelInValue
            extraParams={{
              afterSaleOrderIdList: [record?.id],
            }}
            textRender={
              <Tag
                color={AfterSalesBusinessTypeColor[afterSaleType]}
                className="rounded-sm"
              >
                {value}
              </Tag>
            }
            textClassName="text-description"
            onOk={() => {
              onReload();
            }}
            configForm={{
              type: 'select',
              fieldProps: {
                options: enumToOptions(AfterSalesBusinessTypeView),
                fieldNames: {
                  label: 'label',
                  value: 'value',
                },
                searchKey: 'label',
              },
              formItemProps: {
                name: 'afterSaleType',
                rules: [{ required: true, message: '请选择售后类型' }],
              },
            }}
          />
        );
        // return value ? (
        //   <Tag
        //     color={
        //       AfterSalesBusinessTypeColor[
        //         afterSaleType as keyof typeof AfterSalesBusinessTypeColor
        //       ]
        //     }
        //     className="rounded-sm"
        //   >
        //     {value}
        //   </Tag>
        // ) : null;
      },
    },
    {
      width: ColumnWidth.Remark,
      title: '线上售后状态',
      dataIndex: 'mallAfterSaleStatusCaption',
      key: 'mallAfterSaleStatusCaption',
    },
    {
      width: ColumnWidth.Remark,
      title: '退货状态',
      dataIndex: 'returnStatusCaption',
      key: 'returnStatusCaption',
      render: (value) => {
        return (
          <TypographyV2.Text
            className={value === '全部收货' ? 'text-success' : ''}
          >
            {value}
          </TypographyV2.Text>
        );
      },
    },
    {
      width: ColumnWidth.Action,
      title: '退货物流公司',
      dataIndex: 'returnLogisticsName',
      key: 'returnLogisticsName',
      render: (_, record) => {
        const { returnStatus, returnLogisticsName } = record;
        if (returnStatus === ReturnedStatus.SellerNotGoods && editAuthAble) {
          return (
            <TextItemEdit
              value={{
                label: returnLogisticsName!,
                value: returnLogisticsName!,
              }}
              request={omsAfterSalesUpdateLogistics}
              extraParams={{
                afterSaleOrderIdList: [record?.id],
                returnLogisticsNo: record?.returnLogisticsNo,
              }}
              onOk={() => onReload?.()}
              labelInValue
              isAutoBlur
              textClassName="text-description"
              configForm={{
                type: 'returnLogisticsCompanySelect',
                fieldProps: {
                  labelInValue: true,
                  fieldNames: {
                    label: 'logisticsName',
                    value: 'logisticsName',
                  },
                },
                formItemProps: {
                  name: 'returnLogisticsName',
                },
              }}
            />
          );
        } else {
          return (
            <TypographyV2.Text>{returnLogisticsName || '-'}</TypographyV2.Text>
          );
        }
      },
    },
    {
      width: ColumnWidth.Action,
      title: '退货物流单号',
      dataIndex: 'returnLogisticsNo',
      key: 'returnLogisticsNo',
      render: (_, record) => {
        const { returnStatus, returnLogisticsNo } = record;
        if (returnStatus === ReturnedStatus.SellerNotGoods && editAuthAble) {
          return (
            <TextItemEdit
              value={returnLogisticsNo || ''}
              request={omsAfterSalesUpdateLogistics}
              extraParams={{
                afterSaleOrderIdList: [record?.id],
                returnLogisticsName: record?.returnLogisticsName,
              }}
              onOk={() => onReload?.()}
              textClassName="text-description"
              configForm={{
                type: 'input',
                formItemProps: {
                  name: 'returnLogisticsNo',
                },
              }}
            />
          );
        } else {
          return (
            <TypographyV2.Text>{returnLogisticsNo || '-'}</TypographyV2.Text>
          );
        }
      },
    },
    {
      width: ColumnWidth.Remark,
      title: '售后附件/备注',
      dataIndex: 'remark',
      key: 'remark',
      needExport: false,
      render: (_, record) => {
        return (
          <AfterSaleRemarkAttachment
            value={{
              afterSaleAttachment: {
                remark: record?.remark,
                attachmentUrlList: record?.attachment
                  ? record?.attachment?.split(',')
                  : [],
              },
            }}
            afterSaleOrderId={record?.id}
            viewMode={AfterSaleViewModeEnum.我的视角}
            hasAuth={editAuthAble}
            reload={onReload}
          />
        );
      },
    },
    {
      width: ColumnWidth.Remark,
      title: '供分销附件/备注',
      dataIndex: 'supplierDistributionRemark',
      key: 'supplierDistributionRemark',
      needExport: false,
      render: (_, record) => {
        const {
          supplierDistributionRemark,
          supplierDistributionAttachment,
          attachmentUpdateTime,
        } = record?.afterSaleOrderExtVO || {};
        return (
          <AfterSaleRemarkAttachment
            value={{
              afterSaleAttachment: {
                remark: supplierDistributionRemark,
                attachmentUrlList: supplierDistributionAttachment
                  ? supplierDistributionAttachment.split(',')
                  : [],
              },
              supplierDistributionAttachment: [
                {
                  attachmentUpdateTime: attachmentUpdateTime,
                  remark: supplierDistributionRemark,
                  attachmentUrlList: supplierDistributionAttachment
                    ? supplierDistributionAttachment.split(',')
                    : [],
                },
              ],
            }}
            reload={onReload}
            afterSaleOrderId={record?.id}
            afterOpen={() => {
              const isOnlineAfterSaleOrder = getIsOnlineOrder(
                record?.businessType,
                record?.qiMenSupplierFlag ?? false,
                record?.rootOrderId !== '-1',
              );
              return isOnlineAfterSaleOrder;
            }}
            viewMode={AfterSaleViewModeEnum.供分视角}
            hasAuth={editAuthAble}
          />
        );
      },
    },
    {
      width: ColumnWidth.Action,
      title: '换货/补发订单号',
      dataIndex: 'reissueSaleOrderId',
      key: 'reissueSaleOrderId',
      sort: true,
      render: (_, record) => {
        return record?.reissueSaleOrderId ? (
          <OrderDetails
            saleOrderId={record?.reissueSaleOrderId}
            text={record?.reissueSaleOrderId}
            copyable
          />
        ) : null;
      },
      batchCopy: true,
    },
    {
      width: ColumnWidth.Action,
      title: '换货/补发进度',
      dataIndex: 'reissueProcessCaption',
      key: 'reissueProcessCaption',
      render: (value) => {
        return value ? <TypographyV2.Text>{value}</TypographyV2.Text> : null;
      },
    },
    {
      width: ColumnWidth.Action,
      title: '线上交易单号',
      dataIndex: 'extOriginalId',
      key: 'extOriginalId',
      sort: true,
      render: (value, record) => {
        return value ? (
          <TypographyV2.Text copyable>{value}</TypographyV2.Text>
        ) : null;
      },
      batchCopy: true,
    },
    {
      width: ColumnWidth.Action,
      title: '原始订单号',
      dataIndex: 'sourceExtOriginalId',
      key: 'sourceExtOriginalId',
      sort: true,
      render: (value) => {
        return value ? (
          <TypographyV2.Text copyable>{value}</TypographyV2.Text>
        ) : null;
      },
      batchCopy: true,
    },
    {
      width: ColumnWidth.Action,
      title: '线上售后单号',
      dataIndex: 'afterExtOriginalId',
      key: 'afterExtOriginalId',
      sort: true,
      render: (value) => {
        return value ? (
          <TypographyV2.Text copyable>{value}</TypographyV2.Text>
        ) : null;
      },
      batchCopy: true,
    },
    {
      width: ColumnWidth.Action,
      title: '店铺名称',
      dataIndex: 'shopName',
      key: 'shopName',
      sort: true,
      render: (value, record) => {
        const { shopLogoUrl } = record;
        return (
          <Flex align="center">
            {shopLogoUrl ? (
              <GoodsImage
                src={shopLogoUrl}
                size="small"
                wrapperClassName="mr-xxs"
              />
            ) : null}
            <TypographyV2.Text>{value}</TypographyV2.Text>
          </Flex>
        );
      },
    },
    {
      width: ColumnWidth.Action,
      title: '买家账号',
      dataIndex: 'buyerNick',
      key: 'buyerNick',
      sort: true,
      render: (text, record) => {
        const { platformContactLogUrl } = record;
        return (
          <Space>
            {platformContactLogUrl ? (
              <GoodsImage size="small" src={platformContactLogUrl} />
            ) : null}
            {text}
          </Space>
        );
      },
    },
    {
      width: ColumnWidth.Remark,
      title: '应退金额',
      dataIndex: 'refundAmount',
      key: 'refundAmount',
      sort: true,
    },
    {
      width: ColumnWidth.Remark,
      title: '实退金额',
      dataIndex: 'refundRealAmount',
      key: 'refundRealAmount',
      sort: true,
    },
    {
      width: ColumnWidth.Remark,
      title: '退运费金额',
      dataIndex: 'refundPostAmount',
      key: 'refundPostAmount',
      sort: true,
      render: (value, record) => {
        const { afterSaleType } = record;
        if (afterSaleType !== AfterSalesBusinessType.补发) {
          return (
            <TextItemEdit
              value={`${record?.refundPostAmount || '-'}`}
              isEditAble={editAuthAble}
              authId={AfterSaleOrderPermission.编辑}
              request={omsAfterSalesUpdateDetailPostAmount}
              labelInValue={false}
              extraParams={{
                afterSaleOrderIdList: [record?.id],
              }}
              onOk={() => onReload?.()}
              textClassName="text-description"
              configForm={{
                type: 'inputNumber',
                formItemProps: {
                  name: 'refundPostAmount',
                  rules: [{ required: true, message: '退运费金额' }],
                },
                fieldProps: {
                  ...inputNumberProps.price,
                  min: 0,
                  max: maxInputDecimal,
                  placeholder: '退运费金额',
                },
              }}
            />
          );
        }
        return <TypographyV2.Text>{value}</TypographyV2.Text>;
      },
    },
    {
      width: ColumnWidth.Remark,
      title: '退款状态',
      tooltips:
        '退款给买家（C端消费者或分销商），当退款状态为已退款时，表示资金已经全部退回',
      dataIndex: 'refundStatusCaption',
      key: 'refundStatusCaption',
      render: (value, record) => {
        const { refundStatus, refundStatusCaption } = record;
        return (
          <TypographyV2.Text
            className={
              SupplierAfterSaleReturnStatusColor[
                refundStatus || SupplierRefundStatusEnum.无需退款
              ]
            }
          >
            {refundStatusCaption}
          </TypographyV2.Text>
        );
      },
    },
    {
      width: ColumnWidth.Action,
      title: '售后原因',
      dataIndex: 'refundReason',
      key: 'refundReason',
      render: (value, record) => {
        const { afterSaleStatus, refundReason } = record;
        const find = dataEnum?.refundReasonEnum?.find(
          (item) => item.key === refundReason,
        );
        const flag = afterSaleStatus === OrderStatus.WaitAudit && editAuthAble;
        if (flag) {
          return (
            <TextItemEdit
              value={(find?.key || value) as string}
              isEditAble={flag}
              request={omsAfterSalesUpdateReason}
              extraParams={{
                afterSaleOrderIdList: [record?.id],
              }}
              onOk={() => onReload?.()}
              textClassName="text-description"
              configForm={{
                type: 'select',
                fieldProps: {
                  options: dataEnum?.refundReasonEnum || [],
                  fieldNames: {
                    label: 'key',
                    value: 'key',
                  },
                },
                formItemProps: {
                  name: 'refundReason',
                  rules: [{ required: true, message: '请选择售后原因' }],
                },
              }}
            />
          );
        } else {
          return <>{refundReason || '-'}</>;
        }
      },
    },
    {
      width: ColumnWidth.Remark,
      title: '售后单来源',
      dataIndex: 'srcChannelCaption',
      key: 'srcChannelCaption',
    },
    {
      width: ColumnWidth.Remark,
      title: '买家售后留言',
      dataIndex: 'buyerRefundWords',
      key: 'buyerRefundWords',
      sort: true,
    },
    {
      width: ColumnWidth.Remark,
      title: '分销商',
      dataIndex: 'distributorName',
      key: 'distributorName',
    },
    {
      width: ColumnWidth.Remark,
      title: '供应商',
      dataIndex: 'supplierName',
      key: 'supplierName',
    },
    {
      width: ColumnWidth.Remark,
      title: '售后异常',
      dataIndex: 'refundExceptionCaption',
      key: 'refundExceptionCaption',
    },
    {
      width: ColumnWidth.Remark,
      title: '提交供应商',
      dataIndex: 'submitSupplierCaption',
      key: 'submitSupplierCaption',
    },
    {
      width: ColumnWidth.Remark,
      title: '供应商售后状态',
      dataIndex: 'supplierAfterSaleStatusCaption',
      key: 'supplierAfterSaleStatusCaption',
      render: (value, record) => {
        const { supplierAfterSaleStatus } = record;
        return (
          <TypographyV2.Text
            className={
              SupplierAfterSaleStatusColor[
                supplierAfterSaleStatus || SupplierAfterSaleStatusEnum.已取消
              ]
            }
          >
            {value || '-'}
          </TypographyV2.Text>
        );
      },
    },
    {
      width: ColumnWidth.Action,
      title: '供应商退款状态',
      tooltips: '供应商退款给我，当退款状态为已退款时，表示资金已经全部退回',
      dataIndex: 'supplierRefundStatusCaption',
      key: 'supplierRefundStatusCaption',
      render: (value, record) => {
        const { supplierRefundStatus, supplierRefundAmount } = record;
        return (
          <TypographyV2.Text
            className={
              SupplierAfterSaleReturnStatusColor[
                supplierRefundStatus || SupplierRefundStatusEnum.无需退款
              ]
            }
          >
            {value || '-'}
            {supplierRefundAmount &&
            SupplierRefundStatusEnum.无需退款 !== supplierRefundStatus ? (
              <TypographyV2.Text className="ml-[4px] text-text-quaternary">
                应退 {supplierRefundAmount}
              </TypographyV2.Text>
            ) : null}
          </TypographyV2.Text>
        );
      },
    },

    {
      width: ColumnWidth.Action,
      title: '退货仓库',
      dataIndex: 'refundWarehouseName',
      key: 'refundWarehouseName',
      render: (value, record) => {
        const { returnStatus, refundWarehouseId } = record;
        if (returnStatus === ReturnedStatus.SellerNotGoods && editAuthAble) {
          return (
            <TextItemEdit
              value={{
                label: record.refundWarehouseName!,
                value: refundWarehouseId!,
              }}
              request={omsAfterSalesUpdateWarehouse}
              extraParams={{
                afterSaleOrderIdList: [record?.id],
              }}
              onOk={() => onReload?.()}
              labelInValue
              textClassName="text-description"
              configForm={{
                type: 'warehouseSelect',
                fieldProps: {
                  params: {
                    permission: false,
                  },
                  fieldNames: {
                    label: 'label',
                    value: 'value',
                  },
                },
                formItemProps: {
                  name: 'refundWarehouseId',
                  rules: [{ required: true, message: '请选择退货仓库' }],
                },
              }}
            />
          );
        } else {
          return <TypographyV2.Text>{value || '-'}</TypographyV2.Text>;
        }
      },
    },
    {
      width: ColumnWidth.Action,
      title: '云仓推送状态',
      dataIndex: 'pushErpStatusCaption',
      key: 'pushErpStatusCaption',
    },
    {
      width: ColumnWidth.Remark,
      title: '原订单卖家备注',
      dataIndex: 'originalSaleRemark',
      key: 'originalSaleRemark',
    },
    {
      width: ColumnWidth.Remark,
      title: '原订单状态',
      dataIndex: 'originalSaleStatusCaption',
      key: 'originalSaleStatusCaption',
    },
    {
      width: ColumnWidth.Action,
      title: '原订单标签',
      dataIndex: 'originalSaleTag',
      key: 'originalSaleTag',
      render: (value, record) => (
        <OrderTag orderTag={record?.originalSaleTagList || []} />
      ),
    },
    {
      width: ColumnWidth.Remark,
      title: '原订单发货仓',
      dataIndex: 'originalSaleWarehouseName',
      key: 'originalSaleWarehouseName',
    },
    {
      width: ColumnWidth.Remark,
      title: '货主',
      dataIndex: 'ownerWarehouseName',
      key: 'ownerWarehouseName',
    },
    {
      width: ColumnWidth.Remark,
      title: '发货物流单号',
      dataIndex: 'originalSaleLogisticsNo',
      key: 'originalSaleLogisticsNo',
      sort: true,
    },
    {
      width: 'time',
      title: '下单时间',
      dataIndex: 'originalSaleCreateTime',
      key: 'originalSaleCreateTime',
      sort: true,
    },
    {
      width: 'time',
      title: '付款时间',
      dataIndex: 'originalSalePayTime',
      key: 'originalSalePayTime',
      sort: true,
    },
    {
      width: 'time',
      title: '发货时间',
      dataIndex: 'originalSaleDeliveryTime',
      key: 'originalSaleDeliveryTime',
      sort: true,
    },
    {
      width: 'time',
      title: '申请时间',
      dataIndex: 'platformApplyTime',
      key: 'platformApplyTime',
      sort: true,
    },
    {
      width: 'time',
      title: '确认收货时间',
      dataIndex: 'confirmReceiptTime',
      key: 'confirmReceiptTime',
      sort: true,
    },
    {
      width: 'time',
      title: '审核时间',
      dataIndex: 'confirmTime',
      key: 'confirmTime',
      sort: true,
    },
    {
      width: ColumnWidth.Remark,
      title: '创建人',
      dataIndex: 'createUserName',
      key: 'createUserName',
    },
    {
      width: ColumnWidth.Remark,
      title: '审核人',
      dataIndex: 'confirmUserName',
      key: 'confirmUserName',
    },
    {
      width: ColumnWidth.Remark,
      title: '确认收货人',
      dataIndex: 'confirmReceiptUserName',
      key: 'confirmReceiptUserName',
    },
    {
      width: ColumnWidth.Remark,
      title: '生成退货单',
      dataIndex: 'genBillStatusCaption',
      key: 'genBillStatusCaption',
    },
    {
      width: ColumnWidth.Remark,
      title: '销售退货单号',
      dataIndex: 'afterSaleOrderBillNo',
      key: 'afterSaleOrderBillNo',
      render: (v, record) => {
        return <TypographyV2.Text>{v ?? '-'}</TypographyV2.Text>;
      },
    },
    {
      width: ColumnWidth.Remark,
      title: '费用单号',
      dataIndex: 'expenseBillNo',
      key: 'expenseBillNo',
      render: (_, record) => {
        const { expenseBillNo: billNo, expenseBillId: billId } =
          record?.afterSaleOrderExtVO || {};
        return (
          <TypographyV2.Link
            onClick={() => {
              billId &&
                openBillDetailTabByBillType(billId, BillTypeEnum.费用单);
            }}
          >
            {billNo || '-'}
          </TypographyV2.Link>
        );
      },
    },
    {
      width: ColumnWidth.Remark,
      title: '其他收入单号',
      dataIndex: 'otherIncomeBillNo',
      key: 'otherIncomeBillNo',
      render: (_, record) => {
        const { otherIncomeBillNo: billNo, otherIncomeBillId: billId } =
          record?.afterSaleOrderExtVO || {};
        return (
          <TypographyV2.Link
            onClick={() => {
              billId &&
                openBillDetailTabByBillType(billId, BillTypeEnum.其他收入单);
            }}
          >
            {billNo || '-'}
          </TypographyV2.Link>
        );
      },
    },
    {
      width: ColumnWidth.Remark,
      title: '收款单号',
      dataIndex: 'receiveBillNo',
      key: 'receiveBillNo',
      render: (_, record) => {
        const { receiveBillNo: billNo, receiveBillId: billId } =
          record?.afterSaleOrderExtVO || {};
        const billNoArray = billNo?.split(',');
        const billIdArray = billId?.split(',');
        return (
          <Flex title={billNo}>
            {billNoArray?.map((item: string, index: number) => (
              <TypographyV2.Link
                key={item}
                onClick={() => {
                  billIdArray?.[index] &&
                    openBillDetailTabByBillType(
                      billIdArray[index],
                      BillTypeEnum.收款单,
                    );
                }}
              >
                {item} {index === billNoArray.length - 1 ? null : ','}
              </TypographyV2.Link>
            ))}
          </Flex>
        );
      },
    },
    {
      width: ColumnWidth.Remark,
      title: '付款单号',
      dataIndex: 'payBillNo',
      key: 'payBillNo',
      render: (_, record) => {
        const { payBillNo: billNo, payBillId: billId } =
          record?.afterSaleOrderExtVO || {};

        const billNoArray = billNo?.split(',');
        const billIdArray = billId?.split(',');
        return (
          <Flex title={billNo}>
            {billNoArray?.map((item: string, index: number) => (
              <TypographyV2.Link
                key={item}
                onClick={() => {
                  billIdArray?.[index] &&
                    openBillDetailTabByBillType(
                      billIdArray[index],
                      BillTypeEnum.付款单,
                    );
                }}
              >
                {item}
                {index === billNoArray.length - 1 ? null : '，'}
              </TypographyV2.Link>
            ))}
          </Flex>
        );
      },
    },
  ];
};

export const summaryData = [
  {
    key: 'refundRealAmount',
    needDecimals: 2,
  },
  {
    key: 'refundPostAmount',
    needDecimals: 2,
  },
];
