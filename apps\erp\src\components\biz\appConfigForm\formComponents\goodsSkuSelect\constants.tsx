import type { QueryFormListItem } from '@slt/biz-ui';
import {
  Flex,
  GoodsImage,
  Tag,
  Typography,
  type TableColumnType,
} from '@slt/base-ui';
import { type CommonRecord } from '@slt/base-utils';
import type { BasicGoodsPageSkuDTO, GoodsMaterialBO } from '@slt/services';
import { GoodsCombineType, PLACEHOLDER } from '@slt/utils';
import { isNil } from 'lodash-es';

const { Text } = Typography;

export const selectionModalSearchColumns: QueryFormListItem[] = [
  {
    type: 'input',
    formItemProps: {
      name: 'commonQuery',
    },
    fieldProps: {
      placeholder: '请输入商品名称/规格名称/ISBN查询',
    },
  },
];

export const selectionModalTableColumns: TableColumnType<BasicGoodsPageSkuDTO>[] =
  [
    {
      title: '主图',
      key: 'specPicUrlList',
      render: (mainPicList: GoodsMaterialBO[]) => {
        const src = mainPicList?.[0]?.url;
        return src ? <GoodsImage src={src} size="small" /> : null;
      },
    },
    { title: '商品编码', key: 'goodsNo' },
    {
      title: '商品名称',
      key: 'goodsName',
      render: (text: string, record: BasicGoodsPageSkuDTO) => {
        if (record.type !== GoodsCombineType.组合) {
          return <Text>{text}</Text>;
        }
        return (
          <Flex align="center">
            <Tag color="red">组合</Tag>
            <Text ellipsis title={text}>
              {text}
            </Text>
          </Flex>
        );
      },
    },
    {
      title: '规格编码',
      key: 'goodsSkuNo',
      render: (text: string, record: BasicGoodsPageSkuDTO) => {
        if (record.type !== GoodsCombineType.组合) {
          return <Text>{text}</Text>;
        }
        return <Text>{PLACEHOLDER}</Text>;
      },
    },
    {
      title: '规格名称',
      key: 'goodsSkuName',
      render: (text: string, record: BasicGoodsPageSkuDTO) => {
        if (record.type !== GoodsCombineType.组合) {
          return <Text>{text}</Text>;
        }
        return <Text>{PLACEHOLDER}</Text>;
      },
    },
    { title: 'ISBN', key: 'isbn' },
    { title: '图书名称', key: 'bookName' },
    { title: '定价', key: 'price' },
    { title: '出版社', key: 'publisherName' },
    { title: '作者', key: 'author' },
    { title: '商品类型', key: 'goodsTypeName' },
    { title: '创建人', key: 'createUserName' },
    { title: '创建时间', key: 'gmtCreate' },
    {
      key: 'enable',
      title: '状态',
      render: (text: number) => {
        if (isNil(text)) {
          return <Typography.Text>{PLACEHOLDER}</Typography.Text>;
        }

        const txt = Number(text) === 1 ? '启用' : '停用';
        return <Typography.Text title={txt}>{txt}</Typography.Text>;
      },
    },
  ];

export const selectTableColumns: TableColumnType<CommonRecord>[] = [
  {
    key: 'goodsName',
    title: '商品名称',
    render: (text: string, record) => {
      if (record.type !== GoodsCombineType.组合) {
        return <Text>{text}</Text>;
      }
      return (
        <Flex align="center">
          <Tag color="red">组合</Tag>
          <Text ellipsis title={text}>
            {text}
          </Text>
        </Flex>
      );
    },
  },
  {
    key: 'goodsNo',
    title: '商品编码',
  },
  {
    key: 'goodsSkuName',
    title: '规格名称',
    render: (text, record) => {
      if (record.type !== 2) {
        return <Text>{text}</Text>;
      }
      return <Text>{PLACEHOLDER}</Text>;
    },
  },
  {
    key: 'goodsSkuNo',
    title: '规格编码',
    render: (text, record) => {
      if (record.type !== 2) {
        return <Text>{text}</Text>;
      }
      return <Text>{PLACEHOLDER}</Text>;
    },
  },
  {
    key: 'enable',
    title: '状态',
    render: (text: number) => {
      if (isNil(text)) {
        return <Typography.Text>{PLACEHOLDER}</Typography.Text>;
      }

      const txt = Number(text) === 1 ? '启用' : '停用';
      return <Typography.Text title={txt}>{txt}</Typography.Text>;
    },
  },
];
