import { ModifyDatePickerChoose } from '@/components/biz';
import { BaseBillList } from '@/components/biz/baseBillList/BaseBillList';
import { useBillTypeConfig } from '@/components/biz/baseBillList/hooks/useBillTypeConfig';
import { businessTypeOptions } from '@/constants/billTypeAndBusinessTypeOptions';
import { routerMap } from '@/router/routerMap';
import {
  inputNumberProps,
  Typography,
  type TableColumnType,
} from '@slt/base-ui';
import { toNumber } from '@slt/base-utils';
import type { BizTableRef, QueryFormListItem } from '@slt/biz-ui';
import {
  stockExpenseAndOtherIncomeBillList,
  type FinanceBillDTO,
} from '@slt/services';
import { BillBusinessType, BillTypeEnum } from '@slt/utils';
import { useMemo, useRef } from 'react';
import { authIdsMap } from '@/constants/authIds';

/** 其他收入单 */
const OtherIncomeSheet = () => {
  const tableRef = useRef<BizTableRef<FinanceBillDTO>>(null);

  const {
    authIds,
    billDataTimeSearchColumn,
    labelValueSearchColumn,
    salesManIdListSearchColumn,
    billStatusSearchColumn,
    createTypeSearchColumn,
    customerIdListSearchColumn,
    toDetailPage,
    toAddPage,
  } = useBillTypeConfig({
    type: BillTypeEnum['其他收入单'],
  });

  const searchColumns = useMemo((): QueryFormListItem[] => {
    return [
      billDataTimeSearchColumn,
      billStatusSearchColumn,
      {
        type: 'select',
        formItemProps: { label: '业务类型', name: 'businessTypeList' },
        fieldProps: {
          placeholder: '请选择',
          mode: 'multiple',
          maxTagCount: 'responsive',
          options: businessTypeOptions['QTSR20000'],
        },
      },
      customerIdListSearchColumn,
      labelValueSearchColumn,
      salesManIdListSearchColumn,
      {
        key: 'settlementTime',
        type: 'rangePicker',
        formItemProps: { label: '还款到期时间', name: 'settlementTime' },
        fieldProps: {
          needMinDate: true,
          placeholder: '请选择还款到期时间',
          allowClear: true,
          startFormat: 'YYYY-MM-DD 00:00:00',
          endFormat: 'YYYY-MM-DD 23:59:59',
          fieldNames: {
            start: 'settlementTimeStart',
            end: 'settlementTimeEnd',
          },
        },
      },
      {
        type: 'rangeInputNumber',
        formItemProps: {
          label: '剩余还款天数',
          name: 'settlementDaysRemaining',
        },
        fieldProps: {
          fieldNames: {
            start: 'startSettlementDaysRemaining',
            end: 'endSettlementDaysRemaining',
          },
          ...inputNumberProps.int,
        },
      },
      createTypeSearchColumn,
    ];
  }, [
    billDataTimeSearchColumn,
    billStatusSearchColumn,
    labelValueSearchColumn,
    salesManIdListSearchColumn,
    createTypeSearchColumn,
    customerIdListSearchColumn,
  ]);

  const columns = useMemo((): TableColumnType<FinanceBillDTO>[] => {
    return [
      {
        width: 'time',
        key: 'billDateTime',
        title: '单据日期',
      },
      {
        width: 'billNo',
        key: 'billNo',
        title: '单据编号',
        render: (billNo: string, record: FinanceBillDTO) => {
          return (
            <Typography.Link
              authId={{
                authId: authIds?.detail ?? '',
                authType: 'text',
              }}
              onClick={() => toDetailPage(record.id || '')}
            >
              {billNo}
            </Typography.Link>
          );
        },
      },
      {
        width: 'remark',
        title: '业务类型',
        key: 'businessTypeName',
      },
      {
        width: 'remark',
        title: '单据状态',
        key: 'billStatusText',
      },
      {
        width: 'time',
        title: '还款到期时间',
        key: 'settlementTime',
        render: (value: string, record) => {
          const { businessType } = record;
          if (businessType !== BillBusinessType['其他收入单现金收入']) {
            return (
              <ModifyDatePickerChoose
                tableRef={tableRef}
                record={record}
                value={value}
                authId={authIdsMap.otherIncomeSheetList.edit}
                updateFunc={stockExpenseAndOtherIncomeBillList}
              />
            );
          }
        },
      },
      {
        width: 'time',
        title: '剩余还款天数',
        key: 'settlementDaysRemaining',
      },
      {
        width: 'remark',
        title: '结算单位',
        key: 'customerName',
      },
      {
        width: 'remark',
        title: '业务员',
        key: 'salesManName',
      },
      {
        width: 'remark',
        title: '部门',
        key: 'departmentName',
      },
      {
        title: '单据备注',
        key: 'remark',
        width: 280,
      },
      {
        width: 'remark',
        title: '单据来源',
        key: 'createTypeDesc',
      },
      {
        width: 'remark',
        title: '金额',
        key: 'detailTotalAmount',
        render: (totalAmount: string) => {
          return totalAmount;
        },
      },
      {
        width: 'remark',
        title: '已收款金额',
        key: 'totalAmount',
        render: (allocatedAmount: string) => {
          return allocatedAmount ?? '0.00';
        },
      },
      {
        width: 'remark',
        title: '待收款金额',
        key: 'waitAllocatedAmount',
        render: (_: string, record: FinanceBillDTO) => {
          return (
            (toNumber(record.detailTotalAmount) ?? 0) -
            (toNumber(record.totalAmount) ?? 0)
          ).toFixed(2);
        },
      },
      {
        width: 'remark',
        title: '收款帐户',
        key: 'settleAccountName',
      },
      {
        width: 'remark',
        title: '结算方式',
        key: 'settlementTypeName',
      },
    ];
  }, [authIds?.detail, toDetailPage]);

  return (
    <BaseBillList
      operateModule={routerMap.otherIncomeSheetList.name}
      type={BillTypeEnum['其他收入单']}
      tableColumns={columns}
      searchColumns={searchColumns}
      authIdObj={authIds}
      toDetailPage={toDetailPage}
      toAddPage={toAddPage}
      tableRef={tableRef}
    />
  );
};

export default OtherIncomeSheet;
