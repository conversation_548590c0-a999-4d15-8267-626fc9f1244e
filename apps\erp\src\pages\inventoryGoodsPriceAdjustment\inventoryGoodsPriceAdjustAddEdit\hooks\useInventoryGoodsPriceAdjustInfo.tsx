import { useCallback, useEffect, useRef, useState } from 'react';

import {
  Button,
  ExclamationCircleFilled,
  Form,
  Modal,
  msgSuccess,
  type EditableTableRef,
} from '@slt/base-ui';
import { formatDateAndTime, type CommonRecord } from '@slt/base-utils';
import {
  adjustPriceBillBillNoRepeated,
  adjustPriceBillCheckCreateOrUpdate,
  adjustPriceBillCreateUpdate,
  adjustPriceBillDetail,
  basicBillNoGeneratorGen,
  inventoryInfoQuerySkuWarehouseCostAmount,
  type BasicGoodsPageSkuDTO,
  type LossSpillBillCreateUpdateParams,
} from '@slt/services';

import type { SaveFailedReasonModalRef } from '@/components/biz/receipt/saveFailedReasonModal/types';
import {
  useCalculationPrice,
  useCheckGoodsMaxNum,
  useCreateBillNo,
  useQueryDepartmentBySalesManId,
  useTableExpandSubList,
} from '@/hooks/bizHooks';

import type { NewResultGoodsDTO } from '@/components/biz/receipt/types';
import { useOpenTab } from '@/hooks/useOpenTab';
import { routerMap } from '@/router/routerMap';
import { useActions } from '@/store/system';
import { useDepartments, useEditableTableValidatorRules } from '@slt/biz-ui';
import { useMutation } from '@slt/net-query';
import { BillBusinessType, BillTypeEnum } from '@slt/utils';
import dayjs from 'dayjs';
import { isNil } from 'lodash-es';
import {
  CostAmountSetType,
  OrderStatus,
  type FlatList,
  type GoodsPriceAdjustInfoForm,
  type InventoryGoodsPriceAdjustInfoProps,
  type LossSpillBillDetailListDTO,
} from '../../type';
import {
  formatLabelInValueEchoParams,
  formatLabelInValueSaveParams,
} from '@/utils/bill';
import { maxBillDetailRows } from '@/constants/bill';

export const useInventoryReportLossInfo = (
  props: InventoryGoodsPriceAdjustInfoProps,
) => {
  const { id, type } = props;
  /**商品明细数据 */
  const [dataSource, setDataSource] = useState<LossSpillBillDetailListDTO[]>(
    [],
  );
  /**单个单据详情 */
  const [detailInfo, setDetailInfo] =
    useState<Partial<LossSpillBillCreateUpdateParams>>();
  const [detailLoading, setDetailLoading] = useState(true);

  const saveFailedReasonModalRef = useRef<SaveFailedReasonModalRef>(null);
  const billNoRepeatRef = useRef<string | null>(null);
  const editableTableRef = useRef<EditableTableRef>(null);
  const [form] = Form.useForm<GoodsPriceAdjustInfoForm>();

  const { closeAndRefreshPrevTab, closeActiveTab } = useActions();
  const { openTab } = useOpenTab();
  const { initReceiptNumber } = useCreateBillNo({
    request: basicBillNoGeneratorGen,
    needCreate: !id, //新增时需要自动生成
    businessType: BillBusinessType.库存调价单库存调价,
    form,
    billNoFormItemName: 'billNo',
  });

  const { checkBeforeClick, checkAfterSelected } = useCheckGoodsMaxNum({
    selectedNum: dataSource?.length,
    maxNum: maxBillDetailRows,
  });

  /**业务员改变后查询对应部门信息 */
  useQueryDepartmentBySalesManId({
    form,
  });
  /**部门信息 */
  const { departmentOptions } = useDepartments();

  /**仅预览 */
  const isDetail = type === 'detail';

  /**新增/编辑保存前校验 */
  const { mutate: saveBeforeCheck } = useMutation(
    adjustPriceBillCheckCreateOrUpdate,
  );

  /**需要发送请求的部分 */
  const { mutate: checkBillNoRepeated } = useMutation(
    adjustPriceBillBillNoRepeated,
  );

  const { mutate: queryPurchaseBillDetail } = useMutation(
    adjustPriceBillDetail,
  );
  const { mutate: addPurchaseBill } = useMutation(adjustPriceBillCreateUpdate);
  const { mutate: editPurchaseBill } = useMutation(adjustPriceBillCreateUpdate);
  const { mutate: getInventoryInfoQuerySkuCostAmount } = useMutation(
    inventoryInfoQuerySkuWarehouseCostAmount,
  );

  const { calcImportGoodsPrice } = useCalculationPrice({
    parentFieldName: '',
    billType: BillTypeEnum.库存调价单,
  });

  const { tableRowClassName, onFlatList, onRestoreList } =
    useTableExpandSubList<LossSpillBillDetailListDTO>({
      rowKey: 'uid',
      dataSource,
      setDataSource,
    });

  const { rules } = useEditableTableValidatorRules({
    emptyMsg: '请至少添加一个商品',
    editableTableRef,
  });

  /**赋值表单和state */
  const setGoodsList = useCallback(
    (list: LossSpillBillDetailListDTO[]) => {
      form.setFieldsValue({
        goodsList: list,
      });
      setDataSource(list);
    },
    [form, setDataSource],
  );

  /**保存校验不通过-生成新的单据编号 */
  const onCreateReceiptNo = useCallback(async () => {
    await initReceiptNumber(BillBusinessType.库存调价单库存调价);
  }, [initReceiptNumber]);

  /**单据详情 */
  const getInventoryCheckQueryDetail = useCallback(
    async (id: string) => {
      const res = await queryPurchaseBillDetail({ id });
      if (res.success && res.data) {
        const {
          adjustPriceBillDetailVOList = [],
          billDateTime,
          billNo,
          billStatus,
          remark,
          wholeDepartmentId,
          ...rest
        } = res.data;

        const goodsList = onFlatList(
          adjustPriceBillDetailVOList,
        ) as LossSpillBillDetailListDTO[];
        const values: CommonRecord = {
          billNo,
          billStatus,
          remark,
          departmentId: wholeDepartmentId,
          billDateTime: billDateTime ? dayjs(billDateTime) : undefined,
          goodsList: adjustPriceBillDetailVOList,
          ...rest,
          ...formatLabelInValueEchoParams(res.data),
        };
        setGoodsList(goodsList);
        setDetailInfo(values);
      }

      setDetailLoading(false);
    },
    [onFlatList, setGoodsList, setDetailInfo, queryPurchaseBillDetail],
  );

  useEffect(() => {
    if (id) {
      getInventoryCheckQueryDetail(id);
    }
  }, [getInventoryCheckQueryDetail, id]);

  /**改变行数据 */
  const onRowDataChange = useCallback(
    (
      row?: LossSpillBillDetailListDTO,
      list?: LossSpillBillDetailListDTO[],
      changed?: CommonRecord,
    ) => {
      // 检查 list 是否为有效数组，row 和 changed 是否有效
      if (!row || !Array.isArray(list) || !changed) {
        return;
      }

      if (Object.prototype.hasOwnProperty.call(changed, 'adjustCostAmount')) {
        const { adjustCostAmount } = changed;
        if (isNil(adjustCostAmount)) {
          row.costAmountSetType = CostAmountSetType['自动设置'];
          row.totalAmount = null;
        } else {
          row.costAmountSetType = CostAmountSetType['手动设置'];
        }
      }

      setGoodsList([...list]);
    },
    [setGoodsList],
  );

  /**
   * @description: 把所有的陈本单价和成本金额设置为null
   * @return {*}
   */
  const setGoodsListCostAmountTotalAmountNull = useCallback(
    (list: LossSpillBillDetailListDTO[], isAddGoods: boolean) => {
      const goodsDataInfo = dataSource.map((item) => {
        return {
          ...item,
          originalCostAmount: null,
        };
      });
      if (isAddGoods) {
        setGoodsList([...goodsDataInfo, ...list]);
      } else {
        setGoodsList(goodsDataInfo);
      }
    },
    [dataSource, setGoodsList],
  );

  /**
   * @description: 给选择商品查询改仓库下的成本单价，并且就算成本金额（数量*单价）
   * 1、获取选择的商品信息（skuIdList）
   * 2、获取单据时间和仓库ID、根据skuIdList查询成本单价
   * 3、查询到的数据建立Map集合、查找并且修改数据
   * @param {LossSpillBillDetailListDTO[]} list 选择的商品
   * @param {boolean} isAddGoods 是否是添加商品
   * @return {LossSpillBillDetailListDTO[]} goodsDataInfo
   */
  const setGoodsListCostAmountTotalAmountNumber = useCallback(
    async (list: LossSpillBillDetailListDTO[], isAddGoods: boolean) => {
      if (list.length) {
        const skuIdList = list
          .map((v) => v.goodsSkuId)
          .filter((skuId) => skuId !== undefined); // 过滤掉 undefined

        const { billDateTime, warehouseId: warehouseIdLabelInValue } =
          form.getFieldsValue();
        const warehouseId = warehouseIdLabelInValue?.value;

        //单据时间和仓库必选
        if (isNil(billDateTime) || isNil(warehouseId)) {
          if (isNil(warehouseId)) {
            setGoodsListCostAmountTotalAmountNull(list, isAddGoods);
          }
          return;
        }

        const params = {
          skuIdList,
          warehouseId,
          billDateTime: formatDateAndTime(billDateTime),
        };
        const res = await getInventoryInfoQuerySkuCostAmount(params);
        if (res?.success && res.data) {
          const costAmountList = res.data;
          // 建立map
          const costAmountMap = new Map(
            costAmountList.map((item) => [item.skuId, item.costAmount]),
          );

          const goodsDataInfo = list.map((item) => {
            const costAmount = costAmountMap.get(item.goodsSkuId) ?? null;
            const originalCostAmount = costAmount ?? null;

            return {
              ...item,
              originalCostAmount,
              adjustCostAmount: originalCostAmount,
              costAmountSetType: CostAmountSetType['自动设置'],
            } as LossSpillBillDetailListDTO;
          });
          if (isAddGoods) {
            setGoodsList([...dataSource, ...goodsDataInfo]);
          } else {
            setGoodsList(goodsDataInfo);
          }

          return goodsDataInfo;
        }
      }
    },
    [
      form,
      dataSource,
      setGoodsList,
      getInventoryInfoQuerySkuCostAmount,
      setGoodsListCostAmountTotalAmountNull,
    ],
  );

  /**
   * @description: 过滤重复商品(根据商品的规格编码+商品编码)
   * @param {BasicGoodsPageSkuDTO[]} list 选择的商品
   * @param {InventoryCheckGoodsDetailDTO[]} dataSource 已选商品
   * @return {BasicGoodsPageSkuDTO[]} 过滤后的商品列表
   */
  const filterListBySkuId = useCallback(
    (
      list: BasicGoodsPageSkuDTO[],
      dataSource: LossSpillBillDetailListDTO[],
    ) => {
      const existingKeys = new Set(
        dataSource.map((item) => (item.goodsSpuId ?? '') + item.goodsSkuId),
      );

      const filterListBySkuId = list.filter((v) => {
        if (v?.goodsId && v?.goodsSkuNo) {
          const key = v.goodsId + v.id;
          return !existingKeys.has(key);
        }
        return false;
      });

      const filterListBySkuIdIncludeDataSource = list.filter((v) => {
        if (v?.goodsId && v?.goodsSkuNo) {
          const key = v.goodsId + v.id;
          return existingKeys.has(key);
        }
        return false;
      });

      if (filterListBySkuIdIncludeDataSource.length) {
        msgSuccess('已过滤重复商品');
      }

      return filterListBySkuId.length ? filterListBySkuId : [];
    },
    [],
  );

  /**自选商品选择商品 */
  const onSelectedGoods = useCallback(
    async (list: BasicGoodsPageSkuDTO[]) => {
      //选过的商品需要过滤
      const filterListBySku = filterListBySkuId(list, dataSource);
      if (filterListBySku.length === 0) {
        return;
      }

      const flatList = onFlatList(filterListBySku) as FlatList[];
      const newList = flatList?.map((item) => {
        return {
          rowId: item.rowIndex,
          uid: item.uid,
          goodsSpuId: item.goodsId, //商品id
          goodsSkuId: item.id, //规格id
          goodsName: item.goodsName, //商品名称
          goodsCode: item.goodsNo, //商品编码
          skuName: item.goodsSkuName, //规格名称
          skuCode: item.goodsSkuNo, //规格编码
          adjustCostAmount: item.costPrice || null, //调整后单价
          originalCostAmount: null, //调整前单价
          remark: item.remark || null, //备注
          costAmountSetType: CostAmountSetType['自动设置'],
        };
      }) as unknown as LossSpillBillDetailListDTO[];

      /**校验添加商品后判断单据是否会超过最大限制  */
      if (!checkAfterSelected(newList?.length)) {
        return;
      }

      return await setGoodsListCostAmountTotalAmountNumber(newList, true);
    },
    [
      dataSource,
      filterListBySkuId,
      onFlatList,
      setGoodsListCostAmountTotalAmountNumber,
    ],
  );

  /**监听form表单值变化 */
  const onValuesChange = useCallback(
    async (values: any) => {
      //单据编号改变
      if ('billNo' in values) {
        billNoRepeatRef.current = null;
      }
      //单据日期变化
      if ('billDateTime' in values) {
        await setGoodsListCostAmountTotalAmountNumber(dataSource, false);
      }
      // 仓库发生变化
      if ('warehouseId' in values) {
        await setGoodsListCostAmountTotalAmountNumber(dataSource, false);
      }
    },
    [dataSource, setGoodsListCostAmountTotalAmountNumber],
  );

  /**删除商品 */
  const handleDelete = useCallback(
    (row: LossSpillBillDetailListDTO) => {
      const { uid } = row;
      const newList = dataSource?.filter((item) => item.uid !== uid);

      setGoodsList(newList);
    },
    [dataSource, setGoodsList],
  );

  /**批量修改数量 */
  const handleBatchModify = useCallback(
    (name: string, value?: number) => {
      if (dataSource?.length && !isNil(value)) {
        const updateDataSource = dataSource.map((v) => ({
          ...v,
          [name]: value,
          costAmountSetType: CostAmountSetType['手动设置'],
        }));

        setGoodsList(updateDataSource);
      }
    },
    [dataSource, setGoodsList],
  );

  const getBillStatus = useCallback(
    (status: OrderStatus | undefined, isDraft: boolean) => {
      if (status === OrderStatus['已取消']) {
        return OrderStatus['已取消'];
      } else {
        return isDraft ? 1 : 2;
      }
    },
    [],
  );

  /** 校验表单 */
  const handleFormatValues = useCallback(
    async (isDraft: boolean) => {
      try {
        // 不是草稿，需要校验
        if (!isDraft) {
          await form.validateFields();
          const isPass = await editableTableRef.current?.validateFields();
          if (!isPass) {
            return;
          }
        }

        // 获取表单的值
        const values = form.getFieldsValue();

        if (values) {
          const {
            departmentId,
            billDateTime,
            goodsList = [],
            billStatus,
            ...rest
          } = values;

          const adjustPriceBillDetailList = onRestoreList(goodsList, 'rowId');

          const departmentIdValue = departmentId?.length
            ? departmentId[departmentId?.length - 1]
            : undefined;

          const params = {
            ...rest,
            adjustPriceBillDetailList,
            billStatus: getBillStatus(billStatus, isDraft), // 草稿箱=1，直接保存=2,取消=3
            businessType: BillBusinessType.库存调价单库存调价,
            departmentId: departmentIdValue,
            billDateTime: billDateTime
              ? formatDateAndTime(billDateTime)
              : undefined,

            ...formatLabelInValueSaveParams(values),
          };

          const filteredParams = Object.fromEntries(
            Object.entries(params).filter(([_, v]) => v !== undefined),
          );

          return filteredParams;
        }
      } catch (error) {
        return null;
      }
    },
    [form, onRestoreList, getBillStatus],
  );

  /**单据取消 */
  const handleReceiptCancel = useCallback(() => {
    closeActiveTab();
  }, [closeActiveTab]);

  /**保存 */
  const handleFinish = useCallback(
    async (params: any) => {
      const api = id ? editPurchaseBill : addPurchaseBill;

      const res = await api(params);
      if (res.success) {
        msgSuccess(`${id ? '编辑' : '新增'}成功`);
        const receiptId = id || res.data;
        if (receiptId) {
          closeAndRefreshPrevTab();
          openTab(routerMap.inventoryGoodsPriceAdjustmentDetail.route, {
            search: { id: receiptId, ignoreAuth: true },
          });
        }
      }
    },
    [addPurchaseBill, editPurchaseBill, closeAndRefreshPrevTab, id, openTab],
  );

  /**前置条件校验都通过后再去校验单据编号*/
  const handleCheckBillNoRepeat = useCallback(
    async (values: CommonRecord) => {
      /**业务类型 */
      const businessType = BillBusinessType.库存调价单库存调价;
      /**单据编号 */
      const { billNo } = form.getFieldsValue();
      if (businessType && billNo) {
        const params = {
          id,
          businessType,
          billNo,
          billType: BillTypeEnum.库存调价单,
        };
        const res = await checkBillNoRepeated(params);
        if (res.success && res.data) {
          const { continues, continuesResultList } = res.data;
          if (continues && !continuesResultList?.length) {
            //校验通过直接保存（弹窗来表示云仓）
            handleFinish(values);
          } else {
            //展示校验不通过信息
            if (saveFailedReasonModalRef?.current) {
              saveFailedReasonModalRef?.current?.showModal(res.data);
            }
          }
        }
      }
    },
    [form, id, checkBillNoRepeated, handleFinish],
  );

  const handleCheck = useCallback(
    async (params: any) => {
      const res = await saveBeforeCheck({ ...params });

      if (res.success && res.data) {
        const { continues, continuesResultList } = res.data;

        if (continues && !continuesResultList?.length) {
          /**继续校验单据编号是否重复 */
          handleCheckBillNoRepeat(params);
        } else {
          //展示校验不通过信息
          if (saveFailedReasonModalRef?.current) {
            saveFailedReasonModalRef?.current?.showModal(res.data);
          }
        }
      }
    },
    [handleCheckBillNoRepeat, saveBeforeCheck],
  );

  /**单据保存 */
  const handleReceiptSave = useCallback(
    async (isDraft = false) => {
      const values = await handleFormatValues(isDraft);

      if (values) {
        if (isDraft) {
          // 直接保存为草稿状态
          handleFinish(values);
        } else {
          //保存前的逻辑处理
          Modal.info({
            icon: <ExclamationCircleFilled />,

            content: (
              <div className="flex flex-col gap-xxs">
                <span>
                  请确认单据中的商品是否需要成本重算,需重算请手工核算成本后再保存单据,否则将导致调整后单价不准确。
                </span>
                <span className="text-yellow-7">
                  需要成本重算的场景:今日修改期初库存数量及成本/插入历史单据/修改历史单据/删除历史单据
                </span>
              </div>
            ),
            width: 700,
            onOk() {
              handleCheck(values);
            },
            footer: (
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'flex-end',
                  marginTop: '20px',
                  gap: '10px',
                }}
              >
                <Button onClick={() => Modal.destroyAll()}>取消</Button>
                <Button
                  color="primary"
                  variant="outlined"
                  onClick={() => {
                    Modal.destroyAll();
                    openTab(routerMap.manualRecalculateCost.route);
                  }}
                >
                  去手工核算成本
                </Button>
                <Button
                  type="primary"
                  onClick={() => {
                    Modal.destroyAll();
                    handleCheck(values);
                  }}
                >
                  保存
                </Button>
              </div>
            ),
          });
        }
      }
    },
    [handleFormatValues, handleFinish, handleCheck, openTab],
  );

  /**继续保存 */
  const handleSaveContinue = useCallback(
    async (list?: NewResultGoodsDTO[]) => {
      const values: any = await handleFormatValues(false);
      if (list?.length && values.transferBillDetailList) {
        // 将填入的固定成本设置进商品明细里面
        list?.forEach((item) => {
          // 行号转换成索引
          const handWriteCostPrice = item.handWriteCostPrice;
          const rowIndex = +item.rowNo - 1;

          if (!isNil(handWriteCostPrice)) {
            values.transferBillDetailList[rowIndex].costAmount =
              handWriteCostPrice;
            values.transferBillDetailList[rowIndex].totalCostAmount = (
              handWriteCostPrice *
              values.transferBillDetailList[rowIndex].adjustCostAmount
            ).toFixed(2);
            values.transferBillDetailList[rowIndex].handWriteCostPrice =
              item.handWriteCostPrice;
          }
        });
        setGoodsList(values.transferBillDetailList);
      }

      handleCheckBillNoRepeat(values);
    },
    [setGoodsList, handleFormatValues, handleCheckBillNoRepeat],
  );

  /**单据新增 */
  const handleReceiptAdd = useCallback(() => {
    closeAndRefreshPrevTab();
    openTab(routerMap.inventoryGoodsPriceAdjustmentAdd.route);
  }, [closeAndRefreshPrevTab, openTab]);

  /**保存校验不通过-手动修改单据编号 */
  const onEditReceiptNo = useCallback(() => {
    const msg = '请重新填写单据编号';
    form.setFields([
      {
        name: 'billNo',
        errors: [msg],
      },
    ]);
    billNoRepeatRef.current = msg;
  }, [form]);

  const handleReplaceGoodsList = useCallback(
    (
      originList: LossSpillBillDetailListDTO[],
      newList: LossSpillBillDetailListDTO[],
    ) => {
      //保证原始列表数据顺序
      const result = originList.map((itemA) => {
        const itemB = newList.find((itemB) => itemB.uid === itemA.uid);
        return itemB ? itemB : itemA;
      });
      setGoodsList(result);
    },
    [setGoodsList],
  );

  const getImportGoodsList = useCallback(
    async (list: BasicGoodsPageSkuDTO[]) => {
      const selectedList = await onSelectedGoods(list);
      if (selectedList?.length) {
        const newList = calcImportGoodsPrice(selectedList);

        handleReplaceGoodsList(selectedList, newList);
      }
    },
    [calcImportGoodsPrice, handleReplaceGoodsList, onSelectedGoods],
  );

  return {
    ...props,
    isDetail,
    form,
    dataSource,
    editableTableRef,
    billNoRepeatRef,
    saveFailedReasonModalRef,
    departmentOptions,
    detailLoading,
    detailInfo,
    rules,
    tableRowClassName,
    handleReceiptCancel,
    handleReceiptSave,
    handleReceiptAdd,
    handleBatchModify,
    handleDelete,
    onSelectedGoods,
    onRowDataChange,
    onValuesChange,
    onCreateReceiptNo,
    handleSaveContinue,
    onEditReceiptNo,
    getInventoryCheckQueryDetail,
    getImportGoodsList,
    checkBeforeClick,
  };
};
