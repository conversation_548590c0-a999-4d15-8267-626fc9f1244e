/***
 * @name 售后附件/备注、供分销附件/备注
 * @props AfterSaleViewModeEnum
 */
import { memo, useMemo, useState } from 'react';
import type { RemarkAndAttachmentProps } from './type';
import { AfterSaleViewModeEnum } from '@slt/services';
import {
  Flex,
  UploadOutlined,
  Typography,
  EditOutlined,
  msgWarn,
} from '@slt/base-ui';
import { UploadRemarkAttachmentModal } from './uploadRemarkAttachmentModal';
import type { AfterSaleAttachmentDetailItem } from './type';
import { omsCheckCanAddSupplierDistributionAttachment } from '@slt/services';

import { useMutation } from '@slt/net-query';
export const AfterSaleRemarkAttachment = memo(
  (props: RemarkAndAttachmentProps) => {
    const {
      viewMode = AfterSaleViewModeEnum.我的视角,
      value,
      reload,
      hasAuth = true,
    } = props;
    const [open, setOpen] = useState(false);
    const isMine = viewMode === AfterSaleViewModeEnum.我的视角;
    const { mutate: checkCanAddSupplierRemark } = useMutation(
      omsCheckCanAddSupplierDistributionAttachment,
    );
    const myItem = value?.afterSaleAttachment;
    const supplierItems = value?.supplierDistributionAttachment || [];
    const remarkText: string | undefined = useMemo(
      () =>
        isMine ? myItem?.remark : supplierItems[0]?.remark || myItem?.remark,
      [isMine, myItem, supplierItems],
    );

    const attachmentsCount: number = useMemo(() => {
      if (isMine) {
        return myItem?.attachmentUrlList?.length || 0;
      }
      if (supplierItems?.length) {
        return supplierItems.reduce(
          (sum: number, item: AfterSaleAttachmentDetailItem) =>
            sum + (item?.attachmentUrlList?.length || 0),
          0,
        );
      } else {
        return myItem?.attachmentUrlList?.length || 0;
      }
    }, [isMine, myItem, supplierItems]);

    const hasRemarkOrAttachment = !!remarkText || attachmentsCount > 0;

    return (
      <>
        <div
          onClick={async (e) => {
            e.stopPropagation();
            const res = await checkCanAddSupplierRemark({
              afterSaleOrderIdList: props.afterSaleOrderId
                ? [props.afterSaleOrderId]
                : undefined,
              saleOrderId: props?.saleOrderId,
            });
            if (res.success) {
              if (!res.data) {
                msgWarn('当前售后单，无关联的线上分销商或供应商，无需录入！');
                return;
              }

              setOpen(res.data ?? false);
            }
          }}
          className="w-[90%] cursor-pointer truncate"
        >
          <Flex gap={4} align="center">
            <div className="w-[80%]">
              {hasRemarkOrAttachment ? (
                <Flex vertical>
                  {remarkText ? (
                    <Typography.Text ellipsis title={remarkText}>
                      {remarkText || '-'}
                    </Typography.Text>
                  ) : null}
                  <Typography.Text className="text-sm">
                    附件：{attachmentsCount} 个
                  </Typography.Text>
                </Flex>
              ) : (
                <UploadOutlined className="text-primary" />
              )}
            </div>
            {hasAuth && hasRemarkOrAttachment ? (
              <EditOutlined className="size-base text-primary" />
            ) : null}
          </Flex>
        </div>
        {open ? (
          <UploadRemarkAttachmentModal
            open={open}
            {...props}
            handleClose={(reloadFlag) => {
              if (reloadFlag) {
                reload?.();
              }

              setOpen(false);
            }}
          />
        ) : null}
      </>
    );
  },
);
