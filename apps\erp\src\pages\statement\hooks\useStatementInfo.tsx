import { useSearchParams } from 'react-router-dom';
import { BillInfo } from '../components/statementInfo/billInfo/BillInfo';
import {
  StatementInfoTypeEnum,
  StatementTypeEnum,
  type StatementInfoProps,
} from '../types';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { GoodInfo } from '../components/statementInfo/goodInfo/GoodInfo';
import { OtherInfo } from '../components/statementInfo/OtherInfo';
import { OperateLogTabItemContent, type BizTableRef } from '@slt/biz-ui';
import { routerMap } from '@/router/routerMap';
import { StatementBillStatusType, SystemLogType } from '@slt/utils';
import { Flex, msgWarn, Typography } from '@slt/base-ui';
import { useTabRefresh } from '@/hooks/useTabRefresh';
import {
  anonStatementCheckRemove,
  anonStatementGoodReceivableSum,
  anonStatementQueryById,
  anonStatementQuerySynergyDiff,
  statementCheckRemove,
  statementGoodReceivableSum,
  statementQueryById,
  statementQuerySynergyDiff,
  type GoodsReceivableStatisticsVO,
  type StatementDetailBillInfoVO,
  type StatementQueryDiffVO,
} from '@slt/services';
import { useMutation } from '@slt/net-query';
import {
  useActions,
  useActiveTab,
  useGlobalTabConfig,
  useTabs,
} from '@/store/system';
import type { CommonRecord } from '@slt/base-utils';

export const useStatementInfo = (options: StatementInfoProps) => {
  const { infoType, type } = options;
  const tabs = useTabs();
  const [searchParams] = useSearchParams();
  const billId = searchParams.get('statementId') || '';
  const tabKey = searchParams.get('tabKey') || 'billInfo';
  const params = searchParams.get('params');
  const refreshTableRef = useRef<React.RefObject<
    BizTableRef<CommonRecord>
  > | null>(null);
  const [billInfoData, setBillInfoData] = useState<StatementDetailBillInfoVO>();
  const [diffData, setDiffData] = useState<StatementQueryDiffVO>();
  const [goodsHeaderStatisticsData, setGoodsHeaderStatisticsData] =
    useState<GoodsReceivableStatisticsVO>();
  const goodsHeaderStatisticsApi =
    infoType === StatementInfoTypeEnum.分享
      ? anonStatementGoodReceivableSum
      : statementGoodReceivableSum;

  const { mutate: mutateGoodsHeaderStatistics } = useMutation(
    goodsHeaderStatisticsApi,
  );
  const { mutate: mutateStatementQueryById, isMutating: isLoading } =
    useMutation(
      infoType === StatementInfoTypeEnum.分享
        ? anonStatementQueryById
        : statementQueryById,
    );
  const { mutate: mutateStatementQuerySynergyDiff } = useMutation(
    infoType === StatementInfoTypeEnum.分享
      ? anonStatementQuerySynergyDiff
      : statementQuerySynergyDiff,
  );
  // 获取单据是否被删除
  const { mutate: mutateStatementCheckRemove } = useMutation(
    infoType === StatementInfoTypeEnum.分享
      ? anonStatementCheckRemove
      : statementCheckRemove,
  );
  const { closeActiveTab, updateGlobalTabAttrById } = useActions();

  const handleRefreshTable = () => {
    const detailTabs = tabs.filter(
      (tab) =>
        tab.route === routerMap.clientStatement.route ||
        tab.route === routerMap.supplierStatement.route,
    );
    detailTabs.map((tab) => {
      updateGlobalTabAttrById(tab?.id, {
        refresh: true,
      });
    });
  };

  const handleCheckRemove = async () => {
    const res = await mutateStatementCheckRemove(billId);
    if (res.success && res.data) {
      if (infoType !== StatementInfoTypeEnum.分享) {
        msgWarn('该账单已被删除');
        void closeActiveTab();
        handleRefreshTable();
      } else {
        msgWarn('该单据已被删除，不支持操作');
      }
      return true;
    }
    return false;
  };

  const getGoodsHeaderStatistics = useCallback(() => {
    billId &&
      void mutateGoodsHeaderStatistics({ statementId: billId }).then((res) => {
        if (res.success && res.data) {
          setGoodsHeaderStatisticsData(res.data);
        }
      });
  }, [mutateGoodsHeaderStatistics, billId]);

  const [activeKey, setActiveKey] = useState(tabKey);
  const currentTab = useGlobalTabConfig();
  const activeTab = useActiveTab();

  const getBillInfoData = useCallback(async () => {
    if (billId && currentTab?.id === activeTab?.id) {
      const res = await mutateStatementQueryById(billId);
      if (!res.success || !res.data) {
        return;
      }

      setBillInfoData(res.data);

      if (res.data.statementStatus !== StatementBillStatusType.待核实) {
        return;
      }

      const diffRes = await mutateStatementQuerySynergyDiff({
        statementId: billId,
      });
      setDiffData(diffRes.data);
    }
  }, [
    billId,
    mutateStatementQueryById,
    currentTab?.id,
    activeTab?.id,
    mutateStatementQuerySynergyDiff,
  ]);

  const containReceiverInfo: number = useMemo(() => {
    if (!params) {
      return 0;
    }

    const searchParams = JSON.parse(decodeURIComponent(params)) as {
      containReceiverInfo: number;
    };
    return searchParams?.containReceiverInfo ?? 0;
  }, [params]);

  const tabItems = useMemo(() => {
    const items = [
      {
        label: '账单信息',
        children: <BillInfo type={type} isLoading={isLoading} />,
        key: 'billInfo',
      },
      {
        label: type === StatementTypeEnum.客户 ? '商品应收' : '商品应付',
        children: <GoodInfo type={type} infoType={infoType} />,
        key: 'goods',
      },
      {
        label:
          type === StatementTypeEnum.客户 ? '其他应收及调账' : '其他应付及调账',
        children: <OtherInfo type={type} infoType={infoType} />,
        key: 'other',
      },
      {
        label: '操作日志',
        children: (
          <Flex className="h-full w-full px-sm pt-xs" vertical>
            <Typography.Title level={4}>操作日志</Typography.Title>
            <OperateLogTabItemContent
              operateModule={
                type === StatementTypeEnum.客户
                  ? routerMap.clientStatement.name
                  : routerMap.supplierStatement.name
              }
              searchSource={SystemLogType.ERP}
              recordId={billId}
            />
          </Flex>
        ),
        key: 'log',
      },
    ];
    if (infoType === StatementInfoTypeEnum.详情) {
      return items;
    }
    return items.filter((item) => item.key !== 'log');
  }, [infoType, type, billId, isLoading]);

  const handleTabChange = (key: string) => {
    setActiveKey(key);
  };

  useTabRefresh({
    refreshFunc: () => {
      void getBillInfoData();
    },
  });

  useEffect(() => {
    void getBillInfoData();
  }, [getBillInfoData]);

  return {
    diffData,
    type,
    isLoading,
    infoType,
    billId,
    tabItems,
    activeKey,
    billInfoData,
    containReceiverInfo,
    refreshTableRef,
    goodsHeaderStatisticsData,
    getGoodsHeaderStatistics,
    reload: getBillInfoData,
    setActiveKey,
    handleTabChange,
    handleCheckRemove,
  };
};
