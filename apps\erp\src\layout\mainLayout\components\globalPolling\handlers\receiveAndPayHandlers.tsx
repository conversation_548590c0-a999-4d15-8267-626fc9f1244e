import { globalResultHandlerRegistry } from '../registry';
import { Flex } from '@slt/base-ui';
import { GlobalPollingTaskEnum, type PollingMsgConfig } from '@/store/type';
import { ReceiveTaskStatusEnum, type AsyncTaskProgressVO } from '@slt/services';

import { OperationStatusNotification } from '@/components/biz';
import { BillTypeEnum } from '@slt/utils';

/**
 * 订单详情轮询结果处理器
 * 当订单详情轮询成功时，自动打开订单详情弹窗
 */
class ReceiveAndPayHandlers {
  taskType = GlobalPollingTaskEnum.RECEIVEANDPAY;

  onSuccess = (
    data: AsyncTaskProgressVO,
    taskId: string,
    msgConfig?: PollingMsgConfig,
  ) => {
    // 获取全局contextHolder
    const api = globalResultHandlerRegistry.notificationInstance;

    const { taskStatus, customerName, billType, failReason, billNoList } = data;
    const { operateType } = msgConfig || {};
    const typeText = billType === BillTypeEnum.收款单 ? '收' : '付';

    const handleToTransaction = () => {
      if (msgConfig?.openDetailTabs?.to) {
        globalResultHandlerRegistry.openTabInstance?.(
          msgConfig?.openDetailTabs?.to,
          {
            tabSearch: {
              billNoList: billNoList,
            },
          },
        );
      }
    };

    // 提取公共配置
    const baseConfig = {
      message: '提示',
      duration: 0,
      placement: 'bottomRight',
    };

    switch (true) {
      case taskStatus === ReceiveTaskStatusEnum['完成'] &&
        operateType === 'save':
        api?.open({
          ...baseConfig,
          description: (
            <OperationStatusNotification
              statusText={`${customerName}的${typeText}款单生成成功`}
              primaryBtnText="查看单据"
              handlePrimary={handleToTransaction}
            />
          ),
        });
        break;

      case taskStatus === ReceiveTaskStatusEnum['失败'] &&
        operateType === 'save':
        api?.open({
          ...baseConfig,
          description: (
            <OperationStatusNotification
              status="error"
              statusText={`${customerName}的${typeText}款单生成失败，已自动保存为草稿单据`}
              defaultBtnText="查看失败原因"
              primaryBtnText="查看草稿单据"
              handlePrimary={handleToTransaction}
              failedReasonData={failReason}
            />
          ),
        });
        break;

      case taskStatus === ReceiveTaskStatusEnum['完成'] &&
        operateType === 'delete':
        api?.open({
          ...baseConfig,
          description: (
            <OperationStatusNotification
              statusText={`${customerName}的${typeText}款单删除成功`}
            />
          ),
        });
        break;

      case taskStatus === ReceiveTaskStatusEnum['失败'] &&
        operateType === 'delete':
        api?.open({
          ...baseConfig,
          description: (
            <OperationStatusNotification
              status="error"
              statusText={`${customerName}的${typeText}款单删除失败`}
              defaultBtnText="查看失败原因"
              primaryBtnText="查看单据"
              handlePrimary={handleToTransaction}
              failedReasonData={failReason}
            />
          ),
        });
        break;

      case taskStatus === ReceiveTaskStatusEnum['完成'] &&
        operateType === 'examine':
        api?.open({
          ...baseConfig,
          description: (
            <OperationStatusNotification
              status="success"
              statusText={`${customerName}的${typeText}款单审核成功`}
              primaryBtnText="查看单据"
              handlePrimary={handleToTransaction}
            />
          ),
        });
        break;

      case taskStatus === ReceiveTaskStatusEnum['失败'] &&
        operateType === 'examine':
        api?.open({
          ...baseConfig,
          description: (
            <OperationStatusNotification
              status="error"
              statusText={`${customerName}的${typeText}款单审核失败`}
              primaryBtnText="查看单据"
              defaultBtnText="查看失败原因"
              handlePrimary={handleToTransaction}
              failedReasonData={failReason}
            />
          ),
        });
        break;

      default:
        break;
    }
  };

  onError = (error: any, taskId: string, msgConfig?: PollingMsgConfig) => {
    const api = globalResultHandlerRegistry.notificationInstance; // 获取api
    // 轮询失败时的处理
    api?.error({
      message: msgConfig ? `${msgConfig?.title}任务失败` : '任务失败',
      description: (
        <Flex vertical gap={8}>
          <Flex>{msgConfig?.description || '任务执行过程中发生错误'}</Flex>
        </Flex>
      ),
      placement: 'bottomRight',
    });
  };
}

// 注册处理器
export const receiveAndPayHandlers = new ReceiveAndPayHandlers();

globalResultHandlerRegistry.register(receiveAndPayHandlers);
