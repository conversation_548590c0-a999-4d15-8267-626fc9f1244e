import { AddGoodsSkuModal, GoodsSkuDistributionType } from '@/components/biz';
import { useOpenTab } from '@/hooks/useOpenTab';
import { routerMap } from '@/router/routerMap';
import {
  Alert,
  Flex,
  Modal,
  msgSuccess,
  msgWarn,
  Spin,
  Typography,
} from '@slt/base-ui';
import { useMutation, useQuery } from '@slt/net-query';
import {
  hubERPBizUploadGoods,
  hubERPBizUploadGoodsCount,
  type BasicGoodsPageSkuDTO,
  type HubDockingERPPageDTO,
} from '@slt/services';
import { ASYNC_API_SUCCESS_TIPS, EnableGoodsEnum } from '@slt/utils';
import { useState } from 'react';
import { twMerge } from 'tailwind-merge';
import { useStyles } from '../styles';
import dayjs from 'dayjs';

enum PushTypeEnum {
  UnSynced = 1,
  WaitUpdate = 2,
  CustomFilter = 3,
}

/** 自动推送商品到三方系统 */
export const GoodsAutoPushModal = (props: { record: HubDockingERPPageDTO }) => {
  const { record } = props;
  const [pushType, setPushType] = useState<PushTypeEnum | undefined>(undefined);
  const [selectedList, setSelectedList] = useState<BasicGoodsPageSkuDTO[]>([]);

  const { styles } = useStyles();

  const handleSetPushType = (type: PushTypeEnum) => {
    if (pushType === type) {
      setPushType(undefined);
    } else {
      setPushType(type);
    }
  };

  const { openTab } = useOpenTab();

  const {
    data: pushGoodsPreData,
    isLoading,
    reload: reloadPushGoodsPre,
  } = useQuery(hubERPBizUploadGoodsCount({ erpDockingId: record.id }), {
    queryOptions: {
      enabled: false,
    },
  });

  const { mutate: save } = useMutation(hubERPBizUploadGoods);

  const handleOpenGoodsInfo = () => {
    openTab(routerMap.goodsInfo.route);
  };

  const handleBeforeTrigger = async () => {
    setSelectedList([]);
    setPushType(undefined);
    await reloadPushGoodsPre();
    return true;
  };

  const handleOk = async (cb: () => void) => {
    if (!pushType) {
      msgWarn('请先选择同步商品范围');
      return;
    }

    if (pushType === PushTypeEnum.CustomFilter && !selectedList.length) {
      msgWarn('请选择商品');
      return;
    }

    const params: {
      optType: string;
      erpDockingId: string;
      erpType: string;
      timeline: string;
      targetIds?: string[];
    } = {
      optType: `${pushType}`,
      erpDockingId: record.id,
      erpType: record.erpType,
      timeline: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    };

    if (pushType === PushTypeEnum.CustomFilter) {
      params.targetIds = selectedList.map((item) => item.id);
    }

    const { success } = await save(params);

    if (success) {
      msgSuccess(ASYNC_API_SUCCESS_TIPS);
      cb();
    }
  };

  return (
    <Modal
      type="edit"
      trigger={<Typography.Link>推送商品</Typography.Link>}
      okText="确认推送"
      title="推送商品"
      onBeforeTrigger={handleBeforeTrigger}
      onOk={handleOk}
    >
      <Flex vertical gap={16}>
        <Alert
          message={
            <Flex>
              <Typography.Text>商品数据来源于【</Typography.Text>
              <Typography.Link onClick={handleOpenGoodsInfo}>
                商品信息
              </Typography.Link>
              <Typography.Text>】</Typography.Text>
            </Flex>
          }
          type="info"
          showIcon
        />
        <Flex vertical gap={8}>
          <Typography.Text className="text-lg font-strong">
            请选择同步商品范围
          </Typography.Text>
          <Spin spinning={isLoading}>
            <Flex gap={32}>
              <Flex
                flex={1}
                vertical
                gap={8}
                className={twMerge(
                  'cursor-pointer rounded bg-fill-secondary py-xxs',
                  pushType === PushTypeEnum.UnSynced && styles.selected,
                )}
                onClick={() => handleSetPushType(PushTypeEnum.UnSynced)}
              >
                <Typography.Text className="text-center">
                  未同步商品
                </Typography.Text>
                <Typography.Text className="text-center text-xl font-strong text-gold-6">
                  {pushGoodsPreData?.unSync}
                </Typography.Text>
              </Flex>
              <Flex
                flex={1}
                vertical
                gap={8}
                className={twMerge(
                  'cursor-pointer rounded bg-fill-secondary py-xxs',
                  pushType === PushTypeEnum.WaitUpdate && styles.selected,
                )}
                onClick={() => handleSetPushType(PushTypeEnum.WaitUpdate)}
              >
                <Typography.Text className="text-center">
                  待更新商品
                </Typography.Text>
                <Typography.Text className="text-center text-xl font-strong text-gold-6">
                  {pushGoodsPreData?.waitUpdate}
                </Typography.Text>
              </Flex>
              <Flex
                flex={1}
                vertical
                gap={8}
                className={twMerge(
                  'cursor-pointer rounded bg-fill-secondary py-xxs',
                  pushType === PushTypeEnum.CustomFilter && styles.selected,
                )}
                onClick={() => handleSetPushType(PushTypeEnum.CustomFilter)}
              >
                <Typography.Text className="text-center">
                  自定义筛选
                </Typography.Text>
                <Flex gap={8} align="center" justify="center">
                  <Typography.Text className="text-center text-xl font-strong text-gold-6">
                    {selectedList.length}
                  </Typography.Text>
                  <AddGoodsSkuModal
                    title="商品选择"
                    tableId="1756455142"
                    selectionType="checkbox"
                    extraParams={{
                      enable: EnableGoodsEnum.启用,
                    }}
                    distributionTypes={[
                      GoodsSkuDistributionType.商品,
                      GoodsSkuDistributionType.组合,
                      GoodsSkuDistributionType.分销,
                      GoodsSkuDistributionType.分销组合,
                    ]}
                    trigger={(onClick) => (
                      <Typography.Link
                        onClick={(e) => {
                          e.stopPropagation();
                          onClick();
                        }}
                      >
                        选择商品
                      </Typography.Link>
                    )}
                    onOk={(data) => {
                      setSelectedList(data);
                      setPushType(PushTypeEnum.CustomFilter);
                    }}
                    echoData={() => selectedList}
                  />
                </Flex>
              </Flex>
            </Flex>
          </Spin>
        </Flex>
      </Flex>
    </Modal>
  );
};
