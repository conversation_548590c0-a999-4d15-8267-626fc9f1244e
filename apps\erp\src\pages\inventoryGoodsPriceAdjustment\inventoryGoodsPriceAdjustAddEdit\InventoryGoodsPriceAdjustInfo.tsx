import { useInventoryReportLossInfo } from './hooks/useInventoryGoodsPriceAdjustInfo';
import { useInventoryReportLossInfoForm } from './hooks/useInventoryAllotOutInfoForm';
import { useStyles } from './InWarehouseAddEdit.styles';

import {
  BillTypeEnum,
  CommonBillStatusType,
  EnableGoodsEnum,
} from '@slt/utils';

import { routerMap } from '@/router/routerMap';
import { useOpenTab } from '@/hooks/useOpenTab';
import { useActions } from '@/store/system';

import type { InventoryGoodsPriceAdjustInfoProps } from '../type';
import { debounce } from 'lodash-es';

import {
  GoodsSkuDistributionType,
  SaveFailedReasonModal,
  ReceiptWatermark,
  AddGoodsSkuModal,
  ReceiptFooter,
  ImportGoodsModal,
} from '@/components/biz';
import {
  Button,
  Col,
  ConfigForm,
  EditableTable,
  Flex,
  Form,
  LinkButton,
  Partition,
  Row,
  Spin,
  Typography,
} from '@slt/base-ui';
import { maxBillDetailRows } from '@/constants/bill';

const importDesc = [
  {
    text: (
      <>
        <Typography.Text className="text-red-6">
          规格编码、调整后单价必填，
        </Typography.Text>
        <Typography.Text>备注可选填，超过200字时，将自动截取</Typography.Text>
      </>
    ),
    style: {},
  },
  {
    text: '商品在单据明细中已存在时，将进行导入更新',
    style: {},
  },
  {
    text: `最多支持导入${maxBillDetailRows}条`,
  },
];

/**商品调价单详情 */
export const InventoryGoodsPriceAdjustInfo = (
  props: InventoryGoodsPriceAdjustInfoProps,
) => {
  const {
    isDetail,
    type,
    id,
    form,
    detailInfo,
    detailLoading,
    dataSource,
    editableTableRef,
    billNoRepeatRef,
    saveFailedReasonModalRef,
    departmentOptions,
    rules,
    tableRowClassName,
    handleReceiptCancel,
    handleReceiptSave,
    handleReceiptAdd,
    handleBatchModify,
    handleDelete,
    onSelectedGoods,
    onRowDataChange,
    onValuesChange,
    handleSaveContinue,
    onCreateReceiptNo,
    onEditReceiptNo,
    getImportGoodsList,
    checkBeforeClick,
  } = useInventoryReportLossInfo(props);
  const { billStatus } = detailInfo || {};

  const { styles } = useStyles();
  const { openTab } = useOpenTab();
  const { closeAndRefreshPrevTab } = useActions();

  const { formBasicColumns, tableColumns } = useInventoryReportLossInfoForm(
    isDetail,
    departmentOptions,
    billNoRepeatRef,
    handleDelete,
    handleBatchModify,
  );

  return (
    <Flex className="flex h-full w-full" vertical>
      {(type === 'add' && !id) || !detailLoading ? (
        <>
          <Flex className="flex-1 overflow-auto">
            <Form
              id={!isDetail ? 'InventoryGoodsPriceAdjustInfo' : ''}
              name="InventoryGoodsPriceAdjustInfo"
              className="w-full"
              labelWidth={80}
              form={form}
              disabled={isDetail}
              initialValues={detailInfo}
              onValuesChange={onValuesChange}
            >
              <Partition title="基本信息" className="relative">
                <ConfigForm proportion={4} isShowAll list={formBasicColumns} />
                <ReceiptWatermark
                  showWatermark={isDetail}
                  sceneType="dockingCloudWH"
                  billStatus={billStatus}
                />
              </Partition>
              <Partition title="商品明细" className="mt-xs">
                <Row>
                  <Col span={24}>
                    <Form.Item
                      label=""
                      name="goodsList"
                      className={styles.form_item_style}
                      rules={rules}
                    >
                      <Flex className="mb-xs" vertical={false} gap={12}>
                        <AddGoodsSkuModal
                          title="商品选择"
                          tableId="1731401597"
                          selectionType="checkbox"
                          distributionTypes={[GoodsSkuDistributionType.商品]}
                          extraParams={{
                            enable: EnableGoodsEnum.启用,
                          }}
                          needSubGoodsList
                          trigger={(onClick) => {
                            return !isDetail ? (
                              <LinkButton
                                sceneType="add"
                                onClick={() => checkBeforeClick(onClick)}
                              >
                                添加商品
                              </LinkButton>
                            ) : (
                              <></>
                            );
                          }}
                          onOk={onSelectedGoods}
                        />
                        {!isDetail ? (
                          <ImportGoodsModal
                            billType={BillTypeEnum.库存调价单}
                            title="导入库存调价单"
                            importDesc={importDesc}
                            selectedNum={dataSource?.length}
                            maxNum={maxBillDetailRows}
                            getImportGoodsList={getImportGoodsList}
                          />
                        ) : null}
                      </Flex>
                      <EditableTable
                        id="inventoryCheckInfoEditableTable"
                        rowKey="uid"
                        ref={editableTableRef}
                        resizableHeader
                        virtual
                        columnsSetting={false}
                        pagination={false}
                        columns={tableColumns}
                        dataSource={dataSource}
                        scroll={{ y: 500 }}
                        rowClassName={tableRowClassName}
                        handleSave={debounce(onRowDataChange, 200)}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Partition>
            </Form>
          </Flex>
          {/* 单据footer */}
          <ReceiptFooter
            billType={BillTypeEnum.库存调价单}
            pageType={type}
            detailInfo={detailInfo}
            handleCancel={handleReceiptCancel}
            handleSave={handleReceiptSave}
            handleAdd={handleReceiptAdd}
            customEditBtn={
              billStatus === CommonBillStatusType['草稿'] ? ( // 如果为草稿，则显示按钮
                <Button
                  size="large"
                  authId="2257"
                  onClick={() => {
                    closeAndRefreshPrevTab();
                    openTab(routerMap.inventoryGoodsPriceAdjustmentEdit.route, {
                      search: { id },
                    });
                  }}
                >
                  编辑
                </Button>
              ) : (
                <div />
              )
            }
            addAuthId="2256"
            editAuthId="2257"
          />
          <SaveFailedReasonModal
            ref={saveFailedReasonModalRef}
            onSaveContinue={handleSaveContinue}
            onCreateReceiptNo={onCreateReceiptNo}
            onEditReceiptNo={onEditReceiptNo}
          />
        </>
      ) : (
        <Spin className="flex h-full w-full items-center justify-center" />
      )}
    </Flex>
  );
};

export default InventoryGoodsPriceAdjustInfo;
