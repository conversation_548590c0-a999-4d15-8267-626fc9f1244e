import { useOpenTab } from '@/hooks/useOpenTab';
import { routerMap } from '@/router/routerMap';
import type {
  FixedType,
  FormItemConfigDTO,
  TableColumnType,
} from '@slt/base-ui';
import {
  ColumnWidth,
  Flex,
  GoodsImage,
  msgSuccess,
  SysTooltip,
  TableOperationGroup,
  Tag,
  TypographyV2,
  useAuthProps,
} from '@slt/base-ui';
import type { CommonRecord } from '@slt/base-utils';
import type { BizTableRef } from '@slt/biz-ui';
import {
  GoodsCategoryCascader,
  OperationConfirm,
  OperationLogModal,
  type BizTableColumnType,
} from '@slt/biz-ui';
import { net } from '@slt/net';
import type {
  DistributionPriceExtension,
  ListSpuDTO,
  RelatedParsingConfirmParams,
} from '@slt/services';
import {
  basicBrandListTenantBrand,
  basicResourceAssociateGoodsToResConfirm,
  basicTagListTenantTag,
  BasicTagType,
  bossBookPublisherSelectInput,
  bossGoodsSpuTypeTenantRelationQuery,
  goodsBatchDeleteSku,
  goodsBatchDeleteSpu,
} from '@slt/services';
import {
  DistributionLevel,
  enumToOptions,
  HaveOrNotEnum,
  maxInputDecimal,
  maxInputFourDecimal,
  PLACEHOLDER,
  ResourceAssociateEnum,
  SystemLogType,
} from '@slt/utils';
import { useCallback, useMemo, useRef } from 'react';
import { useSearchParams } from 'react-router-dom';
import {
  SelectedDimension,
  createSourceOptions,
  selectedDimensionOptions,
} from '../constants';
import { createStyles } from 'antd-style';
import { SingleGoodsRelationModal } from '@/components/biz';
import { SingleGoodsRelatedModal } from './SingleGoodsRelatedModal';
import { useMutation } from '@slt/net-query';
import { useSegmentedValue } from '@/hooks/bizHooks';
import { authIdsMap } from '@/constants/authIds';

type Props = {
  visualAngle: 'base' | 'inventory';
};

const renderDistributionPrice = (
  data: DistributionPriceExtension[],
  level: DistributionLevel,
) => {
  const price = data?.find(
    (item) => item.extensionKey === level,
  )?.extensionValue;

  return price;
};

export const useColumns = (props: Props) => {
  const { styles } = useStyles();
  const { visualAngle } = props;
  const { openTab } = useOpenTab();

  const { hasAuth: hasViewAuth } = useAuthProps({
    authId: '1001',
  });

  const [searchParams] = useSearchParams();
  const selectedDimensionFromUrl = searchParams.get(
    'selectedDimension',
  ) as SelectedDimension;

  const tableRef = useRef<BizTableRef<CommonRecord>>(null);

  const reloadTable = useCallback(() => {
    tableRef.current?.reload();
  }, [tableRef]);

  const { segmentedValue: selectedDimension, onSegmentedValueChange } =
    useSegmentedValue<SelectedDimension>({
      module: 'goodsInfo',
      forceUseInitValue: !!selectedDimensionFromUrl,
      initValue: selectedDimensionFromUrl || SelectedDimension['按规格'],
    });

  const onDelete = useCallback(
    async (ids: string[]) => {
      if (selectedDimension === SelectedDimension['按商品']) {
        return await net.fetch(
          goodsBatchDeleteSpu({ ids: ids.join(','), draft: 0 }),
        );
      } else {
        return await net.fetch(goodsBatchDeleteSku(ids.join(',')));
      }
    },
    [selectedDimension],
  );

  const onSelectedDimensionChange = useCallback(
    (value: SelectedDimension) => {
      onSegmentedValueChange(value);
      tableRef.current?.setSelectedRows([]);
      // 切换查看维度重置已有的排序参数
      tableRef.current?.resetSort();
    },
    [onSegmentedValueChange],
  );

  const { mutate: mutateConfirmPackage } = useMutation(
    basicResourceAssociateGoodsToResConfirm,
  );

  const handleConfirmPackage = useCallback(
    async (data: RelatedParsingConfirmParams[]) => {
      const { success } = await mutateConfirmPackage(data);

      if (success) {
        tableRef.current?.reload();
        msgSuccess('素材包关联成功');

        return true;
      }

      return false;
    },
    [mutateConfirmPackage, tableRef],
  );

  const toGoodsDetailPage = useCallback(
    (goodsId: string) => {
      openTab(routerMap.goodsInfoView.route, {
        search: { goodsId },
      });
    },
    [openTab],
  );

  const spuGoodsNoColumn = useMemo((): TableColumnType<ListSpuDTO> => {
    return {
      title: '商品编码',
      key: 'goodsNo',
      sort: true,
      batchCopy: true,
      render: (_, record) => {
        const { goodsNo, id } = record;
        if (!goodsNo) {
          return null;
        }
        return (
          <TypographyV2.Text
            copyable
            className={hasViewAuth ? 'cursor-pointer text-primary' : ''}
            onClick={hasViewAuth ? () => toGoodsDetailPage(id) : undefined}
          >
            {goodsNo}
          </TypographyV2.Text>
        );
      },
    };
  }, [hasViewAuth, toGoodsDetailPage]);

  const skuGoodsNoColumn = useMemo((): TableColumnType<CommonRecord> => {
    return {
      title: '商品编码',
      key: 'goodsNo',
      sort: true,
      batchCopy: true,
      render: (_, record) => {
        const { goodsNo, goodsId } = record;
        if (!goodsNo) {
          return null;
        }

        const checkable = hasViewAuth && !!goodsId;
        return (
          <TypographyV2.Text
            copyable
            className={checkable ? 'cursor-pointer text-primary' : ''}
            onClick={checkable ? () => toGoodsDetailPage(goodsId) : undefined}
          >
            {goodsNo}
          </TypographyV2.Text>
        );
      },
    };
  }, [hasViewAuth, toGoodsDetailPage]);

  const actionColumn = useMemo(() => {
    return {
      title: '操作',
      key: 'action',
      fixed: 'right' as FixedType,
      needExport: false,
      width: 150,
      render: (
        _: unknown,
        record: { id: string; goodsId?: string; quantityNo?: number },
      ) => {
        const { id, goodsId, quantityNo } = record;

        const onClickToEdit = () => {
          openTab(routerMap.goodsInfoEdit.route, {
            search: {
              goodsId:
                selectedDimension === SelectedDimension['按商品']
                  ? id
                  : goodsId,
            },
          });
        };

        const onClickToCopy = () => {
          openTab(routerMap.goodsInfoAdd.route, {
            search: {
              goodsId:
                selectedDimension === SelectedDimension['按商品']
                  ? id
                  : goodsId,
            },
          });
        };

        return (
          <TableOperationGroup
            onClick={(e: React.MouseEvent<HTMLElement>) => e.stopPropagation()}
            operationList={[
              {
                label: (
                  <TypographyV2.Link authId="64" onClick={onClickToEdit}>
                    编辑
                  </TypographyV2.Link>
                ),
                key: 'edit',
                show: true,
                authId: '64',
              },
              {
                label: (
                  <TypographyV2.Link authId="63" onClick={onClickToCopy}>
                    复制
                  </TypographyV2.Link>
                ),
                key: 'copy',
                show: true,
                authId: '63',
              },
              {
                label: (
                  <OperationConfirm
                    batchResult={false}
                    successMsg="删除成功"
                    reload={reloadTable}
                    modalProps={{
                      title: '提示',
                      content: '是否确认删除所选数据',
                      type: 'info',
                    }}
                    onConfirm={() => onDelete([record.id])}
                  >
                    {(onClick) => {
                      return (
                        <TypographyV2.Link authId="65" onClick={onClick}>
                          删除
                        </TypographyV2.Link>
                      );
                    }}
                  </OperationConfirm>
                ),
                key: 'online',
                show: true,
                authId: '65',
              },
              {
                label: (
                  <OperationLogModal
                    operateModule={routerMap.goodsInfo.name}
                    searchSource={SystemLogType.ERP}
                    id={
                      selectedDimension === SelectedDimension['按商品']
                        ? record.id
                        : record.goodsId
                    }
                    tableId="**********"
                  />
                ),
                key: 'log',
                show: true,
              },
            ]}
          />
        );
      },
    };
  }, [onDelete, reloadTable, openTab, selectedDimension]);

  const columns = useMemo(() => {
    switch (selectedDimension) {
      case SelectedDimension['按商品']: {
        switch (visualAngle) {
          case 'base': {
            return [
              {
                title: '主图',
                key: 'mainPicUrlList',
                render: (_, record) => {
                  const url = record.mainPicUrlList?.[0]?.url;
                  if (url) {
                    return <GoodsImage size="small" key={url} src={url} />;
                  }
                },
              },
              spuGoodsNoColumn,
              {
                title: '商品名称',
                key: 'goodsName',
                sort: true,
                editable: true,
                valueType: 'input',
                fieldProps: { maxLength: 100 },
                formItemProps: {
                  rules: [{ required: true, message: '请输入商品名称' }],
                },
              },
              {
                title: '商品简名',
                key: 'goodsShortName',
                sort: true,
                editable: true,
                valueType: 'input',
                fieldProps: { maxLength: 40 },
              },
              {
                title: '商品条码',
                key: 'barcode',
                sort: true,
              },
              {
                title: '固定成本价',
                key: 'referenceCost',
                width: 120,
                sort: true,
                editable: true,
                valueType: 'inputNumberUnitPrice',
                fieldProps: {
                  min: 0,
                  max: maxInputFourDecimal,
                },
              },
              {
                title: 'ISBN',
                key: 'isbn',
                sort: true,
                batchCopy: true,
                render: (value) =>
                  value ? (
                    <TypographyV2.Text copyable>{value}</TypographyV2.Text>
                  ) : (
                    PLACEHOLDER
                  ),
              },
              {
                title: '图书名称',
                key: 'bookName',
                sort: true,
              },
              {
                title: '定价',
                key: 'price',
                sort: true,
              },
              {
                title: '出版社',
                key: 'publisherName',
              },
              {
                title: '出版时间',
                key: 'publishDate',
                sort: true,
              },
              {
                title: '作者',
                key: 'author',
                sort: true,
              },
              {
                title: '商品标签',
                key: 'productTagName',
                render: (_, record) => {
                  return record.tagInfoList
                    ?.map((item) => item.productTagName)
                    .join(',');
                },
              },
              {
                title: '供应商',
                key: 'supplierName',
              },
              {
                title: '商品备注',
                key: 'goodsRemarks',
                sort: true,
                editable: true,
                valueType: 'input',
                fieldProps: { maxLength: 100 },
              },
              {
                title: '商品品牌',
                key: 'brandName',
                render: (_, record) => {
                  const { brandInfoList } = record;
                  if (!brandInfoList || brandInfoList.length === 0) {
                    return PLACEHOLDER;
                  }
                  return brandInfoList[0].brandName || PLACEHOLDER;
                },
              },
              {
                title: '关联素材包',
                key: 'resourceId',
                titleRender: (title) => (
                  <>
                    {title}
                    <SysTooltip title="可以从素材库中关联素材包，当下游分销商选品后，可以下载对应的素材包" />
                  </>
                ),
                render: (value: string, record: ListSpuDTO) => {
                  if (!value) {
                    return (
                      <Flex>
                        <TypographyV2.Text>未关联，</TypographyV2.Text>
                        <SingleGoodsRelationModal
                          type={ResourceAssociateEnum.商品}
                          basicInfo={{
                            id: record.id,
                            name: record.goodsName ?? '',
                            code: record.goodsNo ?? '',
                          }}
                          trigger={
                            <TypographyV2.Link
                              authId={{
                                authId: '2101',
                                authType: 'disabled',
                              }}
                            >
                              去关联
                            </TypographyV2.Link>
                          }
                          onOk={handleConfirmPackage}
                        />
                      </Flex>
                    );
                  }
                  return (
                    <SingleGoodsRelatedModal
                      basicInfo={{
                        id: record.id,
                        name: record.goodsName ?? '',
                        code: record.goodsNo ?? '',
                      }}
                      packageInfo={{
                        id: value,
                        name: record.resourceName ?? '',
                      }}
                      tableRef={tableRef}
                      trigger={
                        <TypographyV2.Link
                          authId={{
                            authId: '2101',
                            authType: 'disabled',
                          }}
                        >
                          已关联
                        </TypographyV2.Link>
                      }
                    />
                  );
                },
              },
              {
                title: '商品类型',
                key: 'goodsTypeName',
              },
              {
                title: '商家自建分类',
                key: 'categoryName',
              },
              {
                title: '选品市场分类',
                key: 'bookCategoryName',
              },
              {
                title: '状态',
                key: 'enable',
                render: (value: number) => {
                  return value ? '启用' : '停用';
                },
              },
              {
                title: '创建来源',
                key: 'createSourceName',
              },
              {
                title: '创建人',
                key: 'createUserName',
              },
              {
                width: 'time',
                title: '创建时间',
                key: 'gmtCreate',
                sort: true,
              },
              {
                title: '修改人',
                key: 'modifiedUserName',
              },
              {
                title: '修改时间',
                key: 'gmtModified',
                width: 'time',
                sort: true,
              },
              actionColumn,
            ] as BizTableColumnType<ListSpuDTO>[];
          }
          case 'inventory': {
            return [
              {
                title: '主图',
                key: 'mainPicUrlList',
                render: (_, record) => {
                  const url = record.mainPicUrlList?.[0]?.url;
                  if (url) {
                    return <GoodsImage size="small" key={url} src={url} />;
                  }
                },
              },
              spuGoodsNoColumn,
              {
                title: '商品名称',
                key: 'goodsName',
                sort: true,
              },
              {
                title: '商品简名',
                key: 'goodsShortName',
                sort: true,
              },
              {
                title: '固定成本价',
                key: 'referenceCost',
                width: 120,
                sort: true,
              },
              {
                title: '实际库存',
                key: 'quantityNo',
                render: (value: number) => {
                  return value ? value : 0;
                },
              },
              {
                title: '成本均价',
                key: 'basicSellingPrice',
              },
              {
                title: '库存金额',
                key: 'basicSellingPriceAmount',
                width: 120,
              },
              {
                title: '可销售库存',
                key: 'sellableQuantityNo',
                render: (value: number) => {
                  return value ? value : 0;
                },
              },
              {
                title: '可发货库存',
                key: 'stockDeliver',
                render: (value: number) => {
                  return value ? value : 0;
                },
              },
              actionColumn,
            ] as TableColumnType<ListSpuDTO>[];
          }
          default: {
            return [];
          }
        }
      }
      case SelectedDimension['按规格']: {
        switch (visualAngle) {
          case 'base': {
            return [
              {
                title: '规格图',
                key: 'specPicUrlList',
                render: (_, record) => {
                  const url = record.specPicUrlList?.[0]?.url;
                  if (url) {
                    return <GoodsImage size="small" key={url} src={url} />;
                  }
                },
              },
              {
                title: '规格编码',
                key: 'goodsSkuNo',
                sort: true,
                batchCopy: true,
                render: (value) =>
                  value ? (
                    <TypographyV2.Text copyable>{value}</TypographyV2.Text>
                  ) : (
                    PLACEHOLDER
                  ),
              },
              {
                title: '规格名称',
                key: 'goodsSkuName',
                sort: true,
                editable: true,
                valueType: 'input',
                fieldProps: { maxLength: 100 },
                formItemProps: {
                  rules: [{ required: true, message: '请输入规格名称' }],
                },
              },
              skuGoodsNoColumn,
              {
                title: '商品名称',
                key: 'goodsName',
                sort: true,
                editable: true,
                valueType: 'input',
                fieldProps: { maxLength: 100 },
                formItemProps: {
                  rules: [{ required: true, message: '请输入商品名称' }],
                },
              },
              {
                title: '商品简名',
                key: 'goodsShortName',
                sort: true,
                editable: true,
                valueType: 'input',
                fieldProps: { maxLength: 40 },
              },
              {
                title: '商品条码',
                key: 'barcode',
                sort: true,
              },
              {
                title: '固定成本价',
                key: 'referenceCost',
                width: 120,
                sort: true,
                editable: true,
                valueType: 'inputNumberUnitPrice',
                fieldProps: {
                  min: 0,
                  max: maxInputFourDecimal,
                },
              },
              {
                title: '重量(kg)',
                key: 'weight',
                width: 120,
                sort: true,
              },
              {
                title: '最低售价',
                key: 'lowestPrice',
                width: 120,
                sort: true,
              },
              {
                title: '标准售价',
                key: 'standardPrice',
                width: 120,
                sort: true,
              },
              {
                title: '基础分销价',
                key: 'basicDistributionPrice',
                width: 120,
                sort: true,
              },
              {
                title: '1级分销价',
                key: 'level1DistributionPrice',
                width: 120,
                render: (_: unknown, record: CommonRecord) => {
                  return renderDistributionPrice(
                    record.extensionPriceList,
                    DistributionLevel.L1,
                  );
                },
              },
              {
                title: '2级分销价',
                key: 'level2DistributionPrice',
                width: 120,
                render: (_: unknown, record: CommonRecord) => {
                  return renderDistributionPrice(
                    record.extensionPriceList,
                    DistributionLevel.L2,
                  );
                },
              },
              {
                title: '3级分销价',
                key: 'level3DistributionPrice',
                width: 120,
                render: (_: unknown, record: CommonRecord) => {
                  return renderDistributionPrice(
                    record.extensionPriceList,
                    DistributionLevel.L3,
                  );
                },
              },
              {
                title: '4级分销价',
                key: 'level4DistributionPrice',
                width: 120,
                render: (_: unknown, record: CommonRecord) => {
                  return renderDistributionPrice(
                    record.extensionPriceList,
                    DistributionLevel.L4,
                  );
                },
              },
              {
                title: '5级分销价',
                key: 'level5DistributionPrice',
                width: 120,
                render: (_: unknown, record: CommonRecord) => {
                  return renderDistributionPrice(
                    record.extensionPriceList,
                    DistributionLevel.L5,
                  );
                },
              },
              {
                title: '库存同步',
                titleRender: (text: string) => (
                  <>
                    {text}
                    <SysTooltip
                      title={
                        <div>
                          <div>库存同步有3层设置：</div>
                          <div>1、商品设置</div>
                          <Flex>
                            2、
                            <TypographyV2.Link
                              authId={{
                                authId: authIdsMap.inventorySync.access,
                                authType: 'text',
                              }}
                              onClick={() => {
                                openTab(routerMap.inventorySync.route);
                              }}
                            >
                              店铺商品设置
                            </TypographyV2.Link>
                          </Flex>
                          <Flex>
                            3、
                            <TypographyV2.Link
                              authId={{
                                authId: authIdsMap.shopManagement.access,
                                authType: 'text',
                              }}
                              onClick={() => {
                                openTab(routerMap.shopManagement.route);
                              }}
                            >
                              店铺设置
                            </TypographyV2.Link>
                          </Flex>
                          <div>
                            此处为商品设置，3处设置均需开启才会自动同步库存
                          </div>
                        </div>
                      }
                    />
                  </>
                ),
                key: 'syncStock',
                render: (value: number) => {
                  // 0关 1开
                  return value ? (
                    <Tag color="success">已开启</Tag>
                  ) : (
                    <Tag color="error">未开启</Tag>
                  );
                },
              },
              {
                title: 'ISBN',
                key: 'isbn',
                sort: true,
                batchCopy: true,
                render: (value) =>
                  value ? (
                    <TypographyV2.Text copyable>{value}</TypographyV2.Text>
                  ) : (
                    PLACEHOLDER
                  ),
              },
              {
                title: '图书名称',
                key: 'bookName',
                sort: true,
              },
              {
                title: '定价',
                key: 'price',
                sort: true,
              },
              {
                title: '出版社',
                key: 'publisherName',
              },
              {
                title: '商品标签',
                key: 'productTagName',
                render: (_: unknown, record: ListSpuDTO) => {
                  return record.tagInfoList
                    ?.map((item) => item.productTagName)
                    .join(',');
                },
              },
              {
                title: '供应商',
                key: 'supplierName',
              },
              {
                title: '商品备注',
                key: 'goodsRemarks',
                sort: true,
                editable: true,
                valueType: 'input',
                fieldProps: { maxLength: 100 },
              },
              {
                title: '商品品牌',
                key: 'brandName',
                render: (_, record) => {
                  const { brandInfoList } = record;
                  if (!brandInfoList || brandInfoList.length === 0) {
                    return PLACEHOLDER;
                  }
                  return brandInfoList[0].brandName || PLACEHOLDER;
                },
              },
              {
                title: '商品类型',
                key: 'goodsTypeName',
              },
              {
                title: '商家自建分类',
                key: 'categoryName',
              },
              {
                title: '选品市场分类',
                key: 'bookCategoryName',
              },
              {
                title: '状态',
                key: 'enable',
                render: (value: number) => {
                  return value ? '启用' : '停用';
                },
              },
              {
                title: '创建来源',
                key: 'createSourceName',
              },
              {
                title: '创建人',
                key: 'createUserName',
              },
              {
                title: '创建时间',
                key: 'gmtCreate',
                width: ColumnWidth.Time,
                sort: true,
              },
              {
                title: '修改人',
                key: 'modifiedUserName',
              },
              {
                title: '修改时间',
                key: 'gmtModified',
                width: 'time',
                sort: true,
              },
              actionColumn,
            ] as TableColumnType<ListSpuDTO>[];
          }
          case 'inventory': {
            return [
              {
                title: '规格图',
                key: 'specPicUrlList',
                render: (_, record) => {
                  const url = record.specPicUrlList?.[0]?.url;
                  if (url) {
                    return <GoodsImage size="small" key={url} src={url} />;
                  }
                },
              },
              {
                title: '规格编码',
                key: 'goodsSkuNo',
                sort: true,
                batchCopy: true,
                render: (value) =>
                  value ? (
                    <TypographyV2.Text copyable>{value}</TypographyV2.Text>
                  ) : (
                    PLACEHOLDER
                  ),
              },
              {
                title: '规格名称',
                key: 'goodsSkuName',
                sort: true,
              },
              skuGoodsNoColumn,
              {
                title: '商品名称',
                key: 'goodsName',
                sort: true,
              },
              {
                title: '商品简名',
                key: 'goodsShortName',
                sort: true,
              },
              {
                title: '固定成本价',
                key: 'referenceCost',
                width: 120,
                sort: true,
              },
              {
                title: '实际库存',
                key: 'quantityNo',
                render: (value: number) => {
                  return value ? value : 0;
                },
              },
              {
                title: '成本均价',
                key: 'basicSellingPrice',
              },
              {
                title: '库存金额',
                key: 'basicSellingPriceAmount',
              },
              {
                title: '可销售库存',
                key: 'sellableQuantityNo',
                render: (value: number) => {
                  return value ? value : 0;
                },
                width: 120,
              },
              {
                title: '可发货库存',
                key: 'stockDeliver',
                render: (value: number) => {
                  return value ? value : 0;
                },
              },
              actionColumn,
            ] as TableColumnType<ListSpuDTO>[];
          }
          default: {
            return [];
          }
        }
      }
      default: {
        return [];
      }
    }
  }, [
    selectedDimension,
    visualAngle,
    actionColumn,
    spuGoodsNoColumn,
    skuGoodsNoColumn,
    handleConfirmPackage,
    openTab,
  ]);

  const searchColumns = useMemo((): FormItemConfigDTO[] => {
    return [
      {
        key: 'selectedDimension',
        type: 'segmented',
        fieldProps: {
          options: selectedDimensionOptions,
          onChange: onSelectedDimensionChange,
          value: selectedDimension,
          block: true,
        },
        colProps: {
          className: styles.segmentedControl,
        },
      },
      ...(selectedDimension === SelectedDimension.按规格
        ? [
            {
              formItemProps: {
                label: '规格编码',
                name: 'goodsSkuNoList',
              },
              key: 'goodsSkuNoList',
              type: 'batchInput',
              fieldProps: {
                placeholder: '请输入规格编码',
                allowClear: true,
              },
            },
            {
              formItemProps: {
                label: '规格名称',
                name: 'goodsSkuName',
              },
              key: 'goodsSkuName',
              type: 'input',
              fieldProps: {
                placeholder: '请输入规格名称',
                allowClear: true,
              },
            },
          ]
        : []),
      {
        formItemProps: {
          label: '商品编码',
          name: 'goodsNo',
        },
        key: 'goodsNo',
        type: 'batchInput',
        fieldProps: {
          placeholder: '请输入商品编码',
          allowClear: true,
        },
      },
      {
        formItemProps: {
          label: '商品名称',
          name: 'goodsName',
        },
        key: 'goodsName',
        type: 'input',
        fieldProps: {
          placeholder: '请输入商品名称',
          allowClear: true,
        },
      },
      {
        formItemProps: {
          label: '商品简名',
          name: 'goodsShortName',
        },
        key: 'goodsShortName',
        type: 'input',
        fieldProps: {
          placeholder: '请输入商品简名',
          allowClear: true,
        },
      },
      {
        formItemProps: {
          label: 'ISBN',
          name: 'isbns',
        },
        key: 'isbns',
        type: 'batchInput',
        fieldProps: {
          placeholder: '请输入ISBN',
          allowClear: true,
        },
      },
      {
        formItemProps: {
          label: '图书名称',
          name: 'bookName',
        },
        key: 'bookName',
        type: 'input',
        fieldProps: {
          placeholder: '请输入图书名称',
          allowClear: true,
        },
      },
      {
        type: 'apiSelect',
        formItemProps: {
          label: '出版社',
          name: 'publisherIdList',
        },
        fieldProps: {
          placeholder: '请选择出版社',
          allowClear: true,
          mode: 'multiple',
          request: bossBookPublisherSelectInput,
        },
      },
      {
        key: 'bookCategoryId',
        formItemProps: {
          label: '选品市场分类',
          name: 'bookCategoryId',
        },
        renderFormItem: () => <GoodsCategoryCascader showSearch />,
      },
      {
        formItemProps: {
          label: '商品类型',
          name: 'goodsTypeId',
        },
        key: 'goodsTypeId',
        type: 'apiSelect',
        fieldProps: {
          placeholder: '请选择商品类型',
          allowClear: true,
          allowSearch: false,
          mode: 'multiple',
          params: {
            filterNotEnable: 1,
          },
          request: bossGoodsSpuTypeTenantRelationQuery,
        },
      },
      {
        formItemProps: {
          label: '商品标签',
          name: 'productTagIds',
        },
        key: 'productTagIds',
        type: 'apiSelect',
        fieldProps: {
          placeholder: '请选择商品标签',
          allowClear: true,
          searchKey: 'tagName',
          mode: 'multiple',
          request: basicTagListTenantTag,
          params: {
            tagType: BasicTagType['商品标签'],
          },
          fieldNames: { label: 'tagName', value: 'id' },
        },
      },
      {
        formItemProps: {
          label: '商品品牌',
          name: 'brandIds',
        },
        key: 'brandIds',
        type: 'apiSelect',
        fieldProps: {
          allowClear: true,
          mode: 'multiple',
          searchKey: 'brandName',
          request: basicBrandListTenantBrand,
          fieldNames: { label: 'brandName', value: 'id' },
        },
      },
      {
        key: 'supplierIds',
        type: 'supplierSelect',
        fieldProps: {
          allowClear: true,
          mode: 'multiple',
        },
        formItemProps: {
          label: '供应商',
          name: 'supplierIds',
        },
      },
      {
        type: 'input',
        formItemProps: {
          label: '商品备注',
          name: 'goodsRemarks',
        },
      },
      ...(selectedDimension === SelectedDimension.按规格
        ? [
            {
              type: 'select',
              formItemProps: {
                label: '库存同步状态',
                name: 'selfSkuStockSync',
              },
              fieldProps: {
                options: [
                  { label: '未开启', value: false },
                  { label: '已开启', value: true },
                ],
              },
            },
          ]
        : []),
      {
        type: 'select',
        formItemProps: {
          label: '启用状态',
          name: 'enable',
          initialValue: 1,
        },
        fieldProps: {
          options: [
            { label: '停用', value: 0 },
            { label: '启用', value: 1 },
          ],
        },
      },
      {
        type: 'select',
        formItemProps: {
          label: '创建来源',
          name: 'createSources',
        },
        fieldProps: {
          mode: 'multiple',
          maxTagCount: 'responsive',
          options: createSourceOptions,
        },
      },
      {
        type: 'rangePicker',
        formItemProps: {
          label: '创建时间',
          name: 'gmtCreate',
        },
        fieldProps: {
          fieldNames: {
            start: 'gmtCreateMin',
            end: 'gmtCreateMax',
          },
          startFormat: 'YYYY-MM-DD 00:00:00',
          endFormat: 'YYYY-MM-DD 23:59:59',
        },
      },
      ...(selectedDimension === SelectedDimension.按商品
        ? [
            {
              key: 'hasMainPic',
              type: 'select',
              formItemProps: { name: 'hasMainPic', label: '有无主图' },
              fieldProps: {
                placeholder: '请选择',
                options: enumToOptions(HaveOrNotEnum),
              },
            },
            {
              key: 'hasDetailPic',
              type: 'select',
              formItemProps: { name: 'hasDetailPic', label: '有无详情图' },
              fieldProps: {
                placeholder: '请选择',
                options: enumToOptions(HaveOrNotEnum),
              },
            },
          ]
        : []),
    ];
  }, [onSelectedDimensionChange, selectedDimension, styles.segmentedControl]);

  return {
    tableRef,
    selectedDimension,
    columns,
    searchColumns,
    onDelete,
    reloadTable,
  };
};

const useStyles = createStyles(({ css }) => ({
  segmentedControl: css`
    .ant-col.ant-form-item-control {
      max-width: initial !important;
    }
  `,
}));
