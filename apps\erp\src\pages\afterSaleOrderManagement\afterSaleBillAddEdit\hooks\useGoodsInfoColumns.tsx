import { CombinedGoodsModal, InputMore, TextItemEdit } from '@/components/biz';
import {
  Avatar,
  Flex,
  Form,
  GoodsImage,
  inputNumberProps,
  msgWarn,
  Tag,
  Typography,
  type EditableTableProps,
  type FormInstance,
} from '@slt/base-ui';
import type { CommonRecord } from '@slt/base-utils';
import { mobileRegexp } from '@slt/base-utils';
import { AreaCompact, type AddressCascaderRef } from '@slt/biz-ui';
import type {
  AfterSaleOrderCreateUpdateDetailRequest,
  AfterSaleOrderCreateUpdateRequest,
  AfterSaleOrderDetailDTO,
  AfterSaleAttachmentDetailGroupVO,
  UpdateAfterSaleRemarkAndAttachmentParam,
} from '@slt/services';
import {
  AfterSaleViewModeEnum,
  omsAfterSaleStrategyRefundReasonList,
} from '@slt/services';
import {
  AfterSalesBusinessType,
  enumToOptions,
  maxInputDecimal,
} from '@slt/utils';
import { useCallback, useMemo, type Ref } from 'react';
import { AddGoodsSkuFromOrderModal } from '../../components/selectOrderOrGoods';
import { getIsOnlineOrder, OrderBillBusinessTypeMap } from '../../constants';
import { AfterSalesBusinessTypeColor } from '../../types';
import type { AfterSaleFormType } from '../../types';
import { AfterSaleRemarkAttachment } from '../../components/supplierDistributionAttachment';
export const useGoodsInfoColumns = (props: {
  onSelectGoods?: (v: AfterSaleOrderCreateUpdateDetailRequest[]) => void;
  form: FormInstance<AfterSaleFormType>;
  removeGoods: (ids: string[]) => void;
  dataSource: AfterSaleOrderCreateUpdateDetailRequest[];
  setFormValuesCb: (v: AfterSaleFormType) => void;
  formValues?: AfterSaleFormType;
  addressCascaderRef: React.RefObject<AddressCascaderRef>;
  handleChangeRefundAmount: (
    record: AfterSaleOrderCreateUpdateDetailRequest,
    index: number,
    changeAmount: number,
  ) => void;
  myAndSupplierDistributionRemark?: AfterSaleAttachmentDetailGroupVO;
  handleAfterSaleRemarkAttachment?:
    | ((
        v: Partial<UpdateAfterSaleRemarkAndAttachmentParam>,
        flag?: AfterSaleViewModeEnum,
      ) => void)
    | undefined;
}) => {
  const {
    form,
    onSelectGoods,
    removeGoods,
    dataSource,
    setFormValuesCb,
    formValues,
    addressCascaderRef,
    handleChangeRefundAmount,
    myAndSupplierDistributionRemark,
    handleAfterSaleRemarkAttachment,
  } = props;
  const afterSaleType = Form.useWatch('afterSaleType', form);
  const saleOrderId = Form.useWatch('saleOrderId', form);

  const validateReceiverInfo = useCallback(
    (_rule: any, _value: any, callback: (v?: string) => void) => {
      const {
        receiverName,
        receiverMobile,
        deliveryAddress: address,
      } = form.getFieldsValue();
      if (
        (receiverName || receiverMobile || address) &&
        (!receiverName || !receiverMobile || !address)
      ) {
        callback('请补充完整收件人信息');
      } else {
        callback();
      }
    },
    [],
  );

  const getOrderAddBaseInfoConfigs = useMemo(() => {
    return [
      {
        key: 'saleOrderId',
        formItemProps: {
          label: '内部订单号',
          name: 'saleOrderId',
          className: 'mb-sm',
          rules: [{ required: true, message: '请选择订单' }],
        },
        renderFormItem: () => (
          <InputMore
            modalContent={() => (
              <AddGoodsSkuFromOrderModal
                title="选择订单"
                onSelectedBillsChange={(v) => {
                  if (!v.length) {
                    msgWarn('请选择订单');
                    return;
                  }

                  const {
                    id,
                    warehouseId,
                    warehouseName,
                    receiverName,
                    receiverMobile,
                    ...rest
                  } = v[0];
                  if (formValues?.saleOrderId !== id) {
                    form?.resetFields();
                  }

                  form?.setFieldsValue({
                    saleOrderId: id,
                    afterSaleType: afterSaleType || 1,
                    refundWarehouseId: warehouseId,
                    refundWarehouseName: warehouseName,
                    ...rest,
                  });
                  setFormValuesCb({
                    saleOrderId: v[0]?.id || '',
                    afterSaleType: afterSaleType || 1,
                    ...v[0],
                  } as AfterSaleFormType);
                }}
                onSelectedGoodsChange={(v) => onSelectGoods?.(v)}
                mainTableOptions={{
                  show: true,
                  defaultCheckedKeys: saleOrderId ? [saleOrderId] : [],
                  rowSelectionType: 'radio',
                }}
                subTableOptions={{
                  show: true,
                  defaultCheckedKeys: dataSource?.map(
                    (el: AfterSaleOrderCreateUpdateDetailRequest) => el.id,
                  ),
                  rowSelectionType: 'checkbox',
                }}
                trigger={
                  <Flex
                    align="center"
                    justify="center"
                    className="h-full w-full"
                  >
                    ...
                  </Flex>
                }
              />
            )}
          />
        ),
      },
      {
        key: 'extOriginalId',
        type: 'plainText',
        formItemProps: {
          label: '线上交易单号',
          name: 'extOriginalId',
        },
      },
      {
        key: 'shopName',
        formItemProps: {
          label: '店铺',
          name: 'shopName',
        },
        renderFormItem: () => {
          return (
            <Flex gap={4}>
              {formValues?.shopLogoUrl ? (
                <Avatar
                  size={20}
                  src={<img src={formValues?.shopLogoUrl || ''} alt="" />}
                />
              ) : null}

              <span className="text-description">
                {formValues?.shopName || '-'}
              </span>
            </Flex>
          );
        },
      },
      {
        key: 'logisticsName',
        type: 'plainText',
        formItemProps: {
          label: '物流公司',
          name: 'logisticsName',
        },
      },
      {
        key: 'logisticsNo',
        type: 'plainText',
        formItemProps: {
          label: '物流单号',
          name: 'logisticsNo',
        },
      },
      {
        key: 'businessType',
        formItemProps: {
          label: '业务类型',
          name: 'businessType',
        },
        renderFormItem: () => {
          const { businessType } = form.getFieldsValue();
          return (
            <>
              {enumToOptions(OrderBillBusinessTypeMap).find(
                (el) => el?.value === businessType,
              )?.label || '-'}
            </>
          );
        },
      },
      {
        type: 'plainText',
        formItemProps: {
          label: '供应商',
          name: 'supplierName',
        },
      },
      {
        type: 'plainText',
        formItemProps: {
          label: '分销商',
          name: 'distributorName',
        },
      },
      {
        type: 'plainText',
        formItemProps: {
          label: '付款时间',
          name: 'payTime',
        },
      },
      {
        key: 'afterSaleType',
        type: 'radio',
        formItemProps: {
          label: '售后类型',
          name: 'afterSaleType',
          rules: [{ required: true, message: '请选择售后类型' }],
        },
        fieldProps: {
          block: true,
          optionType: 'button',
          size: 'small',
          options: enumToOptions(AfterSalesBusinessType),
        },
      },
    ];
  }, [
    form,
    afterSaleType,
    saleOrderId,
    formValues?.shopLogoUrl,
    formValues?.shopName,
    onSelectGoods,
    dataSource,
    setFormValuesCb,
  ]);

  const getColumns = useCallback(() => {
    const basicColumns: EditableTableProps<AfterSaleOrderCreateUpdateDetailRequest>['columns'] =
      [
        {
          title: '商品名称',
          dataIndex: 'goodsName',
          key: 'goodsName',
          valueType: 'plainText',
          width: 160,
          render: (record) => {
            return (
              <Flex>
                {record?.combine ? (
                  <CombinedGoodsModal id={record?.goodsId || ''} />
                ) : null}
                {record?.giftFlag ? (
                  <Tag className="mr-xxs" color="#FA8C16">
                    赠
                  </Tag>
                ) : null}
                <Typography.Text ellipsis>{record?.goodsName}</Typography.Text>
              </Flex>
            );
          },
        },
        {
          title: '图片',
          dataIndex: 'goodsPic',
          key: 'goodsPic',
          width: 60,
          renderFormItem: (record) => {
            return (
              <GoodsImage
                preview={false}
                width={24}
                height={24}
                src={record?.goodsPic || ''}
                className="!rounded-lg !border-none"
              />
            );
          },
        },
        {
          title: '商品编码',
          key: 'goodsCode',
          dataIndex: 'goodsCode',
          valueType: 'plainText',
          width: 120,
        },
        {
          title: '规格编码',
          key: 'skuCode',
          dataIndex: 'skuCode',
          valueType: 'plainText',
          width: 120,
        },
        {
          title: '规格',
          key: 'skuName',
          dataIndex: 'skuName',
          valueType: 'plainText',
          width: 120,
        },
        {
          title: 'ISBN',
          key: 'isbn',
          dataIndex: 'isbn',
          valueType: 'plainText',
          width: 120,
        },
        {
          title: '出版社',
          key: 'publisherName',
          dataIndex: 'publisherName',
          valueType: 'plainText',
          width: 120,
        },
        {
          title: '定价',
          key: 'pricingPrice',
          dataIndex: 'pricingPrice',
          valueType: 'plainText',
          width: 120,
        },
        {
          title: '品牌',
          key: 'brandName',
          dataIndex: 'brandName',
          valueType: 'plainText',
          width: 120,
        },
        {
          title: '类型',
          key: 'afterSaleType',
          dataIndex: 'afterSaleType',
          width: 120,
          render: (record) => {
            const { afterSaleType } = record || {};
            return (
              <Tag
                color={
                  AfterSalesBusinessTypeColor[
                    afterSaleType as AfterSalesBusinessType
                  ]
                }
              >
                {
                  enumToOptions(AfterSalesBusinessType).find(
                    (el) => el?.value === afterSaleType,
                  )?.label
                }
              </Tag>
            );
          },
        },
        {
          title: '下单数量',
          key: 'goodsNum',
          dataIndex: 'goodsNum',
          valueType: 'plainText',
          fieldProps: {
            min: 0,
          },
          width: 120,
        },
        {
          title: '售后单价',
          key: 'price',
          dataIndex: 'price',
          valueType: 'plainText',
          fieldProps: {
            min: 0,
          },
          width: 120,
        },
      ];
    const actionColumns: EditableTableProps<AfterSaleOrderCreateUpdateDetailRequest>['columns'] =
      [
        {
          title: '操作',
          key: 'action',
          width: 80,
          fixed: 'right',
          render: (record) => (
            <Typography.Link onClick={() => removeGoods([record.id])}>
              删除
            </Typography.Link>
          ),
        },
      ];
    if (afterSaleType === AfterSalesBusinessType.退货) {
      return [
        ...basicColumns,
        {
          title: '退货数量',
          key: 'applyNum',
          dataIndex: 'applyNum',
          valueType: 'inputNumberInt',
          fieldProps: {
            min: 0,
          },
          formItemProps: {
            rules: [
              {
                required: true,
                message: '请输入退货数量',
              },
            ],
            rulesWithRecord: (record: CommonRecord) => {
              return [
                {
                  validator: (_: any, value: number) => {
                    const canReturnNum =
                      Number(record?.goodsNum || 0) -
                      Number(record?.hasAfterApplyNum || 0);
                    if (value < 0) {
                      return Promise.reject(new Error('申请数量不能小于0'));
                    }
                    if (value > Number(canReturnNum)) {
                      return Promise.reject(
                        new Error(
                          `已申请数量为${record?.hasAfterApplyNum || 0}，本次最大申请数量${canReturnNum}`,
                        ),
                      );
                    }
                    return Promise.resolve();
                  },
                },
              ];
            },
          },
          width: 120,
        },
        {
          title: '退货金额',
          key: 'amount',
          dataIndex: 'amount',
          valueType: 'plainText',
          width: 120,
        },
        {
          title: '销售金额',
          key: 'totalAmount',
          dataIndex: 'totalAmount',
          valueType: 'plainText',
          width: 80,
        },
        {
          title: '采购金额',
          key: 'purchaseAmount',
          dataIndex: 'purchaseAmount',
          valueType: 'plainText',
          width: 120,
        },
        ...actionColumns,
      ];
    }
    if (afterSaleType === AfterSalesBusinessType.换货) {
      return [
        ...basicColumns,
        {
          title: '退换数量',
          key: 'applyNum',
          dataIndex: 'applyNum',
          valueType: 'inputNumberInt',
          fieldProps: {
            min: 0,
          },
          formItemProps: {
            rules: [
              {
                required: true,
                message: '请输入退换数量',
              },
            ],
            rulesWithRecord: (record: CommonRecord) => {
              return [
                {
                  validator: (_: any, value: number) => {
                    const canReturnNum =
                      Number(record?.goodsNum || 0) -
                      Number(record?.hasAfterApplyNum || 0);
                    if (value < 0) {
                      return Promise.reject(new Error('申请数量不能小于0'));
                    }
                    if (value > Number(canReturnNum)) {
                      return Promise.reject(
                        new Error(
                          `已申请数量为${record?.hasAfterApplyNum || 0}，本次最大申请数量${canReturnNum}`,
                        ),
                      );
                    }
                    return Promise.resolve();
                  },
                },
              ];
            },
          },
          width: 130,
        },
        {
          title: '退换金额',
          key: 'amount',
          dataIndex: 'amount',
          valueType: 'plainText',
          width: 120,
        },
        {
          title: '销售金额',
          key: 'totalAmount',
          dataIndex: 'totalAmount',
          valueType: 'plainText',
          width: 80,
        },
        {
          title: '采购金额',
          key: 'purchaseAmount',
          dataIndex: 'purchaseAmount',
          valueType: 'plainText',
          width: 120,
        },
        ...actionColumns,
      ];
    }
    if (afterSaleType === AfterSalesBusinessType.补发) {
      return [
        ...basicColumns,
        {
          title: '补发数量',
          key: 'applyNum',
          dataIndex: 'applyNum',
          valueType: 'inputNumberInt',
          fieldProps: {
            min: 0,
          },
          formItemProps: {
            rules: [{ required: true, message: '请输入补发数量' }],
          },
          width: 120,
        },
        {
          title: '补发金额',
          key: 'amount',
          dataIndex: 'amount',
          valueType: 'plainText',
          width: 120,
        },
        {
          title: '销售金额',
          key: 'totalAmount',
          dataIndex: 'totalAmount',
          valueType: 'plainText',
          width: 80,
        },
        ...actionColumns,
      ];
    }
    if (afterSaleType === AfterSalesBusinessType.退款) {
      return [
        ...basicColumns,
        {
          title: '退款数量',
          key: 'applyNum',
          dataIndex: 'applyNum',
          valueType: 'inputNumberInt',
          fieldProps: {
            min: 0,
          },
          formItemProps: {
            rules: [{ required: true, message: '请输入退款数量' }],
            rulesWithRecord: (record: CommonRecord) => {
              return [
                {
                  validator: (_: any, value: number) => {
                    const canReturnNum =
                      Number(record?.goodsNum || 0) -
                      Number(record?.hasAfterApplyNum || 0);
                    if (value < 0) {
                      return Promise.reject(new Error('申请数量不能小于0'));
                    }
                    if (value > Number(canReturnNum)) {
                      return Promise.reject(
                        new Error(
                          `已申请数量为${record?.hasAfterApplyNum || 0}，本次最大申请数量${canReturnNum}`,
                        ),
                      );
                    }
                    return Promise.resolve();
                  },
                },
              ];
            },
          },
          width: 120,
        },
        {
          title: '退款金额',
          key: 'amount',
          dataIndex: 'amount',
          valueType: 'plainText',
          width: 120,
          formItemProps: {
            rules: [{ required: true, message: '请输入退款金额' }],
          },
          render: (
            record: AfterSaleOrderCreateUpdateDetailRequest,
            index: number,
          ) => {
            const { amount } = record;
            return (
              <TextItemEdit
                value={`${amount}` || ''}
                extraParams={{
                  afterSaleOrderIdList: [record?.id],
                  returnLogisticsName: record?.returnLogisticsName,
                }}
                customSave={(v: any) => {
                  handleChangeRefundAmount(
                    record,
                    index,
                    Number(v?.amount) || 0,
                  );
                  return Promise.resolve(true);
                }}
                textClassName="text-description"
                configForm={{
                  type: 'inputNumber',
                  fieldProps: {
                    ...inputNumberProps.price,
                    min: 0,
                    max: maxInputDecimal,
                    placeholder: '退款金额',
                  },
                  formItemProps: {
                    name: 'amount',
                  },
                }}
              />
            );
          },
        },
        {
          title: '销售单价',
          key: 'salePrice',
          valueType: 'plainText',
          width: 'number',
        },
        {
          title: '销售金额',
          key: 'totalAmount',
          dataIndex: 'totalAmount',
          valueType: 'plainText',
          width: 80,
        },
        ...actionColumns,
      ];
    }
    return [basicColumns];
  }, [afterSaleType, removeGoods, handleChangeRefundAmount]);

  const getOrderAddOthersConfigs = useMemo(() => {
    return [
      {
        key: 'returnLogisticsName',
        type: 'returnLogisticsCompanySelect',
        fieldProps: {
          labelInValue: true,
          fieldNames: {
            label: 'logisticsName',
            value: 'logisticsName',
            key: 'logisticsName',
          },
        },
        formItemProps: {
          label: '退货物流公司',
          name: 'returnLogisticsName',
          rules: [
            afterSaleType === AfterSalesBusinessType.退款
              ? { required: false }
              : { required: true, message: '请选择退货物流公司' },
          ],
        },
        itemUpdateConfig: {
          shouldUpdateKey: 'afterSaleType',
          showCondition: [
            {
              fieldKey: 'afterSaleType',
              fieldValue: [
                AfterSalesBusinessType['退货'],
                AfterSalesBusinessType['退款'],
                AfterSalesBusinessType['换货'],
              ],
            },
          ],
        },
      },
      {
        key: 'returnLogisticsNo',
        type: 'input',
        formItemProps: {
          label: '退货物流单号',
          name: 'returnLogisticsNo',
          rules:
            afterSaleType === AfterSalesBusinessType.退款
              ? []
              : [{ required: true, message: '请输入退货物流单号' }],
        },
        itemUpdateConfig: {
          shouldUpdateKey: 'afterSaleType',
          showCondition: [
            {
              fieldKey: 'afterSaleType',
              fieldValue: [
                AfterSalesBusinessType['退货'],
                AfterSalesBusinessType['退款'],
                AfterSalesBusinessType['换货'],
              ],
            },
          ],
        },
      },
      {
        key: 'refundReason',
        type: 'apiSelect',
        fieldProps: {
          allowClear: false,
          labelInValue: true,
          request: omsAfterSaleStrategyRefundReasonList,
          fieldNames: {
            label: 'value',
            value: 'value',
          },
        },
        formItemProps: {
          label: '售后原因',
          name: 'refundReason',
          rules: [{ required: true, message: '请选择售后原因' }],
        },
      },
      {
        key: 'refundWarehouseId',
        type: 'warehouseSelect',
        fieldProps: {
          allowClear: false,
          labelInValue: false,
          fieldNames: {
            label: 'label',
            value: 'value',
          },
          params: {
            permission: false,
          },
        },
        formItemProps: {
          label: '退货仓库',
          name: 'refundWarehouseId',
          rules: [{ required: true, message: '请选择退货仓库' }],
        },
        itemUpdateConfig: {
          shouldUpdateKey: 'afterSaleType',
          showCondition: [
            {
              fieldKey: 'afterSaleType',
              fieldValue: [
                AfterSalesBusinessType['退货'],
                AfterSalesBusinessType['换货'],
              ],
            },
          ],
        },
      },
      {
        key: 'receiverName',
        type: 'input',
        fieldProps: {
          maxLength: 50,
        },
        itemUpdateConfig: {
          shouldUpdateKey: 'afterSaleType',
          showCondition: [
            {
              fieldKey: 'afterSaleType',
              fieldValue: [
                AfterSalesBusinessType['补发'],
                AfterSalesBusinessType['换货'],
              ],
            },
          ],
        },
        formItemProps: {
          label: '收件人姓名',
          name: 'receiverName',
          className: '!mb-xs',
          rules: [
            () => ({
              validator: validateReceiverInfo,
              trigger: 'onBlur',
            }),
          ],
        },
      },
      {
        key: 'receiverMobile',
        type: 'input',
        itemUpdateConfig: {
          shouldUpdateKey: 'afterSaleType',
          showCondition: [
            {
              fieldKey: 'afterSaleType',
              fieldValue: [
                AfterSalesBusinessType['补发'],
                AfterSalesBusinessType['换货'],
              ],
            },
          ],
        },
        formItemProps: {
          label: '收件人手机',
          name: 'receiverMobile',
          className: '!mb-xs',
          rules: [
            () => ({
              validator: validateReceiverInfo,
              trigger: 'onBlur',
            }),
            {
              pattern: mobileRegexp,
              message: '手机号格式不正确',
            },
          ],
        },
      },
      {
        key: 'address',
        colProps: {
          span: 16,
        },
        formItemProps: {
          label: '收件人地址',
          name: ['deliveryAddress'],
          rules: [
            () => ({
              validator: validateReceiverInfo,
              trigger: 'onBlur',
            }),
          ],
        },
        itemUpdateConfig: {
          shouldUpdateKey: 'afterSaleType',
          showCondition: [
            {
              fieldKey: 'afterSaleType',
              fieldValue: [
                AfterSalesBusinessType['补发'],
                AfterSalesBusinessType['换货'],
              ],
            },
          ],
        },
        renderFormItem: () => (
          <AreaCompact
            addressName={['deliveryAddress', 'addressId']}
            addressCascaderRef={addressCascaderRef}
            detailName={['deliveryAddress', 'receiverAddress']}
            detailNameLen={200}
            labelInValue
          />
        ),
      },
      {
        key: 'remark',
        renderFormItem: () => {
          return (
            <AfterSaleRemarkAttachment
              viewMode={AfterSaleViewModeEnum.我的视角}
              onOk={(v) =>
                handleAfterSaleRemarkAttachment?.(
                  v,
                  AfterSaleViewModeEnum.我的视角,
                )
              }
              hasAuth
            />
          );
        },
        formItemProps: {
          label: '售后附件/备注',
          name: 'remark',
        },
      },
      {
        key: 'supplierDistributionRemark',
        renderFormItem: () => {
          return (
            <AfterSaleRemarkAttachment
              viewMode={AfterSaleViewModeEnum.供分视角}
              onOk={(v) =>
                handleAfterSaleRemarkAttachment?.(
                  v,
                  AfterSaleViewModeEnum.供分视角,
                )
              }
              saleOrderId={formValues?.id}
              afterOpen={() => {
                if (formValues?.businessType) {
                  const isOnlineOrder = getIsOnlineOrder(
                    formValues?.businessType,
                    formValues?.qiMenSupplierFlag ?? false,
                    formValues?.rootOrderId !== '-1',
                  );
                  return isOnlineOrder;
                }
                return false;
              }}
              hasAuth
            />
          );
        },
        formItemProps: {
          label: '供分销附件/备注',
          name: 'supplierDistributionRemark',
          labelCol: {
            span: 6,
          },
        },
      },
    ];
  }, [
    addressCascaderRef,
    validateReceiverInfo,
    afterSaleType,
    formValues?.businessType,
    formValues?.qiMenSupplierFlag,
    formValues?.rootOrderId,
    handleAfterSaleRemarkAttachment,
    formValues?.id,
  ]);

  /***金额信息 */
  const getAmountConfigs = useMemo(() => {
    return [
      {
        key: 'refoundAmount',
        type: 'plainText',
        formItemProps: {
          label: '应退金额',
          name: 'refoundAmount',
        },
      },
      {
        key: 'refundPostAmount',
        type: 'inputNumber',
        formItemProps: {
          label: '退运费金额',
          name: 'refundPostAmount',
        },
        fieldProps: {
          ...inputNumberProps.price,
          min: 0,
          max: maxInputDecimal,
          placeholder: '退运费金额',
        },
      },
      {
        key: 'refundActualAmount',
        type: 'plainText',
        fieldProps: {
          value: 0,
        },
        formItemProps: {
          label: '实退金额',
          name: 'refundActualAmount',
          initialValue: 0,
        },
      },
    ];
  }, []);
  return {
    getColumns,
    getOrderAddBaseInfoConfigs,
    getOrderAddOthersConfigs,
    getAmountConfigs,
  };
};
