import type { IOption } from '@slt/base-ui';
import type {
  BillBusinessType,
  BillTypeEnum,
  FinanceBillStatusType,
  SynergyStatusType,
} from '@slt/utils';
import type { BusinessBillDTO } from './account.interface';

export type BusinessBillListDTO = {
  billId: string;
  /** 业务单据id */
  businessBillId: string;
  /** 单据时间 */
  businessBillTime: string;
  /** 业务类型 */
  businessType: string;
  /** 客户id */
  customerId: number;
  /** 分配预收款金额 */
  receiptedAmount: number;
  /** 备注 */
  remark: string;
  settlementAmount?: number;
  arrearsAmount?: number;
  billType?: string;
  businessBillNo?: string;
  /** 业务员id */
  salesManId?: string;
  /** 部门id */
  departmentId?: string;
};

export type BillDetailListParams = {
  /** 账户id */
  accountId?: string | number;
  /** 结算方式 */
  settlementType?: string | number;
  /** 金额 */
  amount?: string | number;
  /** 备注 */
  remark?: string;
};

export type BillDetailListDTO = BillDetailListParams & {
  key: string;
  /** 编辑单据时接口返回的明细数据id */
  id?: string;
  /** 账户名称 */
  accountName?: string;
  /** 帐户 */
  account?: IOption;
  /** 账户编码 */
  settleAccountNo?: string;
  /** 结算方式(列表下拉) */
  settleType?: IOption;
  /** 结算方式名称 */
  settlementTypeText?: string;
};

export type FinanceAdvanceFinanceBillSaveParams = {
  id?: string;
  /** 单据编号 */
  billNo?: string;
  /** 单据时间 */
  billDateTime: string;
  /** 是否确认，1-草稿 2-已确认 */
  billStatus: number;
  /** 单据类型，预收款单-YS18000 预付款单-YF19000 */
  billType: string;
  /** 业务类型，预收款-28 预付款-29 */
  businessType: string;
  /** 客户id */
  customerId: string | number;
  /** 部门id */
  departmentId?: string;
  /** 业务员id */
  salesManId?: string | number;
  /** 备注 */
  remark?: string;
  /** 关联单据列表 */
  businessBillList?: BusinessBillListDTO[];
  /** 明细信息 */
  detailList?: BillDetailListParams[];
  /** 合计金额 */
  totalAmount?: number | string;
};

export type FinanceBillSaveCheckErrorDTO = {
  type: number;
  errorData: { errorMsg: string };
};

export type FinanceAdvanceBillSaveCheckDTO = {
  continues: boolean;
  limitedResultList?: FinanceBillSaveCheckErrorDTO[];
};

export type FinanceAdvanceFinanceBillGetDTO = {
  /** 单据明细 */
  detailList: Omit<BillDetailListDTO, 'key'>[];
  /** 单据日期 */
  billDateTime?: string;
  /** 单据编号 */
  billNo?: string;
  /** 供应商/客户 */
  customerId?: string;
  customerName?: string;
  /** 业务员 */
  salesManId?: string;
  salesManName?: string;
  /** 单据备注 */
  remark?: string;
  /** 单据类型 */
  billType?: string;
  /** 单据状态(已确认、草稿) */
  billStatus?: number;
  /**  */
  businessType?: string;
  /** 制单人名称 */
  createUserName?: string;
  /** 单据创建时间 */
  gmtCreate?: string;
  /** 单据修改时间 */
  gmtModified?: string;
  /** 部门 */
  departmentCascadeIdList?: string[];
  /** 协同状态 */
  synergyStatus?: SynergyStatusType;
  /** 协同单据-审核拒绝原因 */
  rejectReason?: string;
  businessBillList?: BusinessBillListDTO[];
};

export type ExpenseAndOtherIncomeBillDetailListParams = {
  rowNo?: number;
  tempId: string;
  account?: IOption;
  accountId?: string | number;
  accountName?: string;
  accountNo?: string;
  amount?: number;
  remark?: string;
  // settlementType?: string;
};

/** 费用单、其他收入单新增编辑参数 */
export type ExpenseAndOtherIncomeBillParams = {
  id?: string;
  businessType?: string;
  billType?: BillTypeEnum;
  customerId?: string | number;
  salesManId?: string | number;
  settleAccountId?: string | number;
  settlementType?: string | number;
  billStatus?: FinanceBillStatusType;
  billDateTime?: string;
  detailList?: ExpenseAndOtherIncomeBillDetailListParams[];
  departmentId?: string;
  settlementTime?: string;
};

/** 收付款单、预收付款单 审核及审核前校验 参数 */
export type FinanceBillReviewParams = {
  id: string;
  /** 2-审核通过; 3-审核拒绝 */
  status: number;
  rejectReason?: string;
};

export type FinanceBillQueryOrderDTO = {
  billNo: string;
  businessType?: BillBusinessType;
  billType?: BillTypeEnum;
  billDateTime?: string;
  customerId?: string;
  customerName?: string;
  salesManId?: string;
  salesManName?: string;
  departmentCascadeIdList?: string[];
  settleAccountId?: string;
  settleAccountName?: string;
  settlementType?: number;
  settlementTypeName?: string;
  settlementTime?: string;
  merchantType?: number;
  detailList?: ExpenseAndOtherIncomeBillDetailListParams[];
  businessBillList?: BusinessBillDTO[];
};
