import { isNil } from 'lodash-es';
import {
  Typography,
  type TableColumnType,
  ColumnWidth,
  type EditableTableProps,
  TypographyV2,
} from '@slt/base-ui';
import type { CommonRecord } from '@slt/base-utils';
import type { CheckRecordDTO, NewErrorDataDTO } from './types';
import type { BusinessBillListDTO } from '@slt/services';
import { PLACEHOLDER } from '@slt/utils';

const commonFormItemProps = {
  rules: [
    {
      validator: async (_: unknown, value?: number) => {
        if (!isNil(value)) {
          return Promise.resolve();
        } else {
          return Promise.reject(new Error('请输入'));
        }
      },
    },
  ],
  validateTrigger: ['onChange', 'onBlur'],
};

/**type=2,单据有关联 */
export const getTypeTwoColumns = (props: {
  handleLink?: (record: CheckRecordDTO) => void;
  needOperation?: boolean;
  showIndex?: boolean;
}): TableColumnType<CommonRecord>[] => {
  const { handleLink, needOperation = true, showIndex } = props;
  return [
    {
      title: '序号',
      key: 'index',
      dataIndex: 'index',
      hidden: !showIndex,
      render: (_, record, index) => {
        return index + 1;
      },
    },
    {
      width: 200,
      title: '单据编号',
      dataIndex: 'billNo',
      key: 'billNo',
    },
    {
      width: 145,
      title: '单据类型',
      dataIndex: 'resourceTypeText',
      key: 'resourceTypeText',
    },
    {
      width: 145,
      title: '单据状态',
      dataIndex: 'resourceStatus',
      key: 'resourceStatus',
    },
    {
      width: 80,
      title: '操作',
      dataIndex: 'operate',
      key: 'operate',
      hidden: !needOperation,
      render: (_, record) => {
        return (
          <Typography.Link
            onClick={() => {
              handleLink?.(record as CheckRecordDTO);
            }}
          >
            前往删除
          </Typography.Link>
        );
      },
    },
  ];
};

/**type=3,商品会导致负库存  */
export const getTypeThreeColumns = (): TableColumnType<CommonRecord>[] => {
  return [
    {
      width: 60,
      title: '行号',
      dataIndex: 'rowNo',
      key: 'rowNo',
    },
    {
      width: 110,
      title: '商品编码',
      dataIndex: 'goodsCode',
      key: 'goodsCode',
    },
    {
      width: 145,
      title: '商品名称',
      dataIndex: 'goodsName',
      key: 'goodsName',
    },
    {
      width: 110,
      title: '规格编码',
      dataIndex: 'skuCode',
      key: 'skuCode',
    },
    {
      width: 145,
      title: '规格名称',
      dataIndex: 'skuName',
      key: 'skuName',
    },
  ];
};

/**type=6,单据超额结算  */
export const getTypeSixColumns = (): TableColumnType<CommonRecord>[] => {
  return [
    {
      width: 60,
      title: '行号',
      dataIndex: 'rowNo',
      key: 'rowNo',
    },
    {
      width: 200,
      title: '单据编号',
      dataIndex: 'billNo',
      key: 'billNo',
    },
    {
      width: 145,
      title: '单据类型',
      dataIndex: 'billType',
      key: 'billType',
    },
    {
      width: 120,
      title: '待结算金额',
      dataIndex: 'waitSettlementAmount',
      key: 'waitSettlementAmount',
    },
  ];
};

/**type=7,单据超额结算  */
export const getTypeSevenColumns = (props: {
  handleLink?: (record: { billNo: string; billType: string }) => void;
  errorMsg: string;
}): TableColumnType<BusinessBillListDTO>[] => {
  const { handleLink, errorMsg } = props || {};
  // 文案
  const financeBillListsTitle = errorMsg.includes('待审核收款单')
    ? '所在待审核收款单'
    : '所在待审核付款单';
  return [
    {
      width: 60,
      title: '行号',
      dataIndex: 'rowNo',
      key: 'rowNo',
    },
    {
      width: 200,
      title: '单据编号',
      dataIndex: 'businessBillNo',
      key: 'businessBillNo',
    },
    {
      width: 145,
      title: '单据类型',
      dataIndex: 'businessBillTypeText',
      key: 'businessBillTypeText',
    },
    {
      width: 200,
      title: financeBillListsTitle,
      dataIndex: 'financeBillLists',
      key: 'financeBillLists',
      batchCopy: true,
      render: (_, record: BusinessBillListDTO) => {
        const { financeBillList } = record;

        if (financeBillList?.length) {
          return (
            <TypographyV2.Link className="flex flex-col">
              {financeBillList.map((item, index) => (
                <TypographyV2.Link
                  onClick={() => {
                    handleLink?.({
                      billNo: item.billNo,
                      billType: item.billType,
                    });
                  }}
                  key={index}
                  copyable
                >
                  {item.billNo}
                </TypographyV2.Link>
              ))}
            </TypographyV2.Link>
          );
        } else {
          return PLACEHOLDER;
        }
      },
    },
  ];
};

/**type=8, */
export const getTypeEightColumns = () => {
  return [
    {
      width: ColumnWidth.RowIndex,
      title: '行号',
      dataIndex: 'rowNo',
      key: 'rowNo',
      editable: false,
    },
    {
      width: ColumnWidth.BillNo,
      title: '单据编号',
      dataIndex: 'billNo',
      key: 'billNo',
      editable: false,
    },
    {
      width: ColumnWidth.Remark,
      title: '单据类型',
      dataIndex: 'billTypeText',
      key: 'billTypeText',
      editable: false,
    },
    {
      width: ColumnWidth.BillNo,
      title: '原始订单号',
      dataIndex: 'mallOrderId',
      key: 'mallOrderId',
      editable: false,
    },
  ];
};

/**type=31, 商品没有出库成本价，需要填写固定成本价 */
export const getTypeThirtyOneColumns =
  (): EditableTableProps<NewErrorDataDTO>['columns'] => {
    return [
      {
        width: 60,
        title: '行号',
        dataIndex: 'rowNo',
        key: 'rowNo',
        editable: false,
      },
      {
        width: ColumnWidth.Remark,
        title: '商品编码',
        dataIndex: 'goodsCode',
        key: 'goodsCode',
        editable: false,
        ellipsis: true,
      },
      {
        width: ColumnWidth.Remark,
        title: '商品名称',
        dataIndex: 'goodsName',
        key: 'goodsName',
        editable: false,
        ellipsis: true,
      },
      {
        width: ColumnWidth.Remark,
        title: '规格编码',
        dataIndex: 'skuCode',
        key: 'skuCode',
        editable: false,
        ellipsis: true,
      },
      {
        width: ColumnWidth.Remark,
        title: '规格名称',
        dataIndex: 'skuName',
        key: 'skuName',
        editable: false,
        ellipsis: true,
      },
      {
        width: ColumnWidth.Remark,
        title: '固定成本价',
        dataIndex: 'handWriteCostPrice',
        key: 'handWriteCostPrice',
        valueType: 'inputNumberUnitPrice',
        fieldProps: {
          min: 0,
        },
        formItemProps: {
          ...commonFormItemProps,
        },
      },
    ];
  };
