import {
  Button,
  Flex,
  Form,
  GoodsImage,
  Modal,
  msgInfo,
  Table,
  Tabs,
  Tag,
  Typography,
  Image,
  msgSuccess,
} from '@slt/base-ui';
import { BizTable, type BizTableRef } from '@slt/biz-ui';
import { useQuery } from '@slt/net-query';
import {
  basicPlatformRolloutGoodsFixedDataUpdate,
  basicPlatformRolloutGoodsRecordQuery,
  PlatformRolloutWayEnum,
} from '@slt/services';
import { useUpdateEffect } from 'ahooks';
import {
  forwardRef,
  Fragment,
  useCallback,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { usePlatformRolloutModalContext } from './contexts';
import { DistributionTypeEnum, PlatformTypeEnum } from '@slt/utils';
import type { CommonRecord } from '@slt/base-utils';
import PriceAndInventoryModal, {
  type InventoryAndPriceDataItem,
} from './rolloutSettings/PriceAndInventoryModal';
import { net } from '@slt/net';
import { convertToFixedSpuData } from './executeRollout/util';
import type { EditPriceSource } from './types';

/**
 * 更新编辑价格源数据
 * @param prev 之前的价格源数据
 * @param platformType 平台类型
 * @param id 商品ID
 * @param dataList 价格数据列表
 * @returns 更新后的价格源数据
 */
export const updateEditPriceSource = (
  prev: EditPriceSource,
  platformType: number,
  id: string | number,
  dataList: InventoryAndPriceDataItem[],
) => {
  const newEditPriceSource = { ...prev };

  if (!platformType) {
    return prev;
  }
  // 检查是否有相同的平台key
  if (newEditPriceSource[platformType]) {
    // 检查是否有相同的id
    const existingIndex = newEditPriceSource[platformType].findIndex(
      (item: any) => item[id] !== undefined,
    );

    if (existingIndex !== -1) {
      // 如果有相同id，直接覆盖
      newEditPriceSource[platformType][existingIndex] = {
        [id]: dataList,
      };
    } else {
      // 如果没有相同id，添加新的
      newEditPriceSource[platformType].push({
        [id]: dataList,
      });
    }
  } else {
    // 如果没有相同的平台key，则添加新的key
    newEditPriceSource[platformType] = [
      {
        [id]: dataList,
      },
    ];
  }

  return newEditPriceSource;
};

type UpdateRolloutSettingsModalRef = {
  showModal: () => void;
  closeModal: () => void;
};

interface ConfirmUpdateStoreProductsProps {
  rolloutWay: number;
  targetIds: string[];
  updateRolloutSettingsModalRef: React.RefObject<UpdateRolloutSettingsModalRef>;
}

enum UpdateRolloutConfirmTab {
  '可更新店铺商品' = '1',
  '不可更新店铺商品' = '2',
}

const ConfirmUpdateStoreProducts = forwardRef<
  UpdateRolloutSettingsModalRef,
  ConfirmUpdateStoreProductsProps
>((props, ref) => {
  const { rolloutWay, updateRolloutSettingsModalRef } = props;
  const tableRef = useRef<BizTableRef<CommonRecord>>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [originalDataSource, setOriginalDataSource] = useState([]);
  const [activeTabKey, setActiveTabKey] = useState<string>(
    UpdateRolloutConfirmTab.可更新店铺商品,
  );
  const [form] = Form.useForm<CommonRecord>();
  const {
    selectedShops,
    setConfirmUpdateProduct,
    updateRolloutRecordedList,
    supportedPlatforms,
    setEditPriceSource,
    setUpdateRolloutRecordedList,
  } = usePlatformRolloutModalContext();

  const updateDataLength = useRef(0);
  const noUpdateDataLength = useRef(0);

  const setSuccessDataList = useCallback((id: string | number) => {
    setUpdateRolloutRecordedList((prev) => {
      const newUpdateRolloutRecordedList = [...prev];
      const noUpdateData = newUpdateRolloutRecordedList.filter(
        (item) =>
          item.reason === '商品铺货售价不在供应商控价范围内' &&
          item.platformDistributionId === id,
      );
      const recordIds = new Set(noUpdateData.map((item) => item.recordId));

      return newUpdateRolloutRecordedList.map((item) => {
        if (recordIds.has(item.recordId)) {
          const { reason, ...rest } = item;
          return rest;
        }

        return item;
      });
    });
  }, []);

  const priceAndInventoryModalSubmit = useCallback(
    (
      dataList: InventoryAndPriceDataItem[],
      id: string | number,
      platformType: number,
    ) => {
      const fixedSpuDatas = convertToFixedSpuData(dataList, platformType);
      return new Promise<void>((resolve, reject) => {
        if (rolloutWay !== PlatformRolloutWayEnum.通用铺货资料) {
          setSuccessDataList(id);

          //根据平台以及商品id临时存储数据
          setEditPriceSource((prev) => {
            return updateEditPriceSource(prev, platformType, id, dataList);
          });
          resolve();
          return;
        }

        net
          .fetch(
            basicPlatformRolloutGoodsFixedDataUpdate({
              platformDistributionSpuIds: [id],
              platformType: Number(platformType),
              fixedSpuDatas,
            }),
          )
          .then((res) => {
            const { data, success, message } = res;
            if (!data && !success) {
              reject(new Error(`${message}`));
              return;
            }

            setSuccessDataList(id);
            msgSuccess('修改成功');

            resolve();
          })
          .catch((error) => {
            reject(new Error(`${error}`));
          });
      });
    },
    [rolloutWay, setEditPriceSource, setSuccessDataList],
  );

  const columns = useMemo(() => {
    return [
      {
        title: '主图',
        key: 'url',
        render: (img?: string) => {
          if (!img) {
            return null;
          }

          return <GoodsImage src={img} size="small" />;
        },
      },
      {
        title: '商品编码',
        key: 'showCode',
        render: (text, record) => {
          if (!text) {
            return null;
          }

          return (
            <Flex flex={1} title={text} align="center">
              {record.distributionType === DistributionTypeEnum.Distribution ? (
                <Tag color="blue">分销</Tag>
              ) : null}
              <Typography.Text className="line-clamp-1 w-0 flex-1" ellipsis>
                {text}
              </Typography.Text>
            </Flex>
          );
        },
      },
      {
        title: '商品名称',
        key: 'showName',
      },
      {
        title: '平台',
        key: 'platformType',
        render: (text, record) => {
          const url = supportedPlatforms?.find(
            (item) => Number(item.key) === text,
          )?.extra;
          return (
            <Flex gap={8} align="center">
              <GoodsImage src={url || ''} size="small" preview={false} />
              <Typography.Text>{PlatformTypeEnum[text]}</Typography.Text>
            </Flex>
          );
        },
      },
      {
        title: '店铺名称',
        key: 'shopName',
      },
      {
        title: '店铺商品ID',
        key: 'platformSpuId',
      },
      {
        title: '店铺商品编码',
        key: 'platformSpuCode',
      },
      {
        title: '店铺商品名称',
        key: 'platformSpuName',
      },
      {
        title: '操作人',
        key: 'optUserName',
      },
      {
        title: '铺货时间',
        key: 'optTime',
      },
      {
        title: '无法铺货原因',
        key: 'reason',
        width: 'action',
        hidden: activeTabKey === UpdateRolloutConfirmTab.可更新店铺商品,
      },
      {
        title: '操作',
        key: 'action',
        width: 'action',
        hidden: activeTabKey === UpdateRolloutConfirmTab.可更新店铺商品,
        fixed: 'right',
        render: (_, record: CommonRecord) => {
          const { reason, platformType } = record;
          const platformDistributionId =
            record.platformDistributionId as string;
          const actualGoodsSpuId = record.actualGoodsSpuId as string;
          const id =
            rolloutWay === PlatformRolloutWayEnum['分销商品管理']
              ? actualGoodsSpuId
              : platformDistributionId;
          switch (reason) {
            case '商品铺货售价不在供应商控价范围内':
              return (
                <PriceAndInventoryModal
                  rolloutWay={rolloutWay}
                  targetIds={[id]}
                  okText="更新所选店铺商品"
                  platformType={Number(platformType)}
                  onSubmit={(dataList: InventoryAndPriceDataItem[]) =>
                    priceAndInventoryModalSubmit(dataList, id, platformType)
                  }
                >
                  {(onClick) => {
                    return (
                      <Typography.Link onClick={onClick}>
                        修改价格
                      </Typography.Link>
                    );
                  }}
                </PriceAndInventoryModal>
              );
          }
        },
      },
    ];
  }, [activeTabKey, rolloutWay, supportedPlatforms]);

  useUpdateEffect(() => {
    if (updateRolloutRecordedList?.length) {
      const supportedPlatform = supportedPlatforms?.map((item) =>
        Number(item.key),
      );

      const updatableData = updateRolloutRecordedList.filter(
        (item) =>
          !item.reason && supportedPlatform?.includes(item.platformType),
      );

      const nonUpdatableData = updateRolloutRecordedList.filter(
        (item) => item.reason && supportedPlatform?.includes(item.platformType),
      );
      updateDataLength.current = updatableData.length;
      noUpdateDataLength.current = nonUpdatableData.length;

      if (activeTabKey === UpdateRolloutConfirmTab.可更新店铺商品) {
        setDataSource(updatableData);
        setOriginalDataSource(updatableData);
      } else {
        setDataSource(nonUpdatableData);
        setOriginalDataSource(nonUpdatableData);
      }
    }
  }, [updateRolloutRecordedList, activeTabKey]);

  const closeModal = useCallback(() => {
    setModalOpen(false);
    setUpdateRolloutRecordedList([]);
    form.resetFields();
  }, [form]);
  const showModal = useCallback(() => {
    setActiveTabKey(UpdateRolloutConfirmTab['可更新店铺商品']);

    setModalOpen(true);
  }, []);

  useImperativeHandle(ref, () => ({ showModal, closeModal }));

  const beforeTriggerQuery = useCallback(() => {
    const formValues = form.getFieldsValue();

    let filteredData = originalDataSource;

    if (formValues.shopId && formValues.shopId.length > 0) {
      filteredData = filteredData.filter((item) =>
        formValues.shopId.includes(item.shopId),
      );
    }

    if (formValues.targetCode && formValues.targetCode.length > 0) {
      const targetCodes = formValues.targetCode.flatMap((code) =>
        code.split(',').map((c) => c.trim()),
      );

      if (formValues.targetCodeVagueType === 0) {
        // 精确匹配
        filteredData = filteredData.filter((item) =>
          targetCodes.includes(item.targetCode),
        );
      } else {
        // 模糊匹配
        filteredData = filteredData.filter((item) =>
          targetCodes.some((code) => item.targetCode.includes(code)),
        );
      }
    }

    setDataSource(filteredData);
  }, [form, originalDataSource]);

  const onOk = useCallback(() => {
    const selectedRows =
      tableRef.current?.getSelectedRows()?.selectedRows || [];
    if (!selectedRows.length) {
      msgInfo('请先勾选数据');
      return;
    }

    setConfirmUpdateProduct(() => {
      return selectedRows?.map((val) => {
        const extra = supportedPlatforms?.find(
          (item) => Number(item.key) === val.platformType,
        )?.extra;
        return {
          ...val,
          platformLogoUrl: extra,
        };
      });
    });

    updateRolloutSettingsModalRef.current?.showModal();
  }, [
    setConfirmUpdateProduct,
    closeModal,
    updateRolloutSettingsModalRef,
    supportedPlatforms,
  ]);

  return (
    <Fragment>
      <Modal
        type="edit"
        title="确认更新的店铺商品"
        open={modalOpen}
        size="xxl"
        destroyOnClose
        onOk={onOk}
        okText="更新所选店铺商品"
        onCancel={closeModal}
      >
        <Tabs
          items={[
            {
              label: `可更新店铺商品 (${updateDataLength.current})`,
              key: UpdateRolloutConfirmTab['可更新店铺商品'],
            },
            {
              label: `不可更新店铺商品 (${noUpdateDataLength.current})`,
              key: UpdateRolloutConfirmTab['不可更新店铺商品'],
            },
          ]}
          activeKey={activeTabKey}
          onChange={setActiveTabKey}
        />
        <BizTable
          showRowIndex={false}
          table={{
            tableRef,
            id: 'MallPromotionChannelsScreen',
            scroll: { y: 300 },
            columns,
            columnsSetting: false,
            rowSelection:
              activeTabKey === UpdateRolloutConfirmTab['可更新店铺商品']
                ? { type: 'checkbox' }
                : false,
            rowKey: 'recordId',

            dataSource,
            pagination: false,
          }}
          search={{
            form,
            beforeTriggerQuery,
            onReset: () => {
              form.resetFields();
              setDataSource(originalDataSource);
            },
            searchColumns: [
              {
                key: 'shopId',
                type: 'localSearchSelect',
                fieldProps: {
                  maxTagCount: 'responsive',
                  mode: 'multiple',
                  placeholder: '请选择',
                  fieldNames: {
                    label: 'label',
                    value: 'value',
                  },
                  options:
                    selectedShops?.map((el) => ({
                      label: el.shopName,
                      value: el.id,
                    })) || [],
                },
                formItemProps: {
                  label: '选择店铺',
                  name: 'shopId',
                },
              },
            ],
            proportion: 3,
          }}
          requestConfig={false}
          toolbar={{
            left: [],
          }}
        />
      </Modal>
    </Fragment>
  );
});

export default ConfirmUpdateStoreProducts;
