import type { CommonRecord } from '@slt/base-utils';
import type { PlatformTypeEnum } from '@slt/utils';
import type { Dayjs } from 'dayjs';

import type { TextAreaProps } from '@slt/base-ui';
/** 批量操作返回结果 */
export type BatchOperationResultVO<FailList = BatchOperationFailListVO> = {
  fail?: number;
  failList?: Array<FailList>;
  success?: number;
  total?: number;
  allFail?: boolean;
  allSuccess?: boolean;
  tipsMsg?: string;
};

export type BatchOperationFailListVO = CommonRecord & {
  /** 失败数据 */
  no: string;
  /** 失败原因 */
  errorMsg: string;
};

/**
 * 批量操作订单，失败列表类型
 */
export type OrderBatchOperationFailListVO = {
  /** 订单号 */
  key: string;
  /** 失败原因 */
  failReason?: string;
  /** 审核成功提示 */
  successTip?: string;
};

/** 表格请求返回参数字段 */
export type CommonTableResultVO<T> = {
  list: T[];
  page: number;
  total: number;
  pageSize: number;
  totalPage: number;
};

export type ToastVO = {
  queryFieldName: string;
  total: number;
  success: number;
  fail: number;
  failList?: string[];
};

/** 表格请求返回参数字段，包含pageVO */
export type CommonTableResultVOWithPageVO<T> = {
  pageVO: CommonTableResultVO<T>;
  toastVOList?: ToastVO[];
} & CommonRecord;

/** 表格分页请求字段 */
export type CommonTableRequestParams = {
  page: number;
  pageSize: number;
};

export type NumericBool = 0 | 1;

/** 通用平台类型 */
export type PlatformVO = {
  code: string;
  name: string;
  logo?: string;
  type: PlatformTypeEnum;
};

/** 侧边栏菜单 */
export type MenuItemDTO = {
  key: string;
  title: string;
  /** 菜单code */
  menuCode: string;
  /** 菜单名称 */
  menuName: string;
  /** 菜单路由 */
  pagePath?: string;
  /**是否编辑菜单名称 */
  canEdit?: boolean;
  iconCode?: string;
  children?: MenuItemDTO[];
  extra?: ExtraMenuItemDTO;
};

export type ExtraMenuItemDTO = {
  moduleNo: string;
  iconCode: string;
  type: number;
};

/** 单据创建/更新前校验结果 */
export type BillOperationResultVO = {
  /**
   * 允许继续的错误
   */
  continuesResultList?: Result[];
  /**
   * 结果集
   */
  continues?: Result[];
};
export interface Result {
  /**
   * 错误信息，提示数据，通过build构建
   */
  errorData?: BaseInfoVO;
  /**
   * 单据删除，错误类型
   */
  type?: number;
}
export interface BaseInfoVO {
  /**
   * 错误提示语
   */
  errorMsg?: string;
}

/** 日期返回格式 */
export type CommonDateType = string | Dayjs;

export type AddressRecognitionProps = {
  /****识别回调函数****/
  updateCallback: (value: AddressRecognitionData) => void;
  /****placeholder****/
  placeholder?: string;
  /**  */
  textAreaProps?: TextAreaProps;
};

export type AddressRecognitionData = {
  /** 姓名 */
  name: string;
  /** 电话 */
  phone: string;
  /** 电话 */
  tel: string;
  /** 完整地址 */
  address: Array<AddressRecognitionItem>;
  /** 详细地址 */
  detailAddress: string;
  /** 最后一级地址 */
  lastNode: AddressRecognitionItem | null;
  contactName: string;
  mobile: string;
  cascadeNodeList: AddressRecognitionItem[];
};

export type AddressRecognitionItem = {
  label: string | undefined;
  value: string | undefined | number;
};

/**单据校验结果类型type */
export enum ErrorResultCheckedType {
  /**单据已标记对账、导致预收/付款不足 */
  TypeOne = 1,
  /**单据有关联 */
  TypeTwo = 2,
  /**商品会导致负库存 */
  TypeThree = 3,
  /** 单据超额结算 */
  TypeSix = 6,
  /**判断当前选择的结算单据，是否有在其他待审核的收付款单的结算明细里面 */
  TypeSeven = 7,
  /**没有在系统中找到对应的业务单据 */
  TypeEight = 8,
  /**商品没有出库成本价，需要填写固定成本价 */
  TypeThirtyOne = 31,
  /**单据编号重复 */
  TypeOneHundred = 100,
  /** 如果可以继续保存情况下如果type全是200，则不展示提示title  */
  TypeTwoHundred = 200,
}
