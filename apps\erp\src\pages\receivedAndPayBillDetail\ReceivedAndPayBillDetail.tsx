import {
  BillSynergyTipsModal,
  SaveFailedReasonModal,
  type BillSynergyTipsModalRef,
  type MerchantSelectWithSynergyRef,
} from '@/components/biz';
import type { SaveFailedReasonModalRef } from '@/components/biz/receipt/saveFailedReasonModal/types';
import {
  useBillOperate,
  type BillActionType,
} from '@/hooks/bizHooks/useBillOperate';
import { useOpenTab } from '@/hooks/useOpenTab';
import { routerMap } from '@/router/routerMap';
import {
  useActions,
  useTabSearch,
  useTabSearchParams,
  useUserInfo,
} from '@/store/system';
import {
  Alert,
  Button,
  CloseCircleFilled,
  Condition,
  Flex,
  Form,
  message,
  msgError,
  msgInfo,
  Partition,
  Space,
  Typography,
  type EditableTableRef,
} from '@slt/base-ui';
import {
  defaultDateFormat,
  toNumber,
  type CommonRecord,
} from '@slt/base-utils';
import { net, type YTRequest } from '@slt/net';
import {
  basicBillNoGeneratorGen,
  financeReceiveAndPaymentBillBillNoRepeated,
  financeReceiveAndPaymentBillCheckCreateOrUpdateOrder,
  financeReceiveAndPaymentBillCheckReview,
  financeReceiveAndPaymentBillCheckUpdateOrDelete,
  financeReceiveAndPaymentBillCreateOrderV2,
  financeReceiveAndPaymentBillQuerySynergyDiff,
  financeReceiveAndPaymentBillUpdateOrderV2,
  stockReceiveAndPaymentBillQueryTaskProgressUrl,
  type FinanceBillReviewParams,
} from '@slt/services';
import {
  BillBusinessType,
  BillSourceType,
  FinanceBillStatusType,
  MerchantType,
  ReceiveAndPaymentBillCheckType,
  type CommonSelectEnum,
} from '@slt/utils';
import { useMount } from 'ahooks';
import { createStyles } from 'antd-style';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { DetailListEditableTable } from './components/detailListEditableTable/DetailListEditableTable';
import { SettlementDetailsEditableTable } from './components/settlementDetailsEditableTable/SettlementDetailsEditableTable';
import { useEditData } from './hooks/useEditData';
import { ReceivedAndPayBill } from './receivedAndPayBill/ReceivedAndPayBill';
import { useMutation } from '@slt/net-query';
import { v4 as uuidv4 } from 'uuid';
import { useEditableTableValidatorRules } from '@slt/biz-ui';
import type { FinanceAdvanceFinanceBillFrom } from './receivedAndPayBill/types';
import { GlobalPollingTaskEnum } from '@/store/type';

const { Text, Link } = Typography;

type Props = {
  type: BillActionType;
  isPay?: boolean;
};

const errTItle =
  '以下原因造成您跟供应商的收款单结算单据数有差异,请检查是否需要处理,需要的话可以把单据补充生成后添加到结算明细,点击【自动分配金额】或手工分配金额后,再执行审核操作';

export const ReceivedAndPayBillDetail = (props: Props) => {
  const { type, isPay } = props;

  const authIdObj: Record<string, string> = useMemo(() => {
    if (isPay) {
      return {
        add: '924',
        edit: '925',
        synergyAudit: '2092',
        settle: '2270',
      };
    } else {
      return {
        add: '798',
        edit: '799',
        synergyAudit: '2089',
        settle: '2269',
      };
    }
  }, [isPay]);

  const billText = isPay ? '付' : '收';

  const { openTab } = useOpenTab();
  const { showName = '' } = useUserInfo() || {};
  const searchParams = useTabSearchParams();
  const id = searchParams.get('id');

  const { tabSearch } = useTabSearch();
  const { styles } = useStyles();
  const { closeAndRefreshPrevTab, closeActiveTab, startPolling } = useActions();

  const [form] = Form.useForm<FinanceAdvanceFinanceBillFrom>();

  const saveFailedReasonModalRef = useRef<SaveFailedReasonModalRef>(null);
  const billNoRepeatRef = useRef<string | null>(null);
  /** 协同单据提示弹窗 */
  const synergyBillModalRef = useRef<BillSynergyTipsModalRef>(null);
  /** 商户下拉 */
  const merchantSelectRef = useRef<MerchantSelectWithSynergyRef>(null);
  /** 收付款明细列表ref */
  const editableTableRef = useRef<EditableTableRef>(null);
  /** 结算明细列表ref */
  const settleEditableTableRef = useRef<EditableTableRef>(null);

  const [initialPrePayment, setInitialPrePayment] = useState<
    number | undefined
  >(undefined);

  const [saveLoading, setSaveLoading] = useState(false);

  const { mutate: billCheckReview } = useMutation(
    financeReceiveAndPaymentBillCheckReview,
  );
  const { mutate: billQuerySynergyDiff } = useMutation(
    financeReceiveAndPaymentBillQuerySynergyDiff,
  );

  const {
    loading: formLoading,
    initialData,
    synergyInfo,
    onEchoBill,
  } = useEditData({ id, form });

  const prePayment = Form.useWatch<number>('prePayment', form);
  const detailList = Form.useWatch<CommonRecord[]>('detailList', form);

  const { rules } = useEditableTableValidatorRules({
    isValidate: !prePayment || !!detailList?.length,
    emptyMsg: `请添加${billText}款明细`,
    editableTableRef,
  });

  const { rules: settleRules } = useEditableTableValidatorRules({
    emptyMsg: '请添加结算明细',
    editableTableRef: settleEditableTableRef,
  });

  const onInitialPrePayment = useCallback(
    (value?: CommonRecord) => {
      if (
        initialData?.customer?.value &&
        value?.value === initialData?.customer?.value
      ) {
        setInitialPrePayment(initialData?.prePayment);
      } else {
        setInitialPrePayment(undefined);
      }
    },
    [initialData],
  );

  useEffect(() => {
    if (initialData?.prePayment) {
      setInitialPrePayment(toNumber(initialData?.prePayment));
    }
  }, [initialData?.prePayment]);

  const initialValues = useMemo(() => {
    if (tabSearch?.customer) {
      const values = {
        ...initialData,
        customer: tabSearch.customer as CommonSelectEnum<string>,
      } as FinanceAdvanceFinanceBillFrom;
      if (tabSearch.arrearsAmount) {
        values.detailList = [
          {
            tempId: uuidv4(),
            amount: tabSearch.arrearsAmount as number,
          },
        ];
      }

      form.setFieldsValue(values);
      return values;
    }
    return initialData as FinanceAdvanceFinanceBillFrom;
  }, [initialData, tabSearch?.customer, tabSearch?.arrearsAmount, form]);

  const isViewPage = useMemo(() => type === 'view', [type]);
  const isSettlePage = useMemo(() => type === 'settle', [type]);

  /** 单据状态(编辑页、详情页) */
  const initialBillStatus = useMemo(() => {
    return initialData?.billStatus as number;
  }, [initialData]);

  /** 编辑单据checkType */
  const editCheckType = useMemo(() => {
    if (isSettlePage) {
      return ReceiveAndPaymentBillCheckType['按单结算'];
    }
    return ReceiveAndPaymentBillCheckType['编辑'];
  }, [isSettlePage]);

  const onFormValidate = useCallback(
    async (status?: FinanceBillStatusType) => {
      const { detailList, prePayment } = form.getFieldsValue();
      const { businessBillList } = form.getFieldsValue();

      let detailValidateRes: boolean | undefined = true;
      let businessValidateRes: boolean | undefined = true;
      if (status !== FinanceBillStatusType['草稿']) {
        if (businessBillList?.length) {
          businessValidateRes =
            await settleEditableTableRef.current?.validateFields();
          !businessValidateRes && msgError('单据的本次结算金额不能为空');
        } else {
          msgError('请选择结算单据');
          businessValidateRes = false;
        }

        if (!prePayment) {
          // 已确认、已提交、暂存时，没有填预收预付字段，明细列表必填
          if (detailList?.length) {
            detailValidateRes =
              await editableTableRef.current?.validateFields();
          } else {
            msgError(`${billText}款明细不能为空`);
            detailValidateRes = false;
          }
        }
      }

      return form
        .validateFields()
        .then(() => !!detailValidateRes && !!businessValidateRes)
        .catch(() => false);
    },
    [billText, form],
  );

  /** 新增单据-获取单据编号 */
  const handleGetBillNo = useCallback(async () => {
    const res = await net.fetch(
      basicBillNoGeneratorGen(
        isPay ? BillBusinessType.付款单付款 : BillBusinessType.收款单收款,
      ),
    );
    if (res.success && res.data) {
      const { billNo, billType } = res.data;
      form.setFieldsValue({
        billNo,
        billType,
      });
    }
  }, [form, isPay]);

  useMount(() => {
    if (type === 'add') {
      handleGetBillNo();
    }
  });

  const saveRequest = useCallback(
    (
      value: CommonRecord,
    ): YTRequest<{
      billId?: string;
      billNo?: string;
      taskId?: string;
    }> => {
      if (type === 'add') {
        return financeReceiveAndPaymentBillCreateOrderV2(value);
      } else {
        return financeReceiveAndPaymentBillUpdateOrderV2({ id, ...value });
      }
    },
    [id, type],
  );
  const viewRoute = useMemo(() => {
    return isPay
      ? routerMap.payBillView.route
      : routerMap.receivedBillView.route;
  }, [isPay]);

  const getValues = useCallback(
    (billStatus: FinanceBillStatusType) => {
      const values = form.getFieldsValue();
      const {
        customer,
        salesMan,
        detailList,
        departmentId,
        billDateTime,
        businessBillList,
        ...rest
      } = values;
      const totalAmount = detailList?.reduce(
        (sum: number, item: CommonRecord) => sum + (toNumber(item.amount) ?? 0),
        0,
      );

      const filteredDetailList = detailList?.filter(
        (item: CommonRecord) =>
          item?.accountName ||
          item?.settlementType ||
          item?.amount ||
          item?.remark,
      );

      const newBusinessBillList = businessBillList?.map((item, index) => ({
        rowNo: index + 1,
        billId: item.billId,
        businessBillId: item.businessBillId,
        settlementAmount: item?.settlementAmount,
        billType: item.billType,
        businessType: item.businessType,
        businessBillNo: item.businessBillNo,
        businessBillTime: item?.businessBillTime,
        salesManId: item?.salesManId,
        departmentId: item?.departmentId,
      }));
      const newValue: CommonRecord = {
        ...rest,
        checkType: editCheckType,
        billStatus,
        businessType: isPay ? '27' : '26',
        customerId: customer?.value,
        salesManId: salesMan?.value,
        totalAmount,
        billDateTime: billDateTime
          ? billDateTime.format(defaultDateFormat.time)
          : undefined,
        detailList: filteredDetailList?.length
          ? filteredDetailList?.map((item, index: number) => ({
              ...item,
              settlementType: item?.settlementType?.value,
              rowNo: index + 1,
            }))
          : undefined,
        allocatedAmount: newBusinessBillList?.reduce(
          (sum: number, item: CommonRecord) =>
            sum +
            (toNumber((toNumber(item?.settlementAmount) ?? 0).toFixed(2)) ?? 0),
          0,
        ),
        businessBillList: newBusinessBillList?.length
          ? newBusinessBillList
          : undefined,
        departmentId: Array.isArray(departmentId)
          ? departmentId[departmentId.length - 1]
          : departmentId,
      };
      if (id) {
        newValue.id = id;
      }

      return newValue as FinanceAdvanceFinanceBillFrom;
    },
    [editCheckType, form, id, isPay],
  );

  /**
   * 1、通过getValues获取参数
   * 2、请求保存
   */
  const saveBill = useCallback(
    async (
      newValue: FinanceAdvanceFinanceBillFrom,
      status?: FinanceBillStatusType,
    ) => {
      const billStatus = status ? status : FinanceBillStatusType['已提交'];
      const route = isPay ? '/payBillList' : '/receivedBill';
      const values = getValues(billStatus);
      const { nextBillRemark } = newValue;
      const response = await net.fetch(
        saveRequest({ ...values, nextBillRemark }),
      );
      if (response.success) {
        closeAndRefreshPrevTab();
        if (response.data?.taskId) {
          // 说明执行的是异步任务
          startPolling({
            request: {
              url:
                stockReceiveAndPaymentBillQueryTaskProgressUrl +
                `/${response.data?.taskId}`,
              method: 'get',
            },
            taskId: response.data?.taskId,
            taskType: GlobalPollingTaskEnum.RECEIVEANDPAY,
            interval: 2000,
            successCondition: [
              {
                field: 'data.taskStatus',
                value: 2,
              },
              {
                field: 'data.taskStatus',
                value: 3,
              },
            ],
            msgConfig: {
              openDetailTabs: {
                to: route,
              },

              operateType: 'save',
            },
          });
          msgInfo('结算单据较多，正在异步保存，请稍后关注系统右下角提示');
        } else {
          message.success('保存成功');
          openTab(viewRoute, {
            search: {
              id: response.data?.billId,
              ignoreAuth: true,
            },
          });
        }
      }
    },
    [
      closeAndRefreshPrevTab,
      getValues,
      isPay,
      openTab,
      saveRequest,
      startPolling,
      viewRoute,
    ],
  );

  /**
   * 是否展示协同窗口逻辑
   */
  const isSynergyBill = useCallback(
    async (
      newValues: FinanceAdvanceFinanceBillFrom,
      billStatus?: FinanceBillStatusType,
    ) => {
      const values = form.getFieldsValue();
      const { customer } = values;
      const synergyConf = await merchantSelectRef?.current?.getSynergyConf(
        customer?.value + '',
      );
      const isSynergyBill =
        synergyConf?.billSynergy || synergyInfo?.synergyStatus;

      if (isSynergyBill && billStatus !== FinanceBillStatusType['草稿']) {
        if (billStatus !== FinanceBillStatusType['暂存'] && !isSettlePage) {
          setSaveLoading(false);
          synergyBillModalRef?.current?.showModal({
            values,
            billStatus: FinanceBillStatusType['已提交'],
            synergyInfo: synergyConf,
          });
        } else {
          await saveBill(newValues, billStatus);
        }
      } else {
        await saveBill(newValues, billStatus);
      }
    },
    [form, isSettlePage, saveBill, synergyInfo?.synergyStatus],
  );

  const handleSave = useCallback(
    async (
      value: CommonRecord,
      billStatus: FinanceBillStatusType,
      forceSave = false,
    ) => {
      const newValue = getValues(billStatus);
      const { billNo } = newValue;

      if (forceSave) {
        // 强制保存
        setSaveLoading(true);
        const response = await net.fetch(saveRequest(newValue));
        setSaveLoading(false);
        if (response.success) {
          message.success('保存成功');
          closeAndRefreshPrevTab();
          openTab(viewRoute, {
            search: {
              id: response.data?.billId,
              ignoreAuth: true,
            },
          });
        }
      } else {
        setSaveLoading(true);
        // 保存为草稿也需要check
        const cheekResponse = await net.fetch(
          financeReceiveAndPaymentBillCheckCreateOrUpdateOrder(newValue),
        );

        if (cheekResponse.data) {
          const { continues, resultList, continuesResultList } =
            cheekResponse.data;
          if (
            continues &&
            !resultList?.length &&
            !continuesResultList?.length
          ) {
            const repeatedResponse = await net.fetch(
              financeReceiveAndPaymentBillBillNoRepeated({
                billNo: billNo,
                id: id ?? undefined,
                businessType: isPay
                  ? BillBusinessType.付款单付款
                  : BillBusinessType.收款单收款,
              }),
            );

            if (repeatedResponse?.data) {
              const { continues, resultList } = repeatedResponse.data;
              if (continues && !resultList?.length) {
                //校验通过直接保存
                await isSynergyBill(newValue, billStatus);
              } else {
                //校验不通过
                if (saveFailedReasonModalRef?.current) {
                  saveFailedReasonModalRef?.current?.showModal(
                    repeatedResponse.data,
                  );
                }
              }
            }
          } else {
            //展示校验不通过信息
            if (saveFailedReasonModalRef?.current) {
              saveFailedReasonModalRef?.current?.showModal(cheekResponse.data);
            }
          }
        }

        setSaveLoading(false);
      }
    },
    [
      id,
      isPay,
      viewRoute,
      openTab,
      getValues,
      saveRequest,
      isSynergyBill,
      closeAndRefreshPrevTab,
    ],
  );

  const handleSaveContinue = useCallback(() => {
    // 要判断是不是协同客户协同客户
    void isSynergyBill(form.getFieldsValue(), FinanceBillStatusType['已确认']);
  }, [form, isSynergyBill]);

  /**保存校验不通过-生成新的单据编号 */
  const onCreateReceiptNo = useCallback(async () => {
    await handleGetBillNo();
  }, [handleGetBillNo]);

  /**保存校验不通过-手动修改单据编号 */
  const onEditReceiptNo = useCallback(() => {
    const msg = '请重新填写单据编号';
    form.setFields([
      {
        name: 'billNo',
        errors: [msg],
      },
    ]);
    billNoRepeatRef.current = msg;
  }, [form]);

  /** 点击保存按钮，判断是否要走协同逻辑 */
  const onClickSave = useCallback(
    async (billStatus?: FinanceBillStatusType) => {
      const res = await onFormValidate();
      if (res) {
        const values = form.getFieldsValue();
        if (billStatus === FinanceBillStatusType['暂存']) {
          // 暂存时不弹对方账户信息弹窗
          void handleSave(values, billStatus);
        } else if (isSettlePage) {
          // 按单结算不用再次弹协同弹窗
          void handleSave(
            values,
            initialBillStatus ?? FinanceBillStatusType['已提交'],
          );
        } else {
          void handleSave(values, FinanceBillStatusType['已确认']);
        }
      }
    },
    [isSettlePage, form, initialBillStatus, onFormValidate, handleSave],
  );

  /** 保存为草稿 */
  const onClickSaveDraft = useCallback(() => {
    const value = form.getFieldsValue();
    void handleSave(value, FinanceBillStatusType['草稿']);
  }, [form, handleSave]);

  const editRoute = useMemo(() => {
    return isPay
      ? routerMap.payBillEdit.route
      : routerMap.receivedBillEdit.route;
  }, [isPay]);

  const onClickEdit = useCallback(async () => {
    if (id) {
      const cheekResponse = await net.fetch(
        financeReceiveAndPaymentBillCheckUpdateOrDelete({
          id,
          checkType: editCheckType,
        }),
      );
      if (cheekResponse.success) {
        closeAndRefreshPrevTab();
        openTab(editRoute, {
          search: {
            id,
          },
        });
      }
    }
  }, [closeAndRefreshPrevTab, editRoute, id, editCheckType, openTab]);

  const addRoute = useMemo(() => {
    return isPay ? routerMap.payBillAdd.route : routerMap.receivedBillAdd.route;
  }, [isPay]);

  const onClickToAdd = useCallback(() => {
    closeActiveTab();
    openTab(addRoute);
  }, [addRoute, closeActiveTab, openTab]);

  const toSettlePage = useCallback(() => {
    const route = isPay
      ? routerMap.payBillSettle.route
      : routerMap.receivedBillSettle.route;

    openTab(route, {
      search: { id },
    });
  }, [id, isPay, openTab]);

  /** 收付款单审核前校验 */
  const handleCheckBeforeReview = useCallback(
    async (params: FinanceBillReviewParams) => {
      const res = await billCheckReview(params);
      if (res.success && res.data) {
        const { continues } = res.data;
        if (continues) {
          return true;
        } else {
          saveFailedReasonModalRef.current?.showModal(
            res.data,
            '单据审核失败，原因如下',
          );
        }
      }

      return false;
    },
    [billCheckReview],
  );

  const {
    primaryBtn,
    billBtnShowMap,
    isSynergyWaitAudit,
    isSynergyAuditReject,
    roleTextDuringAudit,
    handleSynergyPass,
    handleSynergyReject,
  } = useBillOperate({
    type,
    billId: id || '',
    isPaymentBill: !!isPay,
    synergyStatus: synergyInfo?.synergyStatus,
    initialData,
    passText: `通过后会${isPay ? '更新你的应付金额' : '冲抵你的应收金额'}，并同步更新${isPay ? '供应商的应收金额' : '客户的应付金额'}。`,
    rejectText: `确认拒绝${isPay ? '供应商发起的收款' : '客户发起的付款'}？`,
    authIdConf: authIdObj,
    onReload: () => {
      void onEchoBill();
    },
    onCheckBeforeReview: handleCheckBeforeReview,
  });

  const showDifferenceReason = useCallback(async () => {
    if (id) {
      const res = await billQuerySynergyDiff(id);
      if (res.success && res.data) {
        saveFailedReasonModalRef.current?.showModal(res?.data, errTItle);
      }
    }
  }, [billQuerySynergyDiff, id]);

  const showAlert = useCallback(() => {
    // 1 先判断是不是创建来源=协同创建，待审核的单据
    // 2 结算明细的单据数和协同方的单据数
    const {
      settlementBillNum = 0,
      synergySettlementBillNum = 0,
      billStatus,
      createType,
    } = initialData || {};

    if (
      createType === BillSourceType['协同创建'] &&
      billStatus === FinanceBillStatusType['暂存'] &&
      settlementBillNum !== synergySettlementBillNum
    ) {
      return (
        <Alert
          className="m-xs rounded-lg"
          message={
            <Flex align="center">
              <Text className="text-base">
                结算明细的单据数
                <Text className="text-base text-error">
                  ({settlementBillNum}条)
                </Text>
                跟协同方的单据数
                <Text className="text-base text-error">
                  ({synergySettlementBillNum}条)
                </Text>
                有差异，请检查是否需要处理。
              </Text>
              <Link className="ml-lg text-base" onClick={showDifferenceReason}>
                查看差异原因
              </Link>
            </Flex>
          }
          type="error"
          closable
          showIcon
          icon={<CloseCircleFilled className="text-base text-error" />}
        />
      );
    } else {
      return null;
    }
  }, [initialData, showDifferenceReason]);

  return (
    <Flex vertical className={styles.container}>
      {showAlert()}

      <Form
        id="receivedBillDetailForm"
        labelWidth={84}
        form={form}
        loading={formLoading || saveLoading}
        enableUnsavedWarning={!isViewPage}
        initialValues={initialValues}
        className="h-full overflow-auto"
      >
        <Flex vertical className="h-full w-full" gap={8}>
          <ReceivedAndPayBill
            isPay={isPay}
            type={type}
            roleTextDuringAudit={roleTextDuringAudit}
            initialBillStatus={initialBillStatus}
            billNoRepeatRef={billNoRepeatRef}
            rejectReason={synergyInfo?.rejectReason}
            merchantSelectRef={merchantSelectRef}
            isSynergyWaitAudit={isSynergyWaitAudit}
            isSynergyAuditReject={isSynergyAuditReject}
            initialPrePayment={initialPrePayment}
            onInitialPrePayment={onInitialPrePayment}
          />
          <Partition title={`${billText}款明细`}>
            <Form.Item
              name="detailList"
              rules={rules}
              wrapperCol={{ span: 24 }}
            >
              <DetailListEditableTable
                type={type}
                editableTableRef={editableTableRef}
                isRequired={!prePayment}
              />
            </Form.Item>
          </Partition>
          <Partition title="结算明细" className="flex-1">
            <Form.Item
              name="businessBillList"
              rules={settleRules}
              wrapperCol={{ span: 24 }}
            >
              <SettlementDetailsEditableTable
                type={type}
                isPay={isPay}
                settleEditableTableRef={settleEditableTableRef}
              />
            </Form.Item>
          </Partition>
        </Flex>
      </Form>
      <Flex className="border-t bg-white p-sm" justify="between">
        <Space className="w-full text-text" size="middle">
          <div>制单人：{id ? initialData?.createUserName : showName}</div>
          {initialData?.gmtCreate ? (
            <div>制单时间：{initialData?.gmtCreate}</div>
          ) : null}
          {initialData?.gmtModified ? (
            <div>保存时间：{initialData?.gmtModified}</div>
          ) : null}
        </Space>
        <Flex gap={8}>
          <Button size="large" onClick={closeActiveTab}>
            取消
          </Button>
          <Condition visible={billBtnShowMap.edit}>
            <Button
              size="large"
              type={primaryBtn === 'edit' ? 'primary' : 'default'}
              onClick={onClickEdit}
            >
              编辑
            </Button>
          </Condition>
          <Condition visible={billBtnShowMap.add}>
            <Button
              size="large"
              type={primaryBtn === 'add' ? 'primary' : 'default'}
              onClick={onClickToAdd}
            >
              新增
            </Button>
          </Condition>
          <Condition visible={billBtnShowMap.synergyTempSave}>
            <Button
              loading={saveLoading}
              size="large"
              type={primaryBtn === 'synergyTempSave' ? 'primary' : 'default'}
              onClick={() => onClickSave(FinanceBillStatusType['暂存'])}
            >
              暂存
            </Button>
          </Condition>
          <Condition visible={billBtnShowMap.synergyAudit}>
            <Button size="large" onClick={handleSynergyReject}>
              拒绝
            </Button>
            <Button size="large" type="primary" onClick={handleSynergyPass}>
              通过
            </Button>
          </Condition>
          <Condition visible={billBtnShowMap.saveDraft}>
            <Button
              loading={saveLoading}
              size="large"
              onClick={onClickSaveDraft}
            >
              保存成草稿
            </Button>
          </Condition>
          <Condition visible={billBtnShowMap.save}>
            <Button
              loading={saveLoading}
              size="large"
              type="primary"
              onClick={() => onClickSave()}
            >
              保存
            </Button>
          </Condition>
          <Condition visible={billBtnShowMap.settleByOrder}>
            <Button size="large" type="primary" onClick={toSettlePage}>
              按单结算
            </Button>
          </Condition>
        </Flex>
      </Flex>
      <SaveFailedReasonModal
        ref={saveFailedReasonModalRef}
        onSaveContinue={handleSaveContinue}
        onCreateReceiptNo={onCreateReceiptNo}
        onEditReceiptNo={onEditReceiptNo}
      />
      <BillSynergyTipsModal
        ref={synergyBillModalRef}
        merchantType={isPay ? MerchantType.SUPPLIER : MerchantType.DISTRIBUTOR}
        onFinish={saveBill}
      />
    </Flex>
  );
};

const useStyles = createStyles(({ token, css }) => ({
  container: css`
    width: 100%;
    height: 100%;
    overflow: hidden;

    .ant-spin-nested-loading {
      width: 100%;
      height: 100%;
      overflow: hidden;

      .ant-spin-container {
        width: 100%;
        height: 100%;
        overflow: hidden;
      }
    }
  `,
}));
