import { forwardRef, useCallback, useImperativeHandle, useMemo } from 'react';
import { isNil } from 'lodash-es';
import {
  Button,
  Form,
  Modal,
  Space,
  type EditableTableColumnType,
} from '@slt/base-ui';
import type {
  SaveFailedReasonModalProps,
  SaveFailedReasonModalRef,
} from './types';
import { useSaveFailedReasonModal } from './useSaveFailedReasonModal';
import { ReasonInfo } from '../components/reasonInfo/ReasonInfo';
import {
  getTypeEightColumns,
  getTypeSevenColumns,
  getTypeSixColumns,
  getTypeThirtyOneColumns,
  getTypeThreeColumns,
  getTypeTwoColumns,
} from '../columns';
import { ErrorResultCheckedType, type CheckResultDTO } from '@slt/services';
import { ReasonInfoEditableTable } from '../components/reasonInfoEditableTable/ReasonInfoEditableTable';
import type { NewErrorDataDTO, NewResultGoodsDTO } from '../types';

/**单据保存失败原因展示弹窗 */
export const SaveFailedReasonModal = forwardRef<
  SaveFailedReasonModalRef,
  SaveFailedReasonModalProps
>((props, ref) => {
  const {
    trigger,
    defaultShowTips = true,
    visible,
    receiptNoInfo,
    costPriceOfDeliveryForm,
    resultInfoList,
    canContinueSave,
    form,
    errorTitle,
    showModal,
    onCancel,
    handleContinue,
    handleEditReceiptNo,
    handleCreateReceiptNo,
    onRowDataChange,
    handleLinkToListPage,
    isShowTitle,
  } = useSaveFailedReasonModal(props);

  useImperativeHandle(ref, () => ({
    showModal,
  }));

  /**单据编号重复 */
  const receiptNoInfoNode = useMemo(() => {
    const { errorMsg = '单据编号重复！已存在相同编号的单据' } =
      receiptNoInfo?.errorData || {};
    return (
      <Space direction="vertical">
        <div className="text-base">{errorMsg}</div>
        <div className="text-text-secondary">
          生成新编号：按系统单据编码规则自动生成新编号保存
        </div>
        <div className="text-text-secondary">
          手工修改：返回开单页面，手工修改编号
        </div>
      </Space>
    );
  }, [receiptNoInfo?.errorData]);

  /**错误信息展示 */
  const renderResultInfoNode = useCallback(
    (startNum: number, list: CheckResultDTO[]) => {
      return list?.map((item, idx) => {
        const { type, errorData } = item;
        const { errorMsg, goodsList, billList, businessBillList } =
          errorData || {};
        const newErrorMsg = `${idx + startNum}、${errorMsg}`;
        if (type === ErrorResultCheckedType.TypeTwo) {
          return (
            <ReasonInfo
              key={idx}
              errorMsg={newErrorMsg}
              columns={getTypeTwoColumns({
                needOperation: false,
                showIndex: true,
              })}
              dataSource={billList}
            />
          );
        } else if (type === ErrorResultCheckedType.TypeSix) {
          return (
            <ReasonInfo
              key={idx}
              errorMsg={newErrorMsg}
              columns={getTypeSixColumns()}
              dataSource={billList}
            />
          );
        } else if (type === ErrorResultCheckedType.TypeSeven) {
          businessBillList?.forEach((v) => {
            v.financeBillLists =
              v.financeBillList?.map((v) => v.billNo).join(',') + ',';
          });
          return (
            <ReasonInfo
              key={idx}
              errorMsg={newErrorMsg}
              columns={getTypeSevenColumns({
                handleLink: handleLinkToListPage,
                errorMsg: errorMsg,
              })}
              dataSource={businessBillList}
            />
          );
        } else if (type === ErrorResultCheckedType.TypeEight) {
          return (
            <ReasonInfo
              key={idx}
              errorMsg={newErrorMsg}
              columns={getTypeEightColumns()}
              dataSource={billList}
            />
          );
        } else {
          const columns = !isNil(goodsList) ? getTypeThreeColumns() : undefined;
          return (
            <ReasonInfo
              key={idx}
              errorMsg={newErrorMsg}
              columns={columns}
              dataSource={goodsList}
            />
          );
        }
      });
    },
    [handleLinkToListPage],
  );

  /**错误信息展示-存在表单情况
   * type=31,商品没有出库成本价，需要填写固定成本价
   */
  const renderCostPriceOfDeliveryForm = useCallback(
    (startNum: number, errorData?: NewErrorDataDTO) => {
      const { errorMsg, goodsList } = errorData || {};
      /**需要填写的表单排在第一位 */
      const newErrorMsg = `${startNum}、${errorMsg}`;
      const columns = getTypeThirtyOneColumns();
      return (
        <Form
          form={form}
          enableUnsavedWarning
          id="renderCostPriceOfDeliveryForm"
        >
          <Form.Item
            label=""
            name="goodsList"
            style={{ marginBottom: 12 }}
            rules={[
              {
                validator: (_, list: NewResultGoodsDTO[]) => {
                  const hasEmptyField = list?.some((item) =>
                    isNil(item.handWriteCostPrice),
                  );
                  return hasEmptyField
                    ? Promise.reject(new Error('存在必填项未填写'))
                    : Promise.resolve();
                },
              },
            ]}
          >
            <ReasonInfoEditableTable
              errorMsg={newErrorMsg}
              columns={columns as EditableTableColumnType<NewResultGoodsDTO>[]}
              dataSource={goodsList}
              onRowDataChange={onRowDataChange}
            />
          </Form.Item>
        </Form>
      );
    },
    [form, onRowDataChange],
  );

  const renderContent = useCallback(() => {
    if (receiptNoInfo) {
      /**单据编号重复 */
      return receiptNoInfoNode;
    } else {
      return (
        <>
          {isShowTitle && defaultShowTips ? (
            <div className="pb-sm">{errorTitle}</div>
          ) : null}
          {costPriceOfDeliveryForm
            ? renderCostPriceOfDeliveryForm(1, costPriceOfDeliveryForm)
            : null}
          {renderResultInfoNode(
            costPriceOfDeliveryForm ? 2 : 1,
            resultInfoList,
          )}
        </>
      );
    }
  }, [
    defaultShowTips,
    errorTitle,
    costPriceOfDeliveryForm,
    receiptNoInfo,
    receiptNoInfoNode,
    renderCostPriceOfDeliveryForm,
    renderResultInfoNode,
    resultInfoList,
    isShowTitle,
  ]);

  const footer = useCallback(() => {
    if (receiptNoInfo) {
      return (
        <Space>
          <Button onClick={handleEditReceiptNo}>手工修改</Button>
          <Button type="primary" onClick={handleCreateReceiptNo}>
            生成新编号
          </Button>
        </Space>
      );
    } else if (canContinueSave) {
      return (
        <Space>
          <Button onClick={onCancel}>取消</Button>
          <Button type="primary" onClick={handleContinue}>
            继续
          </Button>
        </Space>
      );
    } else {
      return (
        <Button type="primary" onClick={onCancel}>
          确认
        </Button>
      );
    }
  }, [
    receiptNoInfo,
    canContinueSave,
    handleEditReceiptNo,
    handleCreateReceiptNo,
    onCancel,
    handleContinue,
  ]);

  return (
    <Modal
      type="edit"
      size="l"
      title="提示"
      destroyOnClose
      open={visible}
      onCancel={onCancel}
      footer={footer}
      trigger={trigger?.(showModal)}
    >
      {renderContent()}
    </Modal>
  );
});
