import { EllipsisOutlined } from '@ant-design/icons';
import type { CommonRecord } from '@slt/base-utils';
import { net } from '@slt/net';
import { useBoolean, useDebounceFn, useRequest, useSetState } from 'ahooks';
import { Button, Flex, Select, Space, Tooltip } from 'antd';
import { createStyles } from 'antd-style';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { Table } from '../../table';
import { Typography } from '../../typography/Typography';
import type {
  ApiTableSelectPaginationParams,
  ApiTableSelectProps,
  IOptionValue,
} from './ApiTableSelect.type';
import {
  DEFAULT_EMPTY_TEXT,
  DEFAULT_PAGE_NO,
  DEFAULT_PAGE_SIZE,
  POPUP_MATCH_SELECT_WIDTH_MORE,
} from './constants';
import clsx from 'clsx';
import { isNil } from 'lodash-es';
import type { IOption } from '../apiSelect/ApiSelect.types';
import { escapeRegExp } from '@slt/utils';

const { Text } = Typography;

const MAX_COLUMNS = 4;

/**
 * 全量查询 select 下拉数据源，支持按 searchKey 搜索
 * 但是在form中使用时，列数超出不会报错
 * 每次打开下拉框都会重新请求数据
 * @params DTO 请求返回的数据类型
 * @params ReqParams 请求参数类型
 */
export const ApiTableSelect = (props: ApiTableSelectProps) => {
  const {
    manual = false,
    emptyText = DEFAULT_EMPTY_TEXT,
    fieldNames,
    params: extraParams,
    searchKey = 'searchKey',
    selectTableProps,
    value,
    style,
    id,
    fieldProps,
    beforeData,
    request,
    onChange,
    extraNode,
    optionRender,
    moreIconClick,
    moreContent,
    needLimitWidth = true,
    defaultOpen,
    mode,
    onClear,
    labelInValue,
    ...restFieldProps
  } = props;
  const {
    id: selectTableId = '',
    selectField,
    selectValueField,
    defaultSelectValue = -1,
  } = selectTableProps;

  const { styles, cx } = useStyles();
  const [dropdownVisible, setDropdownVisible] = useState(defaultOpen ?? false);
  // 请求的数据列表
  const [requestList, setRequestList] = useState<CommonRecord[]>([]);
  const [selectedRowIndex, setSelectedRowIndex] = useState<number | null>(null);

  /** 下拉框数据 */
  const options = useMemo(() => {
    return requestList.map((item) => ({
      ...item,
      label: item?.[selectField] as string,
      value: item?.[selectValueField] as IOptionValue,
    }));
  }, [requestList, selectField, selectValueField]);

  const selectedRecordList = useMemo(() => {
    let selectedList: Partial<IOption>[] = [];

    if (!isNil(value)) {
      if (Array.isArray(value)) {
        selectedList = labelInValue
          ? value.map((r) => ({
              ...r,
              [selectField]: r.label,
              [selectValueField]: r.value,
            }))
          : value.map((r) => ({ value: r, [selectValueField]: r }));
      } else {
        selectedList = labelInValue
          ? [{ ...value, [selectValueField]: value.value }]
          : [{ value, [selectValueField]: value }];
      }
    }

    return selectedList;
  }, [labelInValue, selectField, selectValueField, value]);

  /** 已选下拉项的值 */
  const selectedValueList: IOptionValue[] = useMemo(
    () => selectedRecordList.map((r) => r?.value ?? ''),
    [selectedRecordList],
  );

  const columnLens = useMemo(
    () => selectTableProps.columns.length,
    [selectTableProps.columns.length],
  );

  // 默认分页参数
  const [requestParams, setRequestParams] =
    useSetState<ApiTableSelectPaginationParams>({
      page: extraParams?.page ?? DEFAULT_PAGE_NO,
      pageSize: extraParams?.pageSize ?? DEFAULT_PAGE_SIZE,
      [searchKey]: '',
    });
  // 是否还有更多数据，true(默认)：有更多数据，false：没有更多数据
  const [
    hasMoreData,
    { setTrue: setHasMoreDataTrue, setFalse: setHasMoreDataFalse },
  ] = useBoolean(true);

  const renderHighlightedText = useCallback(
    (txt: string, searchValue: string) => {
      const text = txt + '';
      const searchParams = searchValue.trim();
      if (!searchParams || !text) {
        return text;
      }

      const escapedSearchParams = escapeRegExp(searchParams);

      const regex = new RegExp(`(${escapedSearchParams})`, 'gi');
      const parts = text.split(regex);
      return (
        <>
          {parts.map((part, index) =>
            regex.test(part) ? (
              <Typography.Text key={index} className="text-error-text">
                {part}
              </Typography.Text>
            ) : (
              <Typography.Text key={index}>{part}</Typography.Text>
            ),
          )}
        </>
      );
    },
    [],
  );

  const selectTableColumns = useMemo(() => {
    return selectTableProps.columns.map((item) => {
      const render = item.render;
      const searchValue = (requestParams[searchKey] as string) || '';
      const newItem = {
        ...item,
        render: (text: string, record: CommonRecord, index: number) => {
          return (
            <Text
              className="text-sm"
              ellipsis
              title={record[newItem?.key] as string}
            >
              {renderHighlightedText(text, searchValue)}
            </Text>
          );
        },
      };
      if (render) {
        newItem.render = (_: string, record: CommonRecord, index: number) => {
          const text = (record[newItem?.key] as string) ?? '';
          return (
            <Text className="text-sm" ellipsis title={text}>
              {
                render(
                  renderHighlightedText(text, searchValue),
                  record,
                  index,
                ) as React.ReactNode
              }
            </Text>
          );
        };
      }
      return newItem;
    });
  }, [
    selectTableProps.columns,
    requestParams,
    searchKey,
    renderHighlightedText,
  ]);

  const defaultParams = useMemo(() => {
    return {
      ...requestParams,
      ...extraParams,
    };
  }, [requestParams, extraParams]);

  const { loading: requestLoading, runAsync: runAsyncRequestList } = useRequest(
    (params) => net.fetch(request({ ...params, ...defaultParams })),
    {
      manual,
      refreshDeps: [extraParams],
      defaultParams: [{ ...requestParams, ...extraParams }],
      onSuccess: (result, params) => {
        const { data } = result;
        if (data) {
          let arrList: IOption[] = [];
          if (Array.isArray(data)) {
            arrList = data as IOption[];
          } else {
            const { list, tableList } = data as {
              list: IOption[];
              tableList: IOption[];
            };
            arrList = list ?? tableList;
          }

          const newData = [...requestList, ...arrList];
          // 当返回条数于小分页条数，或者没有返回数据就代表没有更多数据
          if (arrList?.length < requestParams.pageSize) {
            setHasMoreDataFalse();
          }

          if (beforeData) {
            setRequestList(beforeData(newData));
          } else {
            setRequestList(newData);
          }
        }
      },
    },
  );

  // 重新请求
  const initRequest = async (newValue: string) => {
    // 搜索重置分页
    setRequestParams({
      pageSize: extraParams?.pageSize ?? DEFAULT_PAGE_SIZE,
      page: 1,
      [searchKey]: newValue,
    });
    setHasMoreDataTrue();
    // 重置数据列表
    setRequestList([]);
    await runAsyncRequestList({
      ...requestParams,
      ...extraParams,
      page: 1,
      [searchKey]: newValue,
    });
  };

  // 搜索回调
  const { run: handleSearch } = useDebounceFn(
    (newValue: string) => {
      // 重置参数，重新请求
      void initRequest(newValue ?? '');
    },
    { wait: 500 },
  );

  // 清除内容时的回调
  const handlerClear = () => {
    // 当数据不足一页时，重新加载
    if (requestList.length < requestParams.page) {
      void initRequest('');
    }

    onClear?.();
  };

  const handleDropdownVisibleChange = (open: boolean) => {
    setDropdownVisible(open);
    open && void initRequest('');
  };

  const handleChange = useCallback(
    (data: CommonRecord[]) => {
      if (!data.length) {
        onChange?.(undefined);
        return;
      }

      if (mode === 'multiple') {
        // 多选
        const currentOptions = data.map((r) => ({
          label: r?.[selectField] as string,
          value: (r?.[selectValueField] as IOptionValue) ?? defaultSelectValue,
        })) as IOption[];
        const optionValues = currentOptions.map((r) => r.value);
        onChange?.(labelInValue ? currentOptions : optionValues, data);
      } else {
        // 单选
        const currentOption = {
          label: data[0]?.[selectField] as string,
          value:
            (data[0]?.[selectValueField] as IOptionValue) ?? defaultSelectValue,
        };
        onChange?.(labelInValue ? currentOption : currentOption.value, data[0]);
      }
    },
    [
      defaultSelectValue,
      labelInValue,
      mode,
      selectField,
      selectValueField,
      onChange,
    ],
  );

  /** 点击下拉表格的行数据 */
  const handleRowClick = useCallback(
    (record: CommonRecord) => {
      const currentOption = {
        ...record,
        label: record?.[selectField] as string,
        value:
          (record?.[selectValueField] as IOptionValue) ?? defaultSelectValue,
      };

      if (mode === 'multiple') {
        // 下拉多选
        const index = selectedRecordList.findIndex(
          (r) => r?.[selectValueField] === currentOption.value,
        );
        const newSelectedRecordList = [...selectedRecordList];
        if (index > -1) {
          // 已选中的数据再次触发click，取消选中
          newSelectedRecordList.splice(index, 1);
          setSelectedRowIndex(index);
        } else {
          newSelectedRecordList.push(currentOption);
        }

        const selectedRecords = newSelectedRecordList.map((r) => {
          const currentItem = requestList.find(
            (o) => o?.[selectValueField] === r?.[selectValueField],
          );
          return { ...r, ...currentItem };
        });
        handleChange(selectedRecords);
      } else {
        // 下拉单选
        handleChange([record]);
        setDropdownVisible(false);
      }
    },
    [
      selectField,
      selectValueField,
      defaultSelectValue,
      mode,
      selectedRecordList,
      handleChange,
      requestList,
    ],
  );

  /** 下拉框选项改变 */
  const handleSelectChange = useCallback(
    (item?: IOptionValue | IOption | IOptionValue[] | IOption[]) => {
      if (!item) {
        handleChange([]);
        return;
      }

      let valueList: IOptionValue[] = [];
      if (Array.isArray(item)) {
        valueList = item
          .map((r) => (typeof r === 'object' ? r?.value : r))
          .filter(Boolean);
      } else {
        const optionValue = typeof item === 'object' ? item?.value : item;
        valueList = isNil(optionValue) ? [] : [optionValue];
      }

      const selectedRecords = valueList.length
        ? requestList.filter((r) =>
            valueList.includes(r?.[selectValueField] as IOptionValue),
          )
        : [];

      handleChange(selectedRecords);
    },
    [handleChange, requestList, selectValueField],
  );

  /** 弹窗勾选中的数据 */
  const setSelectDisplayValue = useCallback(
    (data: CommonRecord[]) => {
      handleChange(data);
    },
    [handleChange],
  );

  /** 滚动加载 */
  const handleScroll = async (event: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = event.currentTarget;
    if (scrollTop === 0) {
      return;
    }

    if (scrollHeight - scrollTop === clientHeight && hasMoreData) {
      const newPage = requestParams.page + 1;
      setRequestParams((prev) => ({
        ...prev,
        page: newPage,
      }));
      await runAsyncRequestList({
        ...requestParams,
        ...extraParams,
        page: newPage,
      });
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
    if (event.key === 'ArrowDown') {
      event.preventDefault();
      setSelectedRowIndex((prevIndex) => {
        const newIndex =
          prevIndex === null
            ? 0
            : Math.min(prevIndex + 1, requestList.length - 1);
        scrollToRow(newIndex);
        return newIndex;
      });
    } else if (event.key === 'ArrowUp') {
      event.preventDefault();
      setSelectedRowIndex((prevIndex) => {
        const newIndex = prevIndex === null ? 0 : Math.max(prevIndex - 1, 0);
        scrollToRow(newIndex);
        return newIndex;
      });
    } else if (event.key === 'Enter' && selectedRowIndex !== null) {
      event.preventDefault();
      handleRowClick(requestList[selectedRowIndex]);
    }
  };

  // 键盘滚动到行
  const scrollToRow = (index: number) => {
    const tableBody = document
      .querySelector('.api_select_table')
      ?.querySelector('.ant-table-body');
    // 获取当前选中行
    const row = tableBody?.querySelectorAll('.ant-table-row')[index];
    if (row) {
      row.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }
  };

  return (
    <>
      <Space.Compact className="w-full">
        <Select
          placeholder="请选择"
          // suffixIcon={<EllipsisOutlined onClick={moreIconClick} />}
          labelInValue={labelInValue}
          showSearch
          maxTagPlaceholder={(omittedValues) => {
            const title = omittedValues.map(({ label, value }) => (
              <Text key={value} className="text-white">
                {label}
              </Text>
            ));
            const len = omittedValues.length;

            return (
              <Tooltip
                styles={{ root: { pointerEvents: 'none' } }}
                title={<Flex vertical>{title}</Flex>}
              >
                <span>{`+ ${len} ...`}</span>
              </Tooltip>
            );
          }}
          {...restFieldProps}
          {...fieldProps}
          className={`${restFieldProps.className} h-xl`}
          style={{ ...style, width: 'calc(100% - 32px)' }}
          mode={mode}
          options={options}
          value={value}
          onChange={handleSelectChange}
          dropdownStyle={{
            minWidth:
              columnLens > MAX_COLUMNS
                ? `${POPUP_MATCH_SELECT_WIDTH_MORE * columnLens}px`
                : '300px',
            ...(needLimitWidth ? {} : { width: '600px' }),
          }}
          loading={requestLoading}
          filterOption={false}
          maxTagCount="responsive"
          onSearch={handleSearch}
          onDropdownVisibleChange={handleDropdownVisibleChange}
          onClear={handlerClear}
          open={dropdownVisible}
          onKeyDown={handleKeyDown}
          dropdownRender={() => {
            return (
              <Flex vertical>
                <Table
                  style={{
                    width:
                      columnLens > MAX_COLUMNS && needLimitWidth
                        ? POPUP_MATCH_SELECT_WIDTH_MORE * columnLens
                        : '100%',
                  }}
                  className={`${cx(styles.select_table)} api_select_table`}
                  scroll={{ y: 200 }}
                  dataSource={requestList}
                  loading={requestLoading}
                  pagination={false}
                  rowClassName={(record, index) => {
                    const isSelected = selectedValueList.includes(
                      record?.[selectValueField] as IOptionValue,
                    );
                    const selectedClass =
                      isSelected || index === selectedRowIndex;

                    const hasMouseSelectedClass =
                      isSelected && index === selectedRowIndex;

                    return clsx({
                      table_row_is_selected: selectedClass,
                      mouse_mode_row_is_selected: hasMouseSelectedClass,
                    });
                  }}
                  onRow={(record, index) => ({
                    onClick: () => {
                      setSelectedRowIndex(index as number);
                      handleRowClick(record);
                    },
                  })}
                  {...selectTableProps}
                  id={'api_table_select_1756522984038' + selectTableId}
                  columns={selectTableColumns}
                  onScroll={handleScroll}
                  summary={() => {
                    return !hasMoreData ? (
                      <Table.Summary.Row>
                        <Table.Summary.Cell
                          className="text-center"
                          index={0}
                          colSpan={selectTableColumns.length}
                        >
                          {emptyText}
                        </Table.Summary.Cell>
                      </Table.Summary.Row>
                    ) : null;
                  }}
                />
                {extraNode ? (
                  <>
                    <Flex className="pt-xxs" align="center" justify="center">
                      {extraNode}
                    </Flex>
                  </>
                ) : null}
              </Flex>
            );
          }}
        />
        <Button
          color="default"
          variant="outlined"
          style={{ backgroundColor: 'rgba(0, 0, 0, 0.02)' }}
          icon={<EllipsisOutlined />}
          disabled={restFieldProps?.disabled}
          onClick={moreIconClick}
        />
      </Space.Compact>
      {moreContent?.(setSelectDisplayValue)}
    </>
  );
};

const useStyles = createStyles(({ css, token }) => ({
  select_table: css`
    height: 200px !important;
    .ant-table-tbody {
      .ant-table-row {
        cursor: pointer;
      }
      .table_row_is_selected {
        background-color: ${token.colorPrimaryBg};
        &:hover {
          .ant-table-cell {
            background-color: ${token.colorPrimaryBgHover};
          }
        }
      }

      .mouse_mode_row_is_selected {
        background-color: ${token.colorPrimaryBgHover};
      }

      .table_key_down_row_hover {
        background-color: ${token.colorPrimaryBgHover};
      }
    }
  `,
}));
