import {
  GoodsImage,
  Typography,
  type CollectionProps,
  type TableColumnType,
} from '@slt/base-ui';
import type { QueryFormListItem } from '@slt/biz-ui';
import {
  basicBrandListTenantBrand,
  basicTagListTenantTag,
  BasicTagType,
  bossBookPublisherSelectInput,
  type BasicGoodsPageSkuDTO,
} from '@slt/services';
import {
  DistributionTypeEnum,
  GoodsCombineType,
  PLACEHOLDER,
} from '@slt/utils';
import { GoodsNoContent } from './GoodsNoContent';
import { isNil } from 'lodash-es';

export enum SelectionModalTabsType {
  '普通自有' = 'ordinaryOwned',
  '普通分销' = 'ordinaryDistribution',
  '组合' = 'combine',
}

export const goodsSelectModalTabsMap = Object.freeze<
  Record<SelectionModalTabsType, string>
>({
  [SelectionModalTabsType['普通自有']]: '商品',
  [SelectionModalTabsType['普通分销']]: '分销',
  [SelectionModalTabsType['组合']]: '组合',
});

export const queryParamsByTabsType = {
  [SelectionModalTabsType['普通自有']]: {
    distributionType: DistributionTypeEnum.Owned,
    type: GoodsCombineType['普通'],
  },
  [SelectionModalTabsType['普通分销']]: {
    distributionType: DistributionTypeEnum.Distribution,
    type: GoodsCombineType['普通'],
  },
  [SelectionModalTabsType['组合']]: { type: GoodsCombineType['组合'] },
};

export const getModalTableSearchColumns = (options: {
  isCombine?: boolean;
  activeKey?: SelectionModalTabsType;
  isSpuDimension?: boolean;
  showDisabled?: boolean;
}): QueryFormListItem[] => {
  const { isCombine: isCom, activeKey, isSpuDimension, showDisabled } = options;
  const isCombine = isNil(isCom)
    ? activeKey === SelectionModalTabsType['组合']
    : isCom;

  let hideNames: string[] = [];
  if (isSpuDimension || isCombine) {
    hideNames = isCombine
      ? ['goodsSkuNoList', 'isbns', 'bookName']
      : ['goodsSkuNoList'];
  }

  const nameOptions: CollectionProps['nameOptions'] = [
    {
      type: 'input',
      formItemProps: {
        label: '快速查询',
        name: 'commonQuery',
      },
      fieldProps: {
        placeholder: `请输入${isCombine ? '组合名称' : '商品名称/规格名称/ISBN'}查询`,
        allowClear: true,
      },
    },
    {
      type: 'batchInput',
      formItemProps: {
        label: isCombine ? '组合编码' : '商品编码',
        name: 'goodsNo',
      },
      fieldProps: {
        placeholder: `请输入${isCombine ? '组合' : '商品'}编码`,
        allowClear: true,
        showConfig: false,
      },
    },
    {
      type: 'batchInput',
      formItemProps: {
        label: '规格编码',
        name: 'goodsSkuNoList',
      },
      fieldProps: {
        placeholder: '请输入规格编码',
        allowClear: true,
        showConfig: false,
      },
    },
    {
      type: 'batchInput',
      formItemProps: {
        label: 'ISBN',
        name: 'isbns',
      },
      fieldProps: {
        placeholder: '请输入ISBN',
        allowClear: true,
        showConfig: false,
      },
    },
    {
      type: 'input',
      formItemProps: {
        label: '图书名称',
        name: 'bookName',
      },
      fieldProps: {
        placeholder: '请输入图书名称',
        allowClear: true,
      },
    },
  ];

  const baseColumn = [
    {
      type: 'collection',
      fieldProps: {
        nameOptions: nameOptions.filter((r) => {
          return !hideNames.includes(r?.formItemProps?.name ?? '');
        }),
      },
    },
  ];

  const enableColumn = [
    {
      formItemProps: {
        label: '状态',
        name: 'enable',
      },
      key: 'enable',
      type: 'select',
      fieldProps: {
        allowClear: true,
        options: [
          { label: '启用', value: 1 },
          { label: '停用', value: 0 },
        ],
      },
    },
  ];

  if (activeKey !== SelectionModalTabsType['普通自有']) {
    return showDisabled ? [...baseColumn, ...enableColumn] : baseColumn;
  }

  return [
    ...baseColumn,
    {
      type: 'apiSelect',
      formItemProps: {
        label: '出版社',
        name: 'publisherIdList',
      },
      fieldProps: {
        placeholder: '请选择出版社',
        allowClear: true,
        mode: 'multiple',
        request: bossBookPublisherSelectInput,
      },
    },
    {
      formItemProps: {
        label: '商品品牌',
        name: 'brandIds',
      },
      key: 'brandIds',
      type: 'apiSelect',
      fieldProps: {
        allowClear: true,
        mode: 'multiple',
        searchKey: 'brandName',
        request: basicBrandListTenantBrand,
        fieldNames: { label: 'brandName', value: 'id' },
      },
    },
    {
      formItemProps: {
        label: '商品标签',
        name: 'productTagIds',
      },
      key: 'productTagIds',
      type: 'apiSelect',
      fieldProps: {
        placeholder: '请选择商品标签',
        allowClear: true,
        searchKey: 'tagName',
        mode: 'multiple',
        request: basicTagListTenantTag,
        params: {
          tagType: BasicTagType['商品标签'],
        },
        fieldNames: { label: 'tagName', value: 'id' },
      },
    },
    {
      type: 'input',
      formItemProps: {
        label: '商品备注',
        name: 'goodsRemarks',
      },
    },
    ...(showDisabled ? enableColumn : []),
  ];
};

export const getTableSelectColumns = (isSpuDimension: boolean) => {
  const basicColumns: TableColumnType<BasicGoodsPageSkuDTO>[] = [
    {
      key: 'goodsName',
      title: '商品名称',
    },
    {
      key: 'goodsNo',
      title: '商品编码',
      render: (_, record) => <GoodsNoContent record={record} smallSize />,
    },
    {
      key: 'goodsSkuName',
      title: '规格名称',
      render: (text, record) => {
        return (
          <Typography.Text>
            {record.type === GoodsCombineType['组合'] ? PLACEHOLDER : text}
          </Typography.Text>
        );
      },
    },
    {
      key: 'goodsSkuNo',
      title: '规格编码',
      render: (text, record) => {
        return (
          <Typography.Text>
            {record.type === GoodsCombineType['组合'] ? PLACEHOLDER : text}
          </Typography.Text>
        );
      },
    },
    {
      key: 'enable',
      title: '状态',
      render: (text: number) => {
        if (isNil(text)) {
          return <Typography.Text>{PLACEHOLDER}</Typography.Text>;
        }

        const txt = Number(text) === 1 ? '启用' : '停用';
        return <Typography.Text title={txt}>{txt}</Typography.Text>;
      },
    },
  ];
  // 商品维度时不展示[规格名称、规格编码]字段
  return basicColumns.filter(
    (r) => !isSpuDimension || !['goodsSkuNo', 'goodsSkuName'].includes(r.key),
  );
};

export const getModalTableColumns = ({
  isSpuDimension,
  combineTab,
  distributionTab,
}: {
  /** 商品维度 */
  isSpuDimension?: boolean;
  /** 自有组合/分销组合tab */
  combineTab?: boolean;
  /** 分销商品tab */
  distributionTab?: boolean;
}) => {
  if (combineTab) {
    const combineColumns: TableColumnType<BasicGoodsPageSkuDTO>[] = [
      {
        title: '主图',
        key: 'firstMainPicUrl',
        render: (url: string) => {
          if (!url) {
            return PLACEHOLDER;
          }

          return <GoodsImage size="small" key={url} src={url} />;
        },
      },
      {
        title: '组合名称',
        key: 'goodsName',
        width: 150,
      },
      {
        title: '组合编码',
        key: 'goodsNo',
        width: 150,
      },
      {
        title: '组合条码',
        key: 'barcode',
        width: 100,
      },
      {
        key: 'enable',
        title: '状态',
        render: (text: number) => {
          if (isNil(text)) {
            return <Typography.Text>{PLACEHOLDER}</Typography.Text>;
          }

          const txt = Number(text) === 1 ? '启用' : '停用';
          return <Typography.Text title={txt}>{txt}</Typography.Text>;
        },
      },
    ];
    return combineColumns;
  }

  if (distributionTab) {
    const distributionColumns: TableColumnType<BasicGoodsPageSkuDTO>[] = [
      {
        title: isSpuDimension ? '主图' : '规格图',
        key: 'picUrl',
        render: (_, record: BasicGoodsPageSkuDTO) => {
          const { mainPicUrlList = [], specPicUrlList = [] } = record;
          const src = isSpuDimension
            ? mainPicUrlList?.[0]?.url
            : specPicUrlList?.[0]?.url;
          return src ? <GoodsImage src={src} size="small" /> : PLACEHOLDER;
        },
      },
      {
        title: '商品名称',
        key: 'goodsName',
      },
      {
        title: '商品编码',
        key: 'goodsNo',
        render: (_, record) => (
          <GoodsNoContent
            record={record}
            combineTab={combineTab}
            distributionTab={distributionTab}
          />
        ),
      },
      {
        title: '规格名称',
        key: 'goodsSkuName',
        render: (text, record) => {
          return (
            <Typography.Text>
              {record.type === GoodsCombineType['组合'] ? PLACEHOLDER : text}
            </Typography.Text>
          );
        },
      },
      {
        title: '规格编码',
        key: 'goodsSkuNo',
        render: (text, record) => {
          return (
            <Typography.Text>
              {record.type === GoodsCombineType['组合'] ? PLACEHOLDER : text}
            </Typography.Text>
          );
        },
      },
      ...(distributionTab ? [{ title: '供应商', key: 'supplierName' }] : []),
      {
        title: '库存',
        key: 'quantityNo',
        render: (_, record) => {
          const { distributionType, quantityNo, supplierStock } = record;
          // 分销商品取供应商库存
          switch (distributionType) {
            case DistributionTypeEnum.Distribution:
              return supplierStock ?? 0;
            case DistributionTypeEnum.Owned:
              return quantityNo ?? 0;
            default:
              return 0;
          }
        },
      },
      { title: '标准售价', key: 'standardPrice' },
      { title: 'ISBN', key: 'isbn' },
      { title: '图书名称', key: 'bookName' },
      { title: '定价', key: 'price' },
      { title: '出版社', key: 'publisherName' },
      { title: '作者', key: 'author' },
      { title: '商品类型', key: 'goodsTypeName' },
      { title: '所属分类', key: 'categoryName' },
      { title: '商品条码', key: 'barcode' },
      {
        key: 'enable',
        title: '状态',
        render: (text: number) => {
          if (isNil(text)) {
            return <Typography.Text>{PLACEHOLDER}</Typography.Text>;
          }

          const txt = Number(text) === 1 ? '启用' : '停用';
          return <Typography.Text title={txt}>{txt}</Typography.Text>;
        },
      },
    ];
    return distributionColumns;
  }

  // 自由商品专属
  const basicColumns: TableColumnType<BasicGoodsPageSkuDTO>[] = [
    {
      title: isSpuDimension ? '主图' : '规格图',
      key: 'picUrl',
      render: (_, record: BasicGoodsPageSkuDTO) => {
        const { mainPicUrlList = [], specPicUrlList = [] } = record;
        const src = isSpuDimension
          ? mainPicUrlList?.[0]?.url
          : specPicUrlList?.[0]?.url;
        return src ? <GoodsImage src={src} size="small" /> : PLACEHOLDER;
      },
    },
    {
      title: '规格编码',
      key: 'goodsSkuNo',
      render: (text, record) => {
        return (
          <Typography.Text>
            {record.type === GoodsCombineType['组合'] ? PLACEHOLDER : text}
          </Typography.Text>
        );
      },
    },
    {
      title: '规格名称',
      key: 'goodsSkuName',
      render: (text, record) => {
        return (
          <Typography.Text>
            {record.type === GoodsCombineType['组合'] ? PLACEHOLDER : text}
          </Typography.Text>
        );
      },
    },
    {
      title: '商品编码',
      key: 'goodsNo',
      render: (_, record) => (
        <GoodsNoContent
          record={record}
          combineTab={combineTab}
          distributionTab={distributionTab}
        />
      ),
    },
    {
      title: '商品名称',
      key: 'goodsName',
    },
    {
      title: '商品简名',
      key: 'goodsShortName',
    },
    { title: 'ISBN', key: 'isbn' },
    { title: '出版社', key: 'publisherName' },
    { title: '定价', key: 'price' },
    { title: '图书名称', key: 'bookName' },
    { title: '作者', key: 'author' },
    {
      title: '商品品牌',
      key: 'brandName',
      render: (_, record) =>
        record.brandInfoList?.map((brand) => brand.brandName).join(','),
    },
    {
      title: '商品标签',
      key: 'productTagName',
      render: (_, record) =>
        record.tagInfoList?.map((tag) => tag.productTagName).join(','),
    },
    { title: '供应商', key: 'supplierName' },
    { title: '商品备注', key: 'goodsRemarks' },
    { title: '商品条码', key: 'barcode' },
    { title: '标准售价', key: 'standardPrice' },
    { title: '商品类型', key: 'goodsTypeName' },
    { title: '商家自建分类', key: 'categoryName' },
    {
      key: 'enable',
      title: '状态',
      render: (text: number) => {
        if (isNil(text)) {
          return <Typography.Text>{PLACEHOLDER}</Typography.Text>;
        }

        const txt = Number(text) === 1 ? '启用' : '停用';
        return <Typography.Text title={txt}>{txt}</Typography.Text>;
      },
    },
  ];

  // 商品维度时不展示[规格名称、规格编码]字段
  return basicColumns.filter(
    (r) => !isSpuDimension || !['goodsSkuNo', 'goodsSkuName'].includes(r.key),
  );
};
