import { BizTable, ExportBusinessType } from '@slt/biz-ui';
import { Flex, Typography } from '@slt/base-ui';
import { inventoryQueryInfoList } from '@slt/services';
import { useInventoryWarehouseQuery } from './useInventoryWarehouseQuery';

/** 分仓库查询 */
const InventoryWarehouseQuery = () => {
  const {
    form,
    initialSearchValues,
    summaryData,
    tableRef,
    searchColumns,
    tableColumns,
    beforeSearchData,
    toManualRecalculateCostPage,
  } = useInventoryWarehouseQuery();

  return (
    <Flex vertical className="h-full">
      <BizTable
        requestConfig={{
          api: inventoryQueryInfoList(),
          beforeData: {
            beforeLabelInValueKeys: ['goodsSkuNoList'],
            beforeSearchData,
          },
        }}
        exportConfig={{
          businessType: ExportBusinessType.分仓库存导出,
          authId: '2177',
        }}
        table={{
          rowSelection: false,
          tableRef,
          id: 'specificationDimensionQuery',
          rowKey: 'id',
          columns: tableColumns,
          wrapperClassName: 'px-sm',
        }}
        summaryData={summaryData}
        showRowIndex
        search={{
          form,
          searchColumns,
          initialValues: {
            ...initialSearchValues,
            enable: true,
          },
        }}
        toolbar={{
          left: (
            <Flex gap={8}>
              <Typography.Text type="secondary">
                注：成本均价，如果在产生业务后执行了修改期初库存数量及成本/插单/修单/删单，采用T+1的算法，成本需要在第二天才会更新显示为最新成本
              </Typography.Text>
              <Typography.Link
                authId="2261"
                onClick={toManualRecalculateCostPage}
              >
                去手工核算最新成本
              </Typography.Link>
            </Flex>
          ),
        }}
      />
    </Flex>
  );
};

export default InventoryWarehouseQuery;
