import { useOpenTab } from '@/hooks/useOpenTab';
import { useTabRefresh } from '@/hooks/useTabRefresh';
import { useUserInfo } from '@/store/system';
import {
  Button,
  CaretDownOutlined,
  Condition,
  Dropdown,
  Flex,
  InboxOutlined,
  message,
  Modal,
  msgInfo,
  msgSuccess,
  msgWarn,
  Space,
  Tree,
  Typography,
  useAuthProps,
  VerticalSplitPane,
  type DropdownMenuProps,
  type IUploadFile,
  type MenuProps,
} from '@slt/base-ui';
import type { CommonRecord } from '@slt/base-utils';
import type { BaseTagDTO, QueryFormListItem } from '@slt/biz-ui';
import {
  BatchDropdown,
  BizTable,
  CalculatePriceInputBasicPrice,
  DropdownButton,
  ExportBusinessType,
  ImportBusinessType,
  ImportButton,
  SelectTagModal,
} from '@slt/biz-ui';
import { net } from '@slt/net';
import { useMutation } from '@slt/net-query';
import {
  basicGoodsBatchUpdateTag,
  basicGoodsCloseSyncStock,
  basicGoodsGoodsImport,
  basicGoodsImportEditTemplateDownload,
  basicGoodsBatchModifyGoodsName,
  basicGoodsOpenSyncStock,
  basicGoodsPageSkuUrl,
  basicGoodsPageSpuUrl,
  basicGoodsBatchModifyBaseData,
  BasicTagType,
  bossTaskExport,
  goodsBatchDisableSku,
  goodsBatchDisableSpu,
  goodsBatchEnableSku,
  goodsBatchEnableSpu,
  goodsTotalDataService,
  basicGoodsUpdateSingleRowGoodsInfo,
  type BasicGoodsPageCommonParams,
  type BasicBatchModifyGoodsNameParams,
  type BasicGoodsPageSkuDTO,
  type BasicModifyBaseInfoParams,
  type BasicGoodsTotalDataParams,
} from '@slt/services';
import { DEFAULT_AMOUNT, UserType, type RawValue } from '@slt/utils';
import { createStyles } from 'antd-style';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { routerMap } from '../../../router/routerMap';
import {
  SelectedDimension,
  userExportConfigs,
  SelectedDimensionValue,
} from '../constants';
import { useColumns } from './useColumns';
import { noAuthMessage } from '@/constants/messageText';
import { BatchSyncFieldsInfoModal } from '@/pages/goodsStoreInfo/components/batchSyncFieldsInfoModal/BatchSyncFieldsInfoModal';
import { windowOpen } from '@slt/utils';

import BatchImportImgModal from './BatchImportImgModal';
import { CancelRelatedPackage } from './CancelRelatedPackage';
import { EditPrice } from './EditPrice';
import { LevelDistributionPrice } from './LevelDistributionPrice';
import { RelatedPackage } from './RelatedPackage';
import { authIdsMap } from '@/constants/authIds';
import { useWindowSize } from 'usehooks-ts';
import { useGoodsCategoryTreeData } from '@/hooks/bizHooks/useGoodsCategoryTreeData';
import { EditGoodsBrandModal } from './EditGoodsBrandModal';
import {
  EditBaseInfoModal,
  type EditBaseInfoModalFormFields,
} from '@/components/biz/editBaseInfoModal/EditBaseInfoModal';
import {
  getAddGoodsNameFixConfigs,
  getEditBaseInfoConfigs,
} from '../formConfig';
import { AddGoodsPrefixOrSuffixModal } from '@/components/biz/addGoodsPrefixOrSuffixModal/AddGoodsPrefixOrSuffixModal';
import { cloneDeep, isNil } from 'lodash-es';
import { useRequest } from 'ahooks';

const GoodsInfo = () => {
  const [summaryData, setSummaryData] = useState<CommonRecord>({});
  const [searchData, setSearchData] = useState<BasicGoodsPageCommonParams>();
  const searchParamsRef = useRef<CommonRecord>({});
  const { styles } = useStyles();

  const [visualAngle, setVisualAngle] = useState<'base' | 'inventory'>('base');

  const { mutate: exportGoodsList } = useMutation(bossTaskExport);
  const { runAsync: mutateGoodsTotalData } = useRequest(
    (data: BasicGoodsTotalDataParams) => net.fetch(goodsTotalDataService(data)),
    {
      manual: true,
    },
  );
  const { mutate: basicGoodsImportEditTemplateDownloadMutate } = useMutation(
    basicGoodsImportEditTemplateDownload,
  );
  const { mutate: batchUpdateGoodsTag } = useMutation(basicGoodsBatchUpdateTag);
  const { mutate: basicGoodsUpdateSingleRowGoodsInfoMutate } = useMutation(
    basicGoodsUpdateSingleRowGoodsInfo,
  );
  const { hasAuth: hasGoodsConfigAuth } = useAuthProps({ authId: '2116' });

  const {
    treeData,
    selectedNode,
    treeLoading,
    onTreeNodeChange,
    onTreeSortChange,
    onTreeNodeAdd,
    onSelectTreeNode,
  } = useGoodsCategoryTreeData();

  const userInfo = useUserInfo();

  const { openTab } = useOpenTab();

  const { height: windowHeight = 0 } = useWindowSize();

  const gotoDraft = useCallback(() => {
    openTab(routerMap.goodsInfoDraft.route);
  }, [openTab]);

  const {
    columns,
    searchColumns,
    selectedDimension,
    tableRef,
    onDelete,
    reloadTable,
  } = useColumns({
    visualAngle,
  });

  const isGoodsDimension = useMemo(
    () => selectedDimension === SelectedDimension.按商品,
    [selectedDimension],
  );

  const exportQueryType = useMemo(() => {
    return isGoodsDimension ? 0 : 1;
  }, [isGoodsDimension]);

  useTabRefresh({
    refreshFunc: () => {
      reloadTable();
    },
  });

  const onBatchDelete = useCallback(
    async (ids: RawValue[]) => {
      return await onDelete(ids as string[]);
    },
    [onDelete],
  );

  const onBatchDisable = useCallback(
    async (
      ids: RawValue[],
      _rows: any,
      _selectedRowKeys: any,
      extraParams?: CommonRecord,
    ) => {
      if (isGoodsDimension) {
        return await net.fetch(
          goodsBatchDisableSpu({
            ids: ids.join(','),
            continueFlag: extraParams?.continueFlag ? true : undefined,
          }),
        );
      } else {
        return await net.fetch(goodsBatchDisableSku(ids.join(',')));
      }
    },
    [isGoodsDimension],
  );

  const onBatchEnable = useCallback(
    async (ids: RawValue[]) => {
      if (isGoodsDimension) {
        return await net.fetch(goodsBatchEnableSpu(ids.join(',')));
      } else {
        return await net.fetch(goodsBatchEnableSku(ids.join(',')));
      }
    },
    [isGoodsDimension],
  );

  const onClickToCreate = useCallback(() => {
    openTab(routerMap.goodsInfoAdd.route, {
      search:
        selectedNode?.key &&
        selectedNode?.key !== '-1' &&
        selectedNode?.key !== '-2'
          ? {
              categoryId: selectedNode?.key,
              categoryName: selectedNode?.title,
            }
          : undefined,
    });
  }, [openTab, selectedNode?.key, selectedNode?.title]);

  const handleDropdownClick = useCallback<NonNullable<MenuProps['onClick']>>(
    ({ key }) => {
      // 判断是否有勾选，为勾选message提示
      const selectedRows =
        tableRef.current?.getSelectedRows()?.selectedRows ?? [];
      if (selectedRows.length === 0) {
        msgInfo('请先勾选数据');
        return;
      }

      const text = key === 'enableStockSync' ? '开启' : '关闭';

      Modal.confirm({
        title: `${text}库存同步`,
        content: `确认${text}库存同步吗？${text}后库存数量将${key === 'disableStockSync' ? '不会' : ''}同步到线上平台`,
        onOk: async () => {
          // 处理开启或关闭库存同步的逻辑
          const api =
            key === 'enableStockSync'
              ? basicGoodsOpenSyncStock
              : basicGoodsCloseSyncStock;
          const res = await net.fetch(
            api({ skuIdList: selectedRows.map((row) => row.id as string) }),
          );
          if (res.success) {
            msgSuccess(`${text}库存同步成功`);
            reloadTable();
          }
        },
      });
      // 勾选则调用相关接口
    },
    [reloadTable, tableRef],
  );

  const handleImportGoodsList = useCallback(
    async (
      fileInfo: IUploadFile | undefined,
      cb?: () => void,
      isImportEdit?: boolean,
    ) => {
      if (fileInfo) {
        const { url, size, name } = fileInfo;
        const res = await net.fetch(
          basicGoodsGoodsImport({
            fileName: name,
            fileUrl: url,
            fileSize: size,
            fileType: isImportEdit ? 2 : 1,
          }),
        );
        if (res.success) {
          cb?.();
          msgSuccess('操作成功');
          openTab(routerMap.importResult.route, {
            search: {
              businessType: isImportEdit
                ? ImportBusinessType.商品导入修改
                : ImportBusinessType.商品导入新增,
            },
          });
        }
      }
    },
    [openTab],
  );

  const onBatchUpdateGoodsTag = useCallback(
    async (tags: BaseTagDTO[], type: 'set' | 'clear') => {
      const selectRows =
        tableRef.current?.getSelectedRows()?.selectedRows || [];
      const goodsIdList: string[] = Array.from(
        new Set(
          selectRows.map((r) =>
            isGoodsDimension ? (r.id as string) : (r.goodsId as string),
          ),
        ),
      );

      const params = {
        goodsIdList,
        tagIdList: tags.map((r) => r.id),
        operateType: type === 'set' ? 1 : 2, // 1-追加, 2-覆盖
      };
      const res = await batchUpdateGoodsTag(params);
      if (res.success) {
        msgSuccess(`批量${type === 'set' ? '添加' : '移除'}标签成功`);
        tableRef?.current?.reload();
      }
    },
    [tableRef, isGoodsDimension, batchUpdateGoodsTag],
  );

  const handleTransformBaseInfoData = useCallback(
    (data: EditBaseInfoModalFormFields) => {
      const idKey =
        selectedDimension === SelectedDimension['按规格'] ? 'goodsId' : 'id';
      const selectedRows =
        (tableRef.current?.getSelectedRows()
          .selectedRows as BasicGoodsPageSkuDTO[]) || [];
      if (!selectedRows || selectedRows.length === 0) {
        return;
      }

      const { categoryCode, supplierIdList, bookCategoryId } = data;
      const spuIdList = selectedRows
        .map((item) => item[idKey])
        .filter((item) => item !== undefined);
      const params: BasicModifyBaseInfoParams = {
        spuIdList,
        categoryCode: categoryCode?.value,
        bookCategoryId: bookCategoryId?.key,
        supplierIdList,
      };
      return params;
    },
    [tableRef, selectedDimension],
  );

  const handleTransformGoodsFixData = useCallback(
    (data: BasicBatchModifyGoodsNameParams) => {
      const idKey =
        selectedDimension === SelectedDimension['按规格'] ? 'goodsId' : 'id';
      const selectedRows =
        (tableRef.current?.getSelectedRows()
          .selectedRows as BasicGoodsPageSkuDTO[]) || [];
      if (!selectedRows || selectedRows.length === 0) {
        return;
      }

      const spuIdList = selectedRows
        .map((item) => item[idKey])
        .filter((item) => item !== undefined);
      return {
        ...data,
        spuIdList,
      };
    },
    [tableRef, selectedDimension],
  );

  const relationPackageItems: DropdownMenuProps['items'] = useMemo(() => {
    return [
      {
        key: 'relatedPackage',
        authId: '2101',
        label: (
          <RelatedPackage
            trigger={<Typography.Text>批量关联素材包</Typography.Text>}
            tableRef={tableRef}
            rowKey={isGoodsDimension ? 'id' : 'goodsId'}
          />
        ),
      },
      {
        key: 'cancelRelatedPackage',
        authId: '2101',
        label: (
          <CancelRelatedPackage
            trigger={<Typography.Text>批量取消关联</Typography.Text>}
            tableRef={tableRef}
            rowKey={isGoodsDimension ? 'id' : 'goodsId'}
          />
        ),
      },
    ];
  }, [tableRef, isGoodsDimension]);

  const productUpdateItems: DropdownMenuProps['items'] = useMemo(() => {
    return [
      {
        key: 'relatedPackage',
        authId: '2162',
        label: (
          <Typography.Text
            onClick={() => openTab(routerMap.standardProductUpdate.route)}
          >
            标品更新
          </Typography.Text>
        ),
      },
      {
        key: 'cancelRelatedPackage',
        authId: '2162',
        label: (
          <Typography.Text
            onClick={() => openTab(routerMap.standardProductReference.route)}
          >
            标品引用
          </Typography.Text>
        ),
      },
    ];
  }, [openTab]);

  const operationItems: DropdownMenuProps['items'] = useMemo(() => {
    return [
      {
        key: 'editBaeInfo',
        label: (
          <EditBaseInfoModal
            formItemList={getEditBaseInfoConfigs()}
            tableRef={tableRef}
            request={basicGoodsBatchModifyBaseData}
            transformData={handleTransformBaseInfoData}
          />
        ),
        authId: '64',
      },
      {
        key: 'editBasicPrice',
        label: (
          <EditPrice
            authId="64"
            title="基础分销价"
            basePrice={CalculatePriceInputBasicPrice.基础分销价}
            type={selectedDimension}
            tableRef={tableRef}
          />
        ),
        authId: '64',
      },
      {
        key: 'editLevelPrice',
        label: (
          <LevelDistributionPrice
            tableRef={tableRef}
            authId="64"
            type={selectedDimension}
          />
        ),
        authId: '64',
      },
      {
        key: 'editStandardPrice',
        label: (
          <EditPrice
            authId="64"
            title="标准售价"
            basePrice={CalculatePriceInputBasicPrice.标准售价}
            type={selectedDimension}
            tableRef={tableRef}
          />
        ),
        authId: '64',
      },
      {
        key: 'editLowestPrice',
        label: (
          <EditPrice
            authId="64"
            title="最低售价"
            basePrice={CalculatePriceInputBasicPrice.最低售价}
            type={selectedDimension}
            tableRef={tableRef}
          />
        ),
        authId: '64',
      },
      {
        key: 'updateGoodsTag',
        label: (
          <SelectTagModal
            title="设置商品标签"
            tagType={BasicTagType['商品标签']}
            scene="batch"
            tableRef={tableRef}
            btnText={<Typography.Text>修改商品标签</Typography.Text>}
            onOk={onBatchUpdateGoodsTag}
          />
        ),
        authId: '64',
      },
      {
        key: 'editGoodsBrand',
        label: (
          <EditGoodsBrandModal tableRef={tableRef} type={selectedDimension} />
        ),
        authId: '64',
      },
      {
        key: 'batchImportImg',
        label: (
          <BatchImportImgModal
            authId="64"
            pageType="goods"
            importApi={basicGoodsGoodsImport}
          />
        ),
        authId: '64',
      },
      {
        key: 'addGoodsFix',
        label: (
          <AddGoodsPrefixOrSuffixModal
            formItemList={getAddGoodsNameFixConfigs()}
            tableRef={tableRef}
            request={basicGoodsBatchModifyGoodsName}
            transformData={handleTransformGoodsFixData}
            initialValues={{ modifyType: 1 }}
          />
        ),
        authId: '64',
      },
      {
        key: 'syncStoreInfo',
        label: (
          <BatchSyncFieldsInfoModal
            tableRef={tableRef}
            goodsIdKey={isGoodsDimension ? 'id' : 'goodsId'}
          />
        ),
        authId: '64',
      },
    ];
  }, [
    selectedDimension,
    tableRef,
    onBatchUpdateGoodsTag,
    handleTransformGoodsFixData,
    handleTransformBaseInfoData,
    isGoodsDimension,
  ]);

  const handleExportCheck = useCallback(async () => {
    const values = {
      ...searchParamsRef.current,
      pageSize: 200,
      queryType: exportQueryType,
    };
    const params = {
      templateId: ExportBusinessType.商品信息导出,
      fileName: ExportBusinessType[ExportBusinessType.商品信息导出],
      queryCondition: values,
      userExportConfigs,
    };
    const res = await exportGoodsList(params);
    if (res.success) {
      openTab(routerMap.exportResult.route, {
        search: { businessType: ExportBusinessType.商品信息导出 },
      });
    }
  }, [exportQueryType, exportGoodsList, openTab]);

  const leftToolbar = useMemo(() => {
    return (
      <Space>
        <Button
          authId={authIdsMap.goodsInfo.add}
          type="primary"
          onClick={onClickToCreate}
        >
          新增
        </Button>
        <ImportButton
          authIds={{
            addImport: authIdsMap.goodsInfo.add,
            editImport: authIdsMap.goodsInfo.edit,
          }}
          addImportDownloadCustomApi={async () => {
            const {
              data,
              code,
              message: errorMessage,
            } = await basicGoodsImportEditTemplateDownloadMutate({
              queryType: exportQueryType,
            });

            if (code === 400) {
              message.error(errorMessage as string);
            } else {
              windowOpen({ route: `${data}?t=${Date.now()}` });
            }
          }}
          onDownloadEditTemplate={handleExportCheck}
          showItems={['addImport', 'editImport', 'queryImport']}
          businessType={ImportBusinessType.商品导入新增}
          title="导入新增商品"
          editImportTitle="导入修改商品"
          importDesc={[
            {
              text: '请谨慎填写商品的ISBN，若由于填写的ISBN本身错误或不规范导致匹配到的图书标品有误，书链通不承担责任',
              style: {
                color: '#FF4D4F',
              },
            },
            {
              text: '商品类型未填写，导入时优先按ISBN匹配书链通图书标品库，未匹配到继续用商品编码匹配；当匹配到时商品的图书相关字段以标品为准，商品类型=图书类，未匹配到时，商品类型=准图书类(缺少资料)',
              isImportant: true,
            },
            {
              text: '商品类型填写，按照填写的商品类型对应的商品保存逻辑执行必填校验',
            },
          ]}
          editImportDesc={[
            {
              text: '规格编码为必填字段；导入模板只要包含规格编码即可导入（如比下载的模板多了列/缺少列/列顺序不同）',
              isImportant: true,
            },
            {
              text: '下载模板时将默认导出当前列表查询出的商品信息；若未做查询则导出全部商品',
              isImportant: true,
            },
            {
              text: '导入时按规格编码匹配，匹配到则覆盖更新',
            },
            {
              text: '准图书类商品导入时会按ISBN或商品编码匹配官方标品；匹配到商品的图书相关字段均取官方标品，不以模板为准；未匹配到则按模板导入',
            },
          ]}
          onOk={handleImportGoodsList}
        />
        <BatchDropdown
          tableRef={tableRef}
          reload={reloadTable}
          enableProps={
            isGoodsDimension
              ? {
                  batchResult: true,
                  tableId: 'goodsInfoListEnable',
                  authId: '67',
                  onConfirm: onBatchEnable,
                }
              : undefined
          }
          disableProps={
            isGoodsDimension
              ? {
                  batchResult: true,
                  tableId: 'goodsInfoListDisable',
                  authId: '66',
                  onConfirm: onBatchDisable,
                  copyKey: 'code',
                  getTableColumns: () => [
                    {
                      title: '商品编码',
                      dataIndex: 'code',
                      key: 'code',
                    },
                    {
                      title: '商品名称',
                      dataIndex: 'name',
                      key: 'name',
                      width: 80,
                    },
                    {
                      title: '失败原因',
                      dataIndex: 'failureReason',
                      key: 'failureReason',
                    },
                  ],
                }
              : undefined
          }
          deleteProps={{
            batchResult: true,
            isAsync: true,
            tableId: 'goodsInfoListDelete',
            authId: '65',
            getTableColumns: () => [
              {
                title: '商品编码',
                dataIndex: 'code',
                key: 'code',
              },
              {
                title: '商品名称',
                dataIndex: 'name',
                key: 'name',
                width: 80,
              },
              {
                title: '失败原因',
                dataIndex: 'failureReason',
                key: 'failureReason',
              },
            ],
            copyKey: 'code',
            onConfirm: onBatchDelete,
          }}
          customItems={{
            before: operationItems,
          }}
        />
        <Condition
          visible={
            !!(!isGoodsDimension && userInfo?.tenantType !== UserType.客户)
          }
        >
          <DropdownButton
            buttonText="库存同步"
            menu={{
              onClick: handleDropdownClick,
              items: [
                {
                  authId: '2093',
                  label: '开启库存同步',
                  key: 'enableStockSync',
                },
                {
                  authId: '2093',
                  label: '关闭库存同步',
                  key: 'disableStockSync',
                },
              ],
            }}
          />
        </Condition>
        <Condition visible>
          <DropdownButton
            buttonText="批量关联素材包"
            overlayClassName={styles.dropdown}
            key="relationPackage"
            menu={{ items: relationPackageItems }}
          />
        </Condition>
        <Condition visible>
          <DropdownButton
            overlayClassName={styles.dropdown}
            key="productUpdate"
            buttonText="商品更新"
            menu={{ items: productUpdateItems }}
          />
        </Condition>
        <Button
          onClick={() => {
            if (!hasGoodsConfigAuth) {
              msgWarn(noAuthMessage);
              return;
            }

            openTab(routerMap.systemConfiguration.route, {
              tabSearch: {},
            });
          }}
        >
          商品配置
        </Button>
      </Space>
    );
  }, [
    onClickToCreate,
    handleExportCheck,
    handleImportGoodsList,
    tableRef,
    reloadTable,
    isGoodsDimension,
    onBatchEnable,
    onBatchDisable,
    onBatchDelete,
    operationItems,
    userInfo?.tenantType,
    handleDropdownClick,
    styles.dropdown,
    relationPackageItems,
    productUpdateItems,
    basicGoodsImportEditTemplateDownloadMutate,
    exportQueryType,
    hasGoodsConfigAuth,
    openTab,
  ]);

  const rightToolbar = useMemo(() => {
    return (
      <Flex gap={8} align="center">
        <Dropdown
          menu={{
            items: [
              {
                label: '基础视角',
                key: 'base',
                onClick: () => {
                  tableRef?.current?.resetRowEditState();
                  setVisualAngle('base');
                },
              },
              {
                label: '库存视角',
                key: 'inventory',
                onClick: () => {
                  tableRef?.current?.resetRowEditState();
                  setVisualAngle('inventory');
                },
              },
            ],
          }}
        >
          <Button className="!gap-0 !p-xxs" type="text">
            <Typography.Text>
              {visualAngle === 'base' ? '基础视角' : '库存视角'}
            </Typography.Text>
            <CaretDownOutlined />
          </Button>
        </Dropdown>
        <InboxOutlined
          className="cursor-pointer px-xs py-xxs text-[18px] text-primary"
          onClick={gotoDraft}
        />
      </Flex>
    );
  }, [visualAngle, gotoDraft]);

  useEffect(() => {
    if (visualAngle === 'base' || !searchData) {
      return;
    }

    const newParams = {
      ...searchData,
      queryType: exportQueryType,
    };

    mutateGoodsTotalData(newParams)
      .then((res) => {
        if (res.data) {
          const {
            /** 实际库存合计 */
            totalQuantityNo = DEFAULT_AMOUNT,
            /** 库存金额合计 */
            totalBasicSellingPriceAmount = DEFAULT_AMOUNT,
            /** 可销售库存合计 */
            totalSellableQuantityNo = DEFAULT_AMOUNT,
            /** 可发货库存合计 */
            totalStockDeliver = DEFAULT_AMOUNT,
          } = res.data;
          setSummaryData({
            quantityNo: totalQuantityNo,
            basicSellingPriceAmount: totalBasicSellingPriceAmount,
            sellableQuantityNo: totalSellableQuantityNo,
            stockDeliver: totalStockDeliver,
          });
        } else {
          setSummaryData({});
        }
      })
      .catch(() => {
        //
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchData, mutateGoodsTotalData, visualAngle]);

  const tableId = useMemo(() => {
    // 兼容之前存储的配置，仅在库存视角添加tableId的后缀
    const inventoryVisual = visualAngle === 'inventory' ? visualAngle : '';
    return 'goodsInfoList' + selectedDimension + inventoryVisual;
  }, [selectedDimension, visualAngle]);

  return (
    <VerticalSplitPane
      config={{
        minSize: 220,
      }}
      leftContext={
        <Tree
          height={windowHeight - 142}
          treeData={treeData}
          showSearch
          showAdd
          showDelete
          showEdit
          treeLoading={treeLoading}
          onTreeNodeChange={onTreeNodeChange}
          onTreeSortChange={onTreeSortChange}
          onTreeNodeAdd={onTreeNodeAdd}
          onSelect={onSelectTreeNode}
          deleteConfirm={{
            content: '确定要删除该分类吗？',
          }}
          searchPlaceholder="请输入分类名称定位"
        />
      }
      rightContext={
        <BizTable
          isTableFullScreen={false}
          requestConfig={{
            api: {
              url:
                selectedDimension === SelectedDimension['按规格']
                  ? basicGoodsPageSkuUrl
                  : basicGoodsPageSpuUrl,
            },
            beforeData: {
              beforeSearchData: (data: BasicGoodsPageCommonParams) => {
                const { bookCategoryId } = data;
                const newData = cloneDeep(data);
                if (
                  bookCategoryId &&
                  typeof bookCategoryId !== 'string' &&
                  (bookCategoryId as CommonRecord)?.key
                ) {
                  newData.bookCategoryId = (bookCategoryId as CommonRecord)
                    .key as string;
                }

                setSearchData(newData);
                return newData;
              },
            },
            extraParams: {
              distributionType: 1,
              // 不知道什么意思，后端让加的
              type: 1,
              draft: 0,
              categoryId: selectedNode?.key,
            },
            onBeforeRequest: (params) => {
              searchParamsRef.current = params;
            },
          }}
          exportConfig={{
            businessType: isGoodsDimension
              ? ExportBusinessType.商品信息按商品导出
              : ExportBusinessType.商品信息按规格导出,
            authId: '2178',
            tableId,
          }}
          table={{
            id: tableId,
            virtual: true,
            columns,
            tableRef,
            rowKey: 'id',
            rowSelection: {
              type: 'checkbox',
              fixed: 'left',
              columnWidth: 42,
            },
            wrapperClassName: 'px-[12px]',
            batchCopyTooltip: '批量复制当前页数据',
          }}
          rowEditConfig={{
            enabled: true,
            submit: async (changeProps, record, dataSource) => {
              const change = cloneDeep(changeProps);
              if (
                Object.hasOwnProperty.call(change, 'referenceCost') &&
                !isNil(change.referenceCost)
              ) {
                const cost = Number(change.referenceCost);
                change.referenceCost = !isNaN(cost) ? cost.toFixed(4) : null;
              }

              const params = isGoodsDimension
                ? {
                    id: record.id,
                    goodsId: record?.id,
                    goodsDimension: SelectedDimensionValue.按商品,
                    referenceCost: record?.referenceCost,
                    goodsName: record?.goodsName,
                    goodsShortName: record?.goodsShortName,
                    goodsRemarks: record?.goodsRemarks,
                    ...change,
                  }
                : {
                    goodsId: record.goodsId,
                    goodsSkuId: record?.id,
                    goodsDimension: SelectedDimensionValue.按规格,
                    goodsSkuName: record?.goodsSkuName,
                    referenceCost: record?.referenceCost,
                    goodsName: record?.goodsName,
                    goodsShortName: record?.goodsShortName,
                    goodsRemarks: record?.goodsRemarks,
                    ...change,
                  };
              const res =
                await basicGoodsUpdateSingleRowGoodsInfoMutate(params);
              if (res.success) {
                msgSuccess('编辑成功');
                if (isGoodsDimension) {
                  return change;
                } else {
                  //如果修改中包含修改了规格名称的话则只更新当前的其他的不管
                  const allData =
                    dataSource?.filter(
                      (item) => item.goodsId === record.goodsId,
                    ) ?? [];
                  if (allData?.length) {
                    return allData.map((skuItem) => {
                      return {
                        key: skuItem.id,
                        update:
                          skuItem?.id === record?.id
                            ? change
                            : {
                                ...change,
                                goodsSkuName: skuItem.goodsSkuName,
                              },
                      };
                    });
                  } else {
                    return false;
                  }
                }
              }
              return false;
            },
          }}
          summaryData={visualAngle === 'base' ? undefined : summaryData}
          search={{
            searchColumns: searchColumns as QueryFormListItem[],
            className: 'px-[8px]',
          }}
          toolbar={{
            left: leftToolbar,
            right: rightToolbar,
          }}
        />
      }
    />
  );
};

export default GoodsInfo;

const useStyles = createStyles(({ css }) => ({
  dropdown: css`
    .ant-dropdown-menu-item {
      padding: 0 !important;

      .ant-dropdown-menu-title-content {
        display: flex;
      }

      .ant-typography {
        padding: 5px 12px;
        width: 100%;
      }
    }
  `,
}));
