import type { EditableTableRef, FormItemConfigDTO } from '@slt/base-ui';
import {
  basicImageType,
  Button,
  ConfigForm,
  defaultAcceptImageSuffix,
  Flex,
  Form,
  imageValidatorRules,
  message,
  Modal,
  Typography,
  useAuthCheck,
  SysTooltip,
  Condition,
  useAuthProps,
} from '@slt/base-ui';
import { useAuthCheckMap } from '@/hooks/useAuthCheckMap';
import {
  basicGoodsChangeConfigSyncQueryConfigByType,
  basicGoodsChangeConfigSyncSaveOrUpdate,
  fareTemplateSelectInputPlatformData,
  findOne,
  goodsStoreInfoModify,
  goodsStoreInfoSave,
  type AddOrUpdateParams,
  type DistributionDetailDTO,
  type DistributionSku,
  type GoodsChangeConfigSyncSaveOrUpdateParams,
  type GoodsChangeSyncConfigByTypeDTO,
} from '@slt/services';
import {
  DistributionInformationConfigField,
  DistributionTypeEnum,
  echoUploadImg,
  GoodsInfoSyncConfigField,
  GoodsInfoSyncConfigType,
  ResourceAssociateEnum,
  type DataImageNumberType,
} from '@slt/utils';
import { DistributionSpuCode } from './DistributionSpuCode';
import { ListingLink } from './ListingLink';
import { SpecificationDetails } from './SpecificationDetails';
import {
  formatInitialData,
  formatMaterials,
  formatSubmitData,
  HTTP_REG,
  PLATFORM_REG,
} from './utils';

import {
  OnlineGoodsModal,
  type OnlineGoodsModalRef,
} from '@/pages/goodsStoreInfo/components/OnlineGoodsModal';
import { MarketStatusEnum } from '@/pages/goodsStoreInfo/utils/enum';
import { useActions } from '@/store/system';
import { GoodsCategoryCascader } from '@slt/biz-ui';
import { useMutation, useQuery } from '@slt/net-query';
import {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
  type ChangeEvent,
} from 'react';
import { cloneDeep, isEmpty, isNil } from 'lodash-es';
import { v4 as uuidv4 } from 'uuid';
import { useGoodsStoreInfoContext } from '../context';
import useStyles from '../styles';
import { SelectPackages, type RelatedResource } from '@/components/biz';
import { RelationPackage } from './RelationPackage';
import { enumTransformArray, type CommonRecord } from '@slt/base-utils';
import type { UploadStatus } from '@slt/upload-core';

const syncToGoodsInfoFields = enumTransformArray<{
  label: string;
  value: string;
}>(GoodsInfoSyncConfigField).map(({ label, value }) => ({
  label: label === '商品名称' ? '资料名称' : label,
  value,
}));
const LABEL_WIDTH = 100;

interface Props {
  id?: string;
  copyId?: string;
  type: 'add' | 'edit' | 'view';
}

export type GoodsStoreInfoFormValues = AddOrUpdateParams & {
  resource?: {
    id: string;
    name: string;
  };
  specificationDetails?: DistributionSku[];
  mainImage?: { url: string; status: UploadStatus }[];
  detailImage?: { url: string; status: UploadStatus }[];
  syncSetting?: string[];
  subImage?: { url: string; status: UploadStatus }[];
  materialImage?: { url: string; status: UploadStatus }[];
  mainVideo?: { url: string; status: UploadStatus }[];
  category?: { key: string; title: string };
  shippingTemplate?: { label: string; value: string };
};

export const GoodsStoreInfoForm = (props: Props) => {
  const { id, copyId, type } = props;

  const { data: syncConfigData } = useQuery(
    basicGoodsChangeConfigSyncQueryConfigByType(
      GoodsInfoSyncConfigType['平台铺货资料'],
    ),
  );

  const { hasAuth: hasInfoCodeAuth } = useAuthProps({
    authId: '2320',
  }); // 是否有平台铺货资料-修改资料编码/规格编码的权限

  const syncConfigIdRef = useRef<string>();

  const originSyncConfigData = useRef<GoodsChangeSyncConfigByTypeDTO | null>(
    null,
  );
  const pricingPrice = useRef<number | undefined>(undefined);

  const { isAdd, isEdit, isView } = useMemo(
    () => ({
      isAdd: type === 'add',
      isEdit: type === 'edit',
      isView: type === 'view',
    }),
    [type],
  );

  const [saveLoading, setSaveLoading] = useState(false);
  const [isDistributedGoods, setIsDistributedGoods] = useState(false);
  const [goodsId, setGoodsId] = useState<string | undefined>();

  const codeRepeatableRef = useRef<string | undefined>(undefined);
  const supplierRef = useRef<{ id: number; name: string } | undefined>();
  const distributionTypeRef = useRef<DistributionTypeEnum | undefined>();
  const onlineGoodsModalRef = useRef<OnlineGoodsModalRef>(null);
  const editableTableRef = useRef<EditableTableRef>(null);

  const { authCheckMap } = useAuthCheckMap();
  const { styles } = useStyles();
  const [form] = Form.useForm<GoodsStoreInfoFormValues>();

  /** 商品所有自动同步平台铺货资料的配置字段 */
  const allSyncFields = useMemo(
    () => Object.values(DistributionInformationConfigField),
    [],
  );

  const { closeAndRefreshPrevTab, closeActiveTab, updateFormValueToLatest } =
    useActions();
  useEffect(() => {
    if (syncConfigData) {
      syncConfigIdRef.current = syncConfigData?.id;
      originSyncConfigData.current = syncConfigData;
      const autoSyncFields = allSyncFields.filter(
        (r) => !!(syncConfigData as CommonRecord)?.[r],
      );

      if (isAdd) {
        form.setFieldsValue({
          syncSetting: [
            ...autoSyncFields,
            'syncLowerPrice',
            'syncStandPrice',
            'syncBasicPrice',
            'syncGradeBasicPrice',
            'syncGoodsWeight',
            'syncGoodsName',
            'syncGoodsSkuName',
            GoodsInfoSyncConfigField['商品编码'],
          ],
        });
      } else {
        form.setFieldsValue({
          syncSetting: autoSyncFields,
        });
      }
    } else {
      form.setFieldsValue({
        syncSetting: allSyncFields,
      });
    }

    updateFormValueToLatest();
  }, [syncConfigData, form, allSyncFields, isAdd, updateFormValueToLatest]);

  const { mutate: save } = useMutation(goodsStoreInfoSave);
  const { mutate: updateSyncConfig } = useMutation(
    basicGoodsChangeConfigSyncSaveOrUpdate,
  );
  const { mutate: update } = useMutation(goodsStoreInfoModify);

  const { platformInfo: detail } = useGoodsStoreInfoContext();

  const { data: copyDetail } = useQuery(findOne({ id: copyId! }), {
    queryOptions: {
      enabled: isAdd && !!copyId,
    },
  });

  const distributionSpuCode = Form.useWatch(['distributionSpuCode'], form);
  const distributionSpuName = Form.useWatch(['distributionSpuName'], form);
  const resource = Form.useWatch(['resource'], form);

  const { checkAuth } = useAuthCheck();

  useEffect(() => {
    if (detail) {
      // 保证编辑商品肯定有 goodsId
      setGoodsId(detail.goodsId);

      if (detail.supplierId && detail.supplierName) {
        supplierRef.current = {
          id: detail.supplierId,
          name: detail.supplierName,
        };
      }

      distributionTypeRef.current =
        detail.distributionType as DistributionTypeEnum;

      setIsDistributedGoods(
        detail.distributionType === DistributionTypeEnum.Distribution,
      );
    }
  }, [detail]);

  useEffect(() => {
    if (copyDetail) {
      const { distributionSku, ...rest } = copyDetail;

      const data = formatInitialData({
        ...rest,
        distributionSku: distributionSku?.map(({ id, ...restItem }) => ({
          ...restItem,
        })),
      }) as CommonRecord;

      const cloneData = cloneDeep(data);
      if (cloneData?.syncSetting) {
        delete cloneData?.syncSetting;
      }

      form.setFieldsValue({
        ...cloneData,
        specificationDetails: (
          cloneData?.specificationDetails as DistributionSku[]
        )?.reduce((pre: DistributionSku[], cur) => {
          const item = { ...cur };
          if ('stock' in item) {
            delete item.stock;
          }

          pre.push(item);
          return pre;
        }, []),
      });
    }
  }, [copyDetail, form]);

  const handleApplyListingLink = (
    data: { url: string; urlType: DataImageNumberType }[],
  ) => {
    const materials: unknown = formatMaterials(
      data.map((item, index) => ({
        url: item.url,
        materialType: item.urlType,
        orderDesc: index,
      })),
    );
    form?.setFieldsValue(materials as GoodsStoreInfoFormValues);
  };

  const handleApplyMatchGoods = (data: DistributionDetailDTO) => {
    const { distributionSku, ...rest } = data;

    const formatData = formatInitialData({
      ...rest,
      distributionSku: distributionSku?.map(({ id, ...restItem }) => ({
        ...restItem,
      })),
    }) as CommonRecord;

    const cloneData = cloneDeep(formatData);
    if (cloneData?.syncSetting) {
      delete cloneData?.syncSetting;
    }

    Object.entries(cloneData).forEach(([key, value]) => {
      // 为了避免多余的覆盖，只覆盖 number、string、非空三种类型

      if (typeof value === 'number') {
        form?.setFieldsValue({ [key]: value });
      } else if (typeof value === 'string') {
        form?.setFieldsValue({ [key]: value });
      } else if (!isEmpty(value)) {
        form?.setFieldsValue({ [key]: value as unknown });
      }
    });

    if (data.goodsId) {
      /**
       * 只要点击快速导入，就意味着绑定商品，就算改了编码，也是绑定到前一个导入的商品
       */
      setGoodsId(data.goodsId);

      if (data.supplierId && data.supplierName) {
        // 处理供应商关联逻辑
        supplierRef.current = {
          id: data.supplierId,
          name: data.supplierName,
        };
      }

      distributionTypeRef.current = data.distributionType;

      setIsDistributedGoods(
        data.distributionType === DistributionTypeEnum.Distribution,
      );
    }
  };

  const handleRepeatable = (msg?: string) => {
    codeRepeatableRef.current = msg;

    // 触发校验
    form?.validateFields(['distributionSpuCode']);
  };

  const handleRelatedPackage = (data: RelatedResource[]) => {
    if (data[0]) {
      form.setFieldValue('resource', {
        id: data[0].resourceId,
        name: data[0].resourceName,
      });

      if (data[0].cover) {
        if (data[0].mainPicList.length) {
          form.setFieldValue(
            'mainImage',
            data[0].mainPicList.map((item) => echoUploadImg(item.url)),
          );
          if (data[0].detailPicList.length) {
            form.setFieldValue(
              'detailImage',
              data[0].detailPicList.map((item) => echoUploadImg(item.url)),
            );
          }
        }
      }
    }
  };

  const handleSpuNameChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (type === 'add') {
      const { specificationDetails } = cloneDeep(form.getFieldsValue());

      if (specificationDetails && specificationDetails.length > 0) {
        specificationDetails[0].distributionSkuName = e.target.value;
      }

      form.setFieldValue('specificationDetails', specificationDetails);
    }
  };

  const syncSettingGoodsFields = useMemo(() => {
    return syncToGoodsInfoFields.filter(
      ({ value }) =>
        value !== 'syncGoodsSkuName' &&
        value !== 'syncGoodsName' &&
        value !== 'syncGoodsWeight' &&
        value !== 'syncGoodsNo',
    );
  }, []);

  const list: FormItemConfigDTO[] = [
    {
      formItemProps: {
        label: '选品市场分类',
        name: 'category',
      },
      renderFormItem: () => <GoodsCategoryCascader onlyEnable showSearch />,
    },
    {
      formItemProps: {
        label: '商品编码',
        name: 'distributionSpuCode',
        rules: [
          {
            required: true,
            message: '请输入商品编码',
          },
          () => ({
            validator(_, value) {
              if (!value || !codeRepeatableRef.current) {
                return Promise.resolve();
              }
              return Promise.reject(new Error(codeRepeatableRef.current));
            },
          }),
        ],
      },
      renderFormItem: () => (
        <DistributionSpuCode
          editId={id}
          distributionType={distributionTypeRef.current}
          supplier={supplierRef.current}
          pricingPriceRef={pricingPrice}
          type={type}
          disabled={
            !authCheckMap.goodsStoreInfo.editGoodsInfoCode && isEdit
              ? true
              : false
          }
          isDistributedGoods={isDistributedGoods}
          oldDistributionSpuCode={detail?.distributionSpuCode}
          onApply={handleApplyMatchGoods}
          onRepeatable={handleRepeatable}
        />
      ),
    },
    {
      type: 'input',
      formItemProps: {
        label: '资料名称',
        name: 'distributionSpuName',
        rules: [
          {
            required: true,
            message: '请输入资料名称',
          },
        ],
      },
      fieldProps: {
        showCount: true,
        maxLength: 100,
        onChange: handleSpuNameChange,
      },
    },
    {
      type: 'fareTemplateIncomeSelect',
      formItemProps: {
        label: '运费收入模板',
        name: 'shippingTemplate',
      },
      fieldProps: {
        labelInValue: true,
        request: fareTemplateSelectInputPlatformData,
      },
    },
    {
      formItemProps: {
        label: (
          <Flex align="center" gap={4}>
            <Typography.Text>关联素材包</Typography.Text>
            <SysTooltip
              title="可以从素材库中关联素材包，当下游分销商选品后，可以下载对应的素材包"
              placement="top"
              className="cursor-help"
            />
          </Flex>
        ),
        name: 'resource',
      },
      renderFormItem: () => <RelationPackage disabled={isView} />,
    },
    {
      formItemProps: {
        label: '宝贝链接抓图',
        name: 'listingLink',
        rules: [
          {
            validator: (_, value) => {
              if (!value) {
                return Promise.resolve();
              }

              if (!HTTP_REG.test(value)) {
                return Promise.reject(new Error('链接不正确'));
              }

              if (!PLATFORM_REG.test(value)) {
                return Promise.reject(
                  new Error(
                    '抓图失败 失败原因：仅支持京东、淘宝、天猫链接抓图',
                  ),
                );
              }

              return Promise.resolve();
            },
          },
        ],
      },
      renderFormItem: () => (
        <ListingLink onApply={handleApplyListingLink} disabled={isView} />
      ),
    },
    {
      formItemProps: {
        label: '主图',
        className: styles.imageFormItem,
      },
      renderFormItem: () => (
        <Typography.Text type="secondary">
          图片支持上传png、jpeg、jpg格式；每张不超过3M，不超过20张；建议宽高比1:1；建议800*800；拖动可调整图片顺序
        </Typography.Text>
      ),
    },
    {
      type: 'mediaUpload',
      formItemProps: {
        label: ' ',
        name: 'mainImage',
        rules: imageValidatorRules,
      },
      fieldProps: {
        readonly: isView,
        maxCount: 20,
        maxSingleSize: 10,
        customUploadType: basicImageType,
        canCrop: true,
        checkAfterUpload: true,
        checkRules: {
          acceptSuffix: defaultAcceptImageSuffix,
          maxSize: 3,
        },
        extraTagInfo: {
          title: '主图',
        },
        extraElementRender: (props: any) => (
          <SelectPackages
            {...props}
            resourceType={ResourceAssociateEnum.平台铺货资料}
            createRelatedAuthId="2110"
            accept={defaultAcceptImageSuffix}
            basicInfo={{
              id: '',
              code: distributionSpuCode,
              name: distributionSpuName,
            }}
            relatedPackage={resource}
            onRelatedPackage={(data) => {
              handleRelatedPackage(data);

              return true;
            }}
          />
        ),
      },
    },
    {
      formItemProps: {
        label: '3:4主图',
        className: styles.imageFormItem,
      },
      renderFormItem: () => (
        <Typography.Text type="secondary">
          图片支持上传png、jpeg、jpg格式；每张不超过3M，不超过10张；拖动可调整图片顺序；支持最小尺寸750*1000，固定宽高比例为3:4
        </Typography.Text>
      ),
    },
    {
      type: 'mediaUpload',
      formItemProps: {
        label: ' ',
        name: 'subImage',
        rules: imageValidatorRules,
      },
      fieldProps: {
        readonly: isView,
        maxCount: 10,
        maxSingleSize: 10,
        customUploadType: basicImageType,
        canCrop: true,
        checkAfterUpload: true,
        checkRules: {
          acceptSuffix: defaultAcceptImageSuffix,
          maxSize: 3,
          whRatio: ['3:4'],
        },
        extraElementRender: (props: any) => (
          <SelectPackages
            {...props}
            resourceType={ResourceAssociateEnum.平台铺货资料}
            createRelatedAuthId="2110"
            accept={defaultAcceptImageSuffix}
            basicInfo={{
              id: '',
              code: distributionSpuCode,
              name: distributionSpuName,
            }}
            relatedPackage={resource}
            onRelatedPackage={(data) => {
              handleRelatedPackage(data);

              return true;
            }}
          />
        ),
      },
    },
    {
      formItemProps: {
        label: '详情图',
        className: styles.imageFormItem,
      },
      renderFormItem: () => (
        <Typography.Text type="secondary">
          图片支持上传png、jpeg、jpg格式；每张不超过10M，不超过50张；建议宽度1000选品市场展示最佳；拖动可调整图片顺序
        </Typography.Text>
      ),
    },
    {
      type: 'mediaUpload',
      formItemProps: {
        label: ' ',
        name: 'detailImage',
        rules: imageValidatorRules,
      },
      fieldProps: {
        readonly: isView,
        maxCount: 50,
        maxSingleSize: 10,
        customUploadType: basicImageType,
        canCrop: true,
        checkAfterUpload: true,
        checkRules: {
          acceptSuffix: defaultAcceptImageSuffix,
          maxSize: 10,
        },
        extraElementRender: (props: any) => (
          <SelectPackages
            {...props}
            resourceType={ResourceAssociateEnum.平台铺货资料}
            createRelatedAuthId="2110"
            accept={defaultAcceptImageSuffix}
            basicInfo={{
              id: '',
              code: distributionSpuCode,
              name: distributionSpuName,
            }}
            relatedPackage={resource}
            onRelatedPackage={(data) => {
              handleRelatedPackage(data);

              return true;
            }}
          />
        ),
      },
    },
    {
      formItemProps: {
        label: '素材图',
        className: styles.imageFormItem,
      },
      renderFormItem: () => (
        <Typography.Text type="secondary">
          图片支持上传png、jpeg、jpg格式；每张不超过10M
        </Typography.Text>
      ),
    },
    {
      type: 'mediaUpload',
      formItemProps: {
        label: ' ',
        name: 'materialImage',
        rules: imageValidatorRules,
      },
      fieldProps: {
        readonly: isView,
        multiple: false,
        maxSingleSize: 10,
        customUploadType: basicImageType,
        canCrop: true,
        checkAfterUpload: true,
        checkRules: {
          acceptSuffix: defaultAcceptImageSuffix,
          maxSize: 10,
        },
        extraElementRender: (props: any) => (
          <SelectPackages
            {...props}
            resourceType={ResourceAssociateEnum.平台铺货资料}
            createRelatedAuthId="2110"
            accept={defaultAcceptImageSuffix}
            basicInfo={{
              id: '',
              code: distributionSpuCode,
              name: distributionSpuName,
            }}
            relatedPackage={resource}
            onRelatedPackage={(data) => {
              handleRelatedPackage(data);

              return true;
            }}
          />
        ),
      },
    },
    {
      formItemProps: {
        label: '视频',
        className: styles.imageFormItem,
      },
      renderFormItem: () => (
        <Typography.Text type="secondary">
          仅支持上传1段，仅支持mp4格式上传，大小100M内，比例支持1:1、3:4及9:16比例视频；时长&lt;=60s
        </Typography.Text>
      ),
    },
    {
      type: 'mediaUpload',
      formItemProps: {
        // label 占位
        label: ' ',
        name: 'mainVideo',
      },
      fieldProps: {
        readonly: isView,

        multiple: false,
        maxSingleSize: 100,
        customUploadType: {
          'video/mp4': ['.mp4'],
        },
        extraElementRender: (props: any) => (
          <SelectPackages
            {...props}
            resourceType={ResourceAssociateEnum.平台铺货资料}
            createRelatedAuthId="2110"
            accept={['.mp4']}
            basicInfo={{
              id: '',
              code: distributionSpuCode,
              name: distributionSpuName,
            }}
            relatedPackage={resource}
            onRelatedPackage={(data) => {
              handleRelatedPackage(data);

              return true;
            }}
          />
        ),
      },
    },
    {
      type: 'input',
      formItemProps: {
        label: '推荐文案',
        name: ['distributionSpuInformation', 'recommendedCopy'],
      },
      fieldProps: {
        showCount: true,
        maxLength: 100,
      },
    },
    {
      type: 'textarea',
      formItemProps: {
        label: '商品描述',
        name: ['distributionSpuInformation', 'productDescription'],
      },
      fieldProps: {
        showCount: true,
        maxLength: 5000,
      },
    },
    {
      formItemProps: {
        label: '规格明细',
        name: 'specificationDetails',
        required: true,
        wrapperCol: {
          flex: '1',
        },
        rules: [
          () => ({
            validator(_, value?: DistributionSku[]) {
              if (
                value?.every(
                  (item) =>
                    item.distributionSkuCode &&
                    item.distributionSkuName &&
                    !isNil(item.basicDistributionPrice),
                )
              ) {
                return Promise.resolve();
              }
              if (!value) {
                return Promise.reject(new Error('规格明细至少有一项'));
              }
              return Promise.reject(
                new Error('规格编码、规格名称、基础分销价均不能为空'),
              );
            },
          }),
        ],
      },
      renderFormItem: () => (
        <SpecificationDetails
          pricingPriceRef={pricingPrice}
          type={type}
          goodsId={goodsId}
          isView={isView}
          isDistributedGoods={isDistributedGoods}
          editableTableRef={editableTableRef}
          redistributionStatus={detail?.redistributionStatus}
        />
      ),
    },
    ...(isDistributedGoods
      ? []
      : ([
          {
            needFormItemWrap: false,
            renderFormItem: () => (
              <Flex>
                <Typography.Text
                  className="pr-sm"
                  style={{
                    width: LABEL_WIDTH,
                  }}
                >
                  同步到商品信息
                </Typography.Text>
                <Typography.Text>
                  1、保存时会将勾选的字段自动同步到商品信息
                  <br />
                  2、同步时按商品编码+规格编码匹配，匹配到则更新该商品及规格的信息；若未匹配到商品编码，则新增商品；若匹配到商品编码，未匹配到规格编码，则在该商品下新增该条规格信息
                </Typography.Text>
              </Flex>
            ),
          },
          {
            type: 'checkboxGroup',
            formItemProps: {
              label: ' ', // label 占位
              name: 'syncSetting',
            },
            fieldProps: {
              options: [
                ...(hasInfoCodeAuth
                  ? [
                      {
                        label: '商品编码',
                        value: 'syncGoodsNo',
                        disabled: !isEdit,
                      },
                    ]
                  : []),
                {
                  label: '商品名称',
                  value: 'syncGoodsName',
                  disabled: !isEdit,
                },
                {
                  label: '规格名称',
                  value: 'syncGoodsSkuName',
                  disabled: !isEdit,
                },
                {
                  label: '重量（kg）',
                  value: 'syncGoodsWeight',
                  disabled: true,
                },
                {
                  label: '最低售价',
                  value: 'syncLowerPrice',
                  disabled: true,
                },
                {
                  label: '标准售价',
                  value: 'syncStandPrice',
                  disabled: true,
                },
                {
                  label: '基础分销价',
                  value: 'syncBasicPrice',
                  disabled: true,
                },
                {
                  label: '1-5级分销价',
                  value: 'syncGradeBasicPrice',
                  disabled: true,
                },
                ...syncSettingGoodsFields,
              ],
            },
          },
        ] as FormItemConfigDTO[])),
  ];

  const onCancel = () => {
    closeActiveTab();
  };

  const onSave = useCallback(
    async (isOnline?: boolean) => {
      const editableTableValues =
        await editableTableRef.current?.validateFields();

      form
        .validateFields()
        .then(async (values) => {
          if (!editableTableValues) {
            return;
          }

          setSaveLoading(true);

          const data = formatSubmitData(values) as GoodsStoreInfoFormValues;
          const supplier = supplierRef.current;
          const distributionType = distributionTypeRef.current;
          const syncConfigParams: GoodsChangeConfigSyncSaveOrUpdateParams &
            CommonRecord = {
            id: syncConfigIdRef.current,
            enable: 1,
            type: GoodsInfoSyncConfigType['平台铺货资料'],
          };
          allSyncFields.forEach((field) => {
            syncConfigParams[field] = values.syncSetting?.includes(field);
          });
          if (isAdd) {
            syncConfigParams['syncGoodsName'] =
              originSyncConfigData.current?.['syncGoodsName'];
            syncConfigParams['syncGoodsSkuName'] =
              originSyncConfigData.current?.['syncGoodsSkuName'];

            const updateSyncConfigRes =
              await updateSyncConfig(syncConfigParams);
            if (updateSyncConfigRes?.success) {
              const saveRes = await save({
                ...data,
                goodsId,
                supplierId: supplier?.id,
                supplierName: supplier?.name,
                uniqueKey: uuidv4(),
                sourceConfig: JSON.stringify(originSyncConfigData.current),
                distributionType,
              });

              if (saveRes?.success && saveRes?.data) {
                message.success('新增成功');

                if (isOnline) {
                  await onlineGoodsModalRef.current?.startOnline(
                    saveRes?.data?.id,
                  );
                }

                closeAndRefreshPrevTab();
              }
            }
          } else {
            // 修改资料
            if (detail?.marketStatus === MarketStatusEnum.Shelves) {
              const result = await new Promise((resolve) => {
                return Modal.confirm({
                  title:
                    '该商品已经上架到【选品市场】和【分销商城】，确认保存编辑后的信息吗？',
                  content: (
                    <Typography.Text className="text-description">
                      商品保存后，该商品会重新上架
                    </Typography.Text>
                  ),
                  okText: '保存并上架',
                  onOk: () => {
                    resolve(true);
                  },
                  onCancel: () => {
                    resolve(false);
                  },
                });
              });

              if (!result) {
                setSaveLoading(false);
                return false;
              }
            }

            const updateSyncConfigRes =
              await updateSyncConfig(syncConfigParams);

            if (updateSyncConfigRes?.success) {
              const updateRes = await update({
                ...data,
                goodsId,
                supplierId: supplier?.id,
                supplierName: supplier?.name,
                id: id!,
                distributionType,
                sourceConfig: JSON.stringify(originSyncConfigData.current),
              });
              if (updateRes?.success) {
                if (updateRes?.data?.onlineErrorMessage) {
                  Modal.error({
                    title: '商品信息保存成功，但商品重新上架失败！',
                    content: (
                      <Typography.Text className="text-description">
                        {updateRes?.data?.onlineErrorMessage}
                      </Typography.Text>
                    ),
                    okText: '确定',
                    onOk: () => {
                      updateFormValueToLatest();
                    },
                  });
                  return;
                }

                message.success('修改成功');

                if (isOnline) {
                  await onlineGoodsModalRef.current?.startOnline(id!);
                }

                updateFormValueToLatest();
              }
            }
          }

          setSaveLoading(false);
        })
        .catch(() => {});
    },
    [
      allSyncFields,
      closeAndRefreshPrevTab,
      updateFormValueToLatest,
      detail?.marketStatus,
      form,
      goodsId,
      id,
      isAdd,
      save,
      update,
      updateSyncConfig,
    ],
  );

  return (
    <Flex vertical className="h-full w-full">
      <Flex flex={1} className="h-0 overflow-y-auto">
        <Form
          id="1730530206760"
          form={form}
          enableUnsavedWarning={!isView}
          disabled={isView}
          autoFocusField
          labelCol={{ flex: `${LABEL_WIDTH}px` }}
          className="!p-sm"
          initialValues={
            detail ? (formatInitialData(detail) as CommonRecord) : undefined
          }
        >
          <ConfigForm proportion={1} isShowAll list={list} />
        </Form>
      </Flex>
      <Condition visible={isView}>
        <Flex
          flex="none"
          justify="end"
          gap={8}
          className="bg-white px-base pb-sm pt-xs"
        >
          <Button size="large" onClick={onCancel}>
            关闭
          </Button>
        </Flex>
      </Condition>
      <Condition visible={!isView}>
        <Flex
          flex="none"
          justify="end"
          gap={8}
          className="bg-white px-base pb-sm pt-xs"
        >
          <Button size="large" onClick={onCancel}>
            取消
          </Button>
          <Button
            size="large"
            type="primary"
            loading={saveLoading}
            disabled={isEdit ? !detail : false}
            onClick={() => onSave(false)}
          >
            保存
          </Button>
          {detail?.marketStatus !== MarketStatusEnum.Shelves &&
          checkAuth('953') ? (
            <>
              <Button
                size="large"
                type="primary"
                loading={saveLoading}
                disabled={isEdit ? !detail : false}
                onClick={() => onSave(true)}
              >
                上架选品市场
              </Button>
              <OnlineGoodsModal trigger={null} ref={onlineGoodsModalRef} />
            </>
          ) : null}
        </Flex>
      </Condition>
    </Flex>
  );
};
