import { PlatformTypeEnum } from '@slt/utils';

class FixedSpuData {
  platformDistributionSpuId: string;
  platformPrice: number | undefined;
  fixedSkuDatas: { [key: string]: FixedSkuData };

  constructor(platformDistributionSpuId: number) {
    this.platformDistributionSpuId = platformDistributionSpuId.toString();
    this.fixedSkuDatas = {};
  }
}

class FixedSkuData {
  platformDistributionSkuId: string;
  price: number;
  extPrice: number | undefined;
  quantity: number;
  weight: number | undefined;

  constructor(
    platformDistributionSkuId: number,
    price: number,
    quantity: number,
    extPrice?: number,
    weight?: number,
  ) {
    this.platformDistributionSkuId = platformDistributionSkuId.toString();
    this.price = price;
    this.quantity = quantity;
    this.extPrice = extPrice;
    this.weight = weight;
  }
}

interface DistributionItem {
  id: string;
  platformType: number;
  shopId: string;
  distributeSource: number;
  targetId: string;
  sourceDistributionSpuId: string;
  outGoodsSpuId: string;
  platformDistributionId?: string;
  platformSpuId?: string;
}

export function convertToFixedSpuData(data: any[]): {
  [key: string]: FixedSpuData;
} {
  const fixedSpuDatas: { [key: string]: FixedSpuData } = {};

  data?.forEach((item) => {
    const spuId = item.goodsInfo.id;
    const skuId = item.id;

    let fixedSpuData = fixedSpuDatas[spuId];
    if (!fixedSpuData) {
      fixedSpuData = new FixedSpuData(spuId);
      if (item.platformPriceField !== undefined) {
        fixedSpuData.platformPrice = parseFloat(item.platformPriceField);
      }

      fixedSpuDatas[spuId] = fixedSpuData;
    }

    const weightValue =
      item.weight !== undefined ? parseFloat(item.weight) : undefined;

    const fixedSkuData = new FixedSkuData(
      item.id,
      parseFloat(item.price),
      parseInt(item.quantity || item.skuStock),
      item.platformSkuPriceField !== undefined
        ? parseFloat(item.platformSkuPriceField)
        : undefined,
      weightValue,
    );

    fixedSpuData.fixedSkuDatas[skuId] = fixedSkuData;
  });

  return fixedSpuDatas;
}

export function transformDistributionData(data: DistributionItem[]) {
  const result: { [key: number]: { [key: string]: string[] } } = {};

  data.forEach((item) => {
    const shopId = Number(item.shopId);
    const spuId = item.platformDistributionId;
    const outGoodsSpuId = item.platformSpuId;

    if (!result[shopId]) {
      result[shopId] = {};
    }

    if (!result[shopId][spuId]) {
      result[shopId][spuId] = [];
    }

    result[shopId][spuId].push(outGoodsSpuId);
  });

  return result;
}

export const groupByPlatformType = (data) => {
  const grouped = data.reduce((acc, item) => {
    const { platformType, shopId } = item;

    if (!acc[platformType]) {
      acc[platformType] = new Set();
    }

    acc[platformType].add(shopId);
    return acc;
  }, {});

  const result = {};
  for (const [platformType, shopIds] of Object.entries(grouped)) {
    result[platformType] = Array.from(shopIds);
  }

  return result;
};

// 用于处理多平台类型的数据结构
export function convertToMultiPlatformFixedSpuData(data: {
  [key: string]: any[];
}): {
  [platformKey: string]: {
    [key: string]: FixedSpuData;
  };
} {
  const result: {
    [platformKey: string]: {
      [key: string]: FixedSpuData;
    };
  } = {};

  Object.keys(data).forEach((platformKey) => {
    result[platformKey] = convertToFixedSpuData(data[platformKey]);
  });

  return result;
}
