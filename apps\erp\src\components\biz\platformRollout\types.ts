import type { ModalProps } from '@slt/base-ui';
import type { PlatformRolloutWayEnum } from '@slt/services';
import type { PlatformTypeEnum } from '@slt/utils';
import type { InventoryAndPriceDataItem } from './rolloutSettings/PriceAndInventoryModal';

export type PlatformRolloutProps = {
  maskArea: ModalProps['maskArea'];
  /** 铺货入口 */
  rolloutWay: PlatformRolloutWayEnum;
  /** 是否需要预先检查合作状态 */
  authCheck: boolean;
  /** 是否可铺货检查时，取的字段（默认`platformDistributionId`） */
  preCheckKey?: 'platformDistributionId' | 'actualGoodsSpuId';
  /** 开始铺货回调 */
  onRolloutStart?: () => void;
};

export type PlatformRolloutRef = {
  begin: (p: {
    targetIds: Array<string>;
    platform?: PlatformTypeEnum;
    isUpdateRollout?: boolean;
  }) => void;
};

export interface BasePlatformRecord {
  recordId: string;
  platformType: number;
  shopId: string;
  platformSpuId: string;
  platformSpuName: string;
  platformSpuCode: string;
  optUserId: string;
  optUserName: string;
  optTime: string;
  targetId: string;
  showId: string;
  showName: string;
  showCode: string;
  platformDistributionId: string;
  actualGoodsSpuId: string;
  url: string;
  distribution: boolean;
  id?: string;
  shopName?: string;
}

export interface ConfirmUpdateProduct extends BasePlatformRecord {
  platformLogoUrl: string;
}

export type UpdateRolloutRecordedList = BasePlatformRecord;

export type EditPriceSource = {
  [platformType: number]: Array<{ [id: string]: InventoryAndPriceDataItem[] }>;
};
