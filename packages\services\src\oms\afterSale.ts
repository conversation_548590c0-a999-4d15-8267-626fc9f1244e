import { type YTRequest } from '@slt/net';
import type {
  OmsAfterSaleMapListSelectInputVO,
  AfterSaleAttachmentDetailGroupVO,
  AfterSaleOrderDetailDTO,
  AfterSaleOrderStatisticsVO,
  AfterSaleOrderCreateUpdateDetailRequest,
  AfterSaleStrategyExecuteRecordQuery,
  AfterSaleStrategyExecuteRecordVO,
  AfterSaleStrategyExecuteRecordStatisticsVO,
  AfterSaleOrderTotalDataResponse,
  OmsAfterSalesStatisticsParams,
  AfterSaleSellerStrategyListVO,
  AfterSalesListQuery,
  AfterSaleStrategyAutoDetail,
  AfterSalesStrategyAddRequest,
  AfterSaleBillListQuery,
  UpdateAfterSaleRemarkAndAttachmentParam,
} from './afterSale.interface';
import type { AfterSalesBusinessType } from '@slt/utils';
import type { BatchOperationResultVO } from '../common.types';
import type { OmsMallOrderAsyncManualOrderDownloadParams } from '@slt/services';
import type { CommonRecord } from '@slt/base-utils';

/***订单枚举 */
export const omsAfterSalesBillOrderEnumQuery =
  (): YTRequest<OmsAfterSaleMapListSelectInputVO> => {
    return {
      url: '/oms/after/sale/order/enums/select/input',
      method: 'GET',
    };
  };

/***售后单创建 */
export const omsAfterSalesBillCreateEdit = (
  data?: Omit<AfterSaleOrderCreateUpdateDetailRequest, 'id'>,
): YTRequest<string> => {
  return {
    url: '/oms/after/sale/order/create',
    data,
  };
};

/***售后单列表 */
export const omsAfterSalesBillListQuery = (
  data?: AfterSaleBillListQuery,
): YTRequest<OmsAfterSaleMapListSelectInputVO> => {
  return {
    url: '/oms/after/sale/order/listPage',
    data,
  };
};
/***售后单列表，表尾合计数据 */
export const omsAfterSaleOrderTotalDataService = (
  data?: CommonRecord,
): YTRequest<AfterSaleOrderTotalDataResponse> => {
  return {
    url: '/oms/after/sale/order/total/data',
    data,
  };
};

/***售后单列表 */
export const omsAfterSalesBillListQueryByStatistics = (data?: {
  statisticsQueryType: number;
}): YTRequest<OmsAfterSaleMapListSelectInputVO> => {
  return {
    url: '/oms/after/sale/order/statistics/query',
    data,
  };
};
/***售后单列表，表尾合计数据 */
export const omsAfterSaleOrderStatisticsTotalDataService = (data?: {
  statisticsQueryType: number;
}): YTRequest<AfterSaleOrderTotalDataResponse> => {
  return {
    url: '/oms/after/sale/order/statistics/total/data',
    data,
  };
};

/***售后单关联商品行项 */
export const omsAfterSalesBillListDetailGoodsQuery = (data: {
  afterSaleOrderId: string;
}): YTRequest<AfterSaleOrderCreateUpdateDetailRequest[]> => {
  return {
    url: '/oms/after/sale/order/list/detail',
    data,
  };
};

/****售后单详情 */
export const omsAfterSalesBillListDetailQuery = (data: {
  afterSaleOrderId: string;
}): YTRequest<AfterSaleOrderDetailDTO> => {
  return {
    url: '/oms/after/sale/order/detail',
    data,
  };
};

/***手工下载订单 */
//TODO 参数晚上

export const omsAfterSalesBillManualDownload = (
  data?: OmsMallOrderAsyncManualOrderDownloadParams,
): YTRequest<{
  mallOrderTransfer: number;
}> => {
  return {
    url: '/oms/after/sale/order/async/manual/order/download',
    data,
  };
};

/***确认收货 */
export const omsAfterSalesBillConfirmReceiveGoods = (data: {
  afterSaleOrderIdList: string[];
}): YTRequest<BatchOperationResultVO> => {
  return {
    url: '/oms/after/sale/order/confirm/receipt',
    data,
  };
};

/***取消收货 */
export const omsAfterSalesBillCancelReceiveGoods = (data: {
  afterSaleOrderIdList: string[];
}): YTRequest<BatchOperationResultVO> => {
  return {
    url: '/oms/after/sale/order/cancel/receipt',
    data,
  };
};

/***审核 */
export const omsAfterSalesBillAudit = (data: {
  afterSaleOrderIdList: string[];
}): YTRequest<BatchOperationResultVO> => {
  return {
    url: '/oms/after/sale/order/confirm',
    data,
  };
};

/***反审核 */
export const omsAfterSalesBillReverseAudit = (data: {
  afterSaleOrderIdList: string[];
}): YTRequest<BatchOperationResultVO> => {
  return {
    url: '/oms/after/sale/order/anti/confirm',
    data,
  };
};
/***取消异常 */
export const omsAfterSalesBillCancelException = (data: {
  afterSaleOrderIdList: string[];
}): YTRequest<BatchOperationResultVO> => {
  return {
    url: '/oms/after/sale/order/cancel/exception',
    data,
  };
};

/***取消 */
export const omsAfterSalesCancel = (data: {
  afterSaleOrderIdList: string[];
}): YTRequest<BatchOperationResultVO> => {
  return {
    url: '/oms/after/sale/order/cancel',
    data,
  };
};

/***反取消 */
export const omsAfterSalesReverseCancel = (data: {
  afterSaleOrderIdList: string[];
}): YTRequest<BatchOperationResultVO> => {
  return {
    url: '/oms/after/sale/order/anti/cancel',
    data,
  };
};

/***推送云仓 */
export const omsAfterSalesPushCloudWarehouse = (data: {
  afterSaleOrderIdList: string[];
}): YTRequest<BatchOperationResultVO> => {
  return {
    url: '/oms/after/sale/order/push/cloud/warehouse',
    data,
  };
};

/***提交供应商 */
export const omsAfterSalesSubmitSupplier = (data: {
  afterSaleOrderIdList: string[];
}): YTRequest<BatchOperationResultVO> => {
  return {
    url: '/oms/after/sale/order/submit/supplier',
    data,
  };
};

/***撤回供销售后 */
export const omsAfterSalesReverseSupplier = (data: {
  afterSaleOrderIdList: string[];
}): YTRequest<BatchOperationResultVO> => {
  return {
    url: '/oms/after/sale/order/revoke/submit/supplier',
    data,
  };
};

/**售后单导入 */
export const omsAfterOrderImport = (data: {
  fileName?: string;
  fileUrl?: string;
  fileSize?: number;
}): YTRequest<any> => {
  return {
    url: `/oms/after/sale/order/import`,
    data,
  };
};

/***重新生成退货单 */
export const omsAfterSalesReGenBill = (data: {
  afterSaleOrderIdList: string[];
}): YTRequest<BatchOperationResultVO> => {
  return {
    url: '/oms/after/sale/order/gen/bill',
    data,
  };
};
/***确认退款 */
export const omsAfterSalesConfirmRefund = (data: {
  afterSaleOrderIdList: string[];
  refundPayAccountId: string;
  refundRemark: string;
  refundSettlementType?: string;
}): YTRequest<BatchOperationResultVO> => {
  return {
    url: '/oms/after/sale/order/confirm/refund',
    data,
  };
};
/***统计 */
export const omsAfterSalesStatistics = (
  data: OmsAfterSalesStatisticsParams,
): YTRequest<AfterSaleOrderStatisticsVO> => {
  return {
    url: '/oms/after/sale/order/statistics',
    data,
  };
};
/***修改退货物流信息 */
export const omsAfterSalesUpdateLogistics = (data: {
  afterSaleOrderIdList: string[];
  returnLogisticsName?: string;
  returnLogisticsNo?: string;
}): YTRequest<any> => {
  return {
    url: '/oms/after/sale/order/update/return/logistics',
    data,
  };
};
/***修改售后备注 */
export const omsAfterSalesUpdateRemark = (data: {
  afterSaleOrderIdList: string[];
  remark?: string;
}): YTRequest<any> => {
  return {
    url: '/oms/after/sale/order/update/remark',
    data,
  };
};
/***修改售后原因 */
export const omsAfterSalesUpdateReason = (data: {
  afterSaleOrderIdList: string[];
  refundReason?: string;
}): YTRequest<any> => {
  return {
    url: '/oms/after/sale/order/update/refund/reason',
    data,
  };
};
/***修改退货仓库 */
export const omsAfterSalesUpdateWarehouse = (data: {
  afterSaleOrderIdList: string[];
  refundWarehouseId?: string;
}): YTRequest<any> => {
  return {
    url: '/oms/after/sale/order/update/return/warehouse',
    data,
  };
};
/***详情页-修改退运费金额 */
export const omsAfterSalesUpdateDetailPostAmount = (data: {
  afterSaleOrderIdList: string[];
  amount?: number;
}): YTRequest<any> => {
  return {
    url: '/oms/after/sale/order/update/post/amount',
    data,
  };
};
/***详情页-移除行项 */
export const omsAfterSalesDetailGoodsRemove = (data: {
  afterSaleOrderDetailIdList?: string[];
  afterSaleOrderIdList?: string[];
}): YTRequest<any> => {
  return {
    url: '/oms/after/sale/order/remove/item',
    data,
  };
};
/***详情页-新增行项 */
export const omsAfterSalesDetailGoodsAdd = (data: {
  afterSaleOrderIdList?: string[];
  saleOrderDetailRequestList?: AfterSaleOrderCreateUpdateDetailRequest[];
}): YTRequest<any> => {
  return {
    url: '/oms/after/sale/order/add/item',
    data,
  };
};
/*** 修改明细实收数量*/

export const omsAfterSalesDetailGoodsReceiveNum = (data: {
  afterSaleOrderIdList?: string[];
  afterSaleOrderDetailIdList?: string[];
  receiveRealNum?: number;
  amount?: number;
}): YTRequest<any> => {
  return {
    url: '/oms/after/sale/order/update/receive/real/num',
    data,
  };
};
/***修改明细金额*/
export const omsAfterSalesDetailGoodsDetail = (data: {
  afterSaleOrderIdList: string[];
  afterSaleOrderDetailIdList?: Array<string | undefined>;
  amount: number | string;
}): YTRequest<BatchOperationResultVO> => {
  return {
    url: '/oms/after/sale/order/update/after/detail/amount',
    data,
  };
};
/***修改售后类型 */
export const omsAfterSalesUpdateType = (data: {
  afterSaleOrderIdList?: string[];
  saleOrderDetailRequestList?: AfterSalesBusinessType;
}): YTRequest<any> => {
  return {
    url: '/oms/after/sale/order/update/after/sale/type',
    data,
  };
};

/***售后策略列表 */
export const omsAfterSaleStrategyList = (
  data?: AfterSalesListQuery,
): YTRequest<AfterSaleSellerStrategyListVO> => {
  return {
    url: '/oms/tenant/strategy/page',
    data,
  };
};
/***策略详情查询 */
export const omsAfterSaleStrategyDetail = (data: {
  id: string;
}): YTRequest<AfterSaleStrategyAutoDetail> => {
  return {
    url: '/oms/tenant/strategy/get',
    data,
  };
};

/***策略启动/禁用 */
export const omsAfterSaleStrategyEnable = (data: {
  id: string;
  status: boolean;
}): YTRequest<any> => {
  return {
    url: '/oms/tenant/strategy/enable',
    data,
  };
};
/***策略删除 */

export const omsAfterSaleStrategyDelete = (data: {
  id: string;
}): YTRequest<AfterSaleStrategyExecuteRecordStatisticsVO> => {
  return {
    url: `/oms/tenant/strategy/delete/${data.id}`,
    method: 'get',
    data,
  };
};
/***策略验证 */
export const omsAfterSaleStrategyVerify = (data: {
  id: string;
  afterSaleOrderId: string;
}): YTRequest<any> => {
  return {
    url: `/oms/tenant/strategy/verify`,
    data,
  };
};
/***策略新增 */
export const omsAfterSaleStrategyAdd = (
  data: AfterSalesStrategyAddRequest,
): YTRequest<any> => {
  return {
    url: '/oms/tenant/strategy/create',
    data,
  };
};
/***策略编辑 */
export const omsAfterSaleStrategyEdit = (
  data: AfterSalesStrategyAddRequest,
): YTRequest<any> => {
  return {
    url: '/oms/tenant/strategy/update',
    data,
  };
};

/***策略执行监控-统计数据 */
export const omsAfterSaleStrategyMonitor = (
  data: AfterSaleStrategyExecuteRecordQuery,
): YTRequest<any> => {
  return {
    url: '/oms/after/sale/strategy/execute/record/statistics/query',
    data,
  };
};

/***策略执行监控-订单列表 */
export const omsAfterSaleStrategyMonitorOrderList = (
  data?: AfterSaleStrategyExecuteRecordQuery,
): YTRequest<AfterSaleStrategyExecuteRecordVO> => {
  return {
    url: '/oms/after/sale/strategy/execute/record/listPage',
    data,
  };
};

/***策略名称下拉 */
export const omsAfterSaleStrategyNameSelect = (): YTRequest<any> => {
  return {
    url: '/oms/tenant/strategy/select/input',
  };
};

/***策略已选中条件 */
export const omsAfterSaleStrategySelectedCondition = (data: {
  bizType: string;
  bizSubType: string;
  conditionCode: string;
}): YTRequest<string[]> => {
  return {
    url: `/oms/tenant/strategy/select/condition/${data.bizType}/${data.bizSubType}/${data.conditionCode}`,
    data,
  };
};

/***新增售后原因 */
export const omsAfterSaleStrategyAddRefundReason = (data: {
  refundReasonName: string;
}): YTRequest<any> => {
  return {
    url: '/oms/order/refund/reason/create',
    data,
  };
};

/***售后原因列表 */
export const omsAfterSaleStrategyRefundReasonList = (): YTRequest<any> => {
  return {
    url: '/oms/order/refund/reason/select/input',
  };
};

/***同步奇门收货失败重试*/
export const omsAfterSalesSyncQimenFailRetry = (data: {
  afterSaleOrderIdList: string[];
}): YTRequest<any> => {
  return {
    url: '/oms/after/sale/order/retry/qimen/sync',
    data,
  };
};

/***附件/备注获取 */
export const omsAfterSaleRemarkAndAttachmentDetail = (data: {
  afterSaleOrderIdList?: string[];
  attachmentRemarkType?: number;
}): YTRequest<AfterSaleAttachmentDetailGroupVO> => {
  return {
    url: '/oms/after/sale/order/get/attachment/and/remark/detail',
    data,
  };
};

/***售后附件备注更新 */
export const omsUpdateAfterSaleRemarkAndAttachment = (
  data?: UpdateAfterSaleRemarkAndAttachmentParam,
): YTRequest<AfterSaleAttachmentDetailGroupVO> => {
  return {
    url: '/oms/after/sale/order/update/attachment/and/remark',
    data,
  };
};

/***检查是否可以添加供分售后附件备注 */
export const omsCheckCanAddSupplierDistributionAttachment = (data?: {
  afterSaleOrderIdList?: string[];
  saleOrderId?: string;
}): YTRequest<boolean> => {
  return {
    url: '/oms/after/sale/order/check/online',
    data,
  };
};
