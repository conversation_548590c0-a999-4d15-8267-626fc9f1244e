import type { UploadFile } from '@slt/upload-core';
import type {
  AfterSaleViewModeEnum,
  UpdateAfterSaleRemarkAndAttachmentParam,
} from '@slt/services';

/**
 * AfterSaleAttachmentDetailGroupVO
 */
export interface AfterSaleAttachmentDetail {
  /**
   * 我的附件/备注
   */
  afterSaleAttachment?: AfterSaleAttachmentDetailItem;
  /**
   * 供分附件/备注
   */
  supplierDistributionAttachment?: AfterSaleAttachmentDetailItem[];
  [property: string]: any;
}

/**
 * 我的附件/备注
 */
export interface AfterSaleAttachmentDetailItem {
  /**
   * 修改时间
   */
  attachmentUpdateTime?: string;
  /**
   * 附件
   */
  attachmentUrlList?: string[];
  /**
   * 备注
   */
  remark?: string;
  /**
   * 租户id
   */
  tenantId?: string;
  /**
   * 租户名称
   */
  tenantName?: string;
  [property: string]: any;
}

export interface UploadRemarkAndAttachmentFormProps {
  remark: string;
  attachmentList: UploadFile[] | null;
}

export type RemarkAndAttachmentProps = {
  saleOrderId?: string;
  afterSaleOrderId?: string;
  value?: {
    /**
     * 我的附件/备注
     */
    afterSaleAttachment?: AfterSaleAttachmentDetailItem;
    /**
     * 供分附件/备注
     */
    supplierDistributionAttachment?: AfterSaleAttachmentDetailItem[];
  };
  viewMode?: AfterSaleViewModeEnum;
  onOk?: (v: Partial<UpdateAfterSaleRemarkAndAttachmentParam>) => void;
  hasAuth?: boolean;
  reload?: () => void;
  afterOpen?: () => boolean;
};
